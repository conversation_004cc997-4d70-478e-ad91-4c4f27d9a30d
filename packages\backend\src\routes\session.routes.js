const express = require('express');
const router = express.Router();
const sessionController = require('../controllers/session.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateSession, validateUpdateSession } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para obtener sesiones
router.get('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionController.getAllSessions);
router.get('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionController.getSessionById);

// Rutas para crear y actualizar sesiones
router.post('/', checkRole(['GLO<PERSON>L_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), validateCreateSession, sessionController.createSession);
router.put('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), validateUpdateSession, sessionController.updateSession);

// Rutas para cambiar estado de sesiones
router.patch('/:id/confirm', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionController.confirmSession);
router.patch('/:id/cancel', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionController.cancelSession);
router.patch('/:id/complete', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), sessionController.completeSession);

module.exports = router;
