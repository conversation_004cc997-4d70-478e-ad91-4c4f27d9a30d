const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const authController = require('../controllers/auth.controller');
const { verifyToken } = require('../middlewares/auth.middleware');
const EmailService = require('../services/email.service');
const { User, Role } = require('../models');
const {
  validateRegister,
  validateLogin,
  validateForgotPassword,
  validateResetPassword
} = require('../middlewares/validation.middleware');

// Rutas públicas
router.post('/register', validateRegister, authController.register);
router.post('/login', validateLogin, authController.login);
router.post('/refresh-token', authController.refreshToken);
router.post('/logout', verifyToken, authController.logout);
router.post('/forgot', validateForgotPassword, authController.forgotPassword);
router.post('/reset/:token', validateResetPassword, authController.resetPassword);

// Ruta para probar SMTP
router.get('/test-smtp', async (req, res) => {
  try {
    const result = await EmailService.testSMTPConnection();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error al probar la conexión SMTP',
      error: error.message
    });
  }
});

// Rutas protegidas
router.get('/profile', verifyToken, authController.getProfile);

// Ruta de depuración para verificar el token y el rol
router.get('/debug-token', verifyToken, (req, res) => {
  res.json({
    success: true,
    message: 'Token válido',
    user: req.user,
    decodedToken: jwt.decode(req.headers.authorization.split(' ')[1])
  });
});

// Ruta de depuración para verificar el rol del usuario en la base de datos
router.get('/debug-role/:email', verifyToken, async (req, res) => {
  try {
    const { email } = req.params;

    // Buscar usuario por email
    const user = await User.findOne({
      where: { email },
      include: [{ model: Role }]
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        roleId: user.roleId,
        role: user.Role ? user.Role.name : null,
        isActive: user.isActive,
      },
      role: user.Role ? {
        id: user.Role.id,
        name: user.Role.name,
        description: user.Role.description
      } : null
    });
  } catch (error) {
    console.error('Error al verificar rol:', error);
    res.status(500).json({
      success: false,
      message: 'Error al verificar rol',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
