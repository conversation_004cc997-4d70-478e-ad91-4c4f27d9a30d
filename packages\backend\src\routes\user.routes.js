const express = require('express');
const router = express.Router();
const multer = require('multer');
const userController = require('../controllers/user.controller');
const { verifyToken, checkRole, isSelfOrAdmin } = require('../middlewares/auth.middleware');
const { validateCreateUser, validateUpdateUser, validateChangePassword } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para administradores
router.get('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), userController.getAllUsers);
router.post('/', checkRole(['GLOBAL_ADMIN']), validateCreateUser, userController.createUser);
router.get('/roles', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), userController.getAllRoles);
router.get('/by-role/:role', checkRole(['GLO<PERSON>L_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), userController.getUsersByRole);

// Rutas para usuarios específicos (propio usuario o administrador)
router.get('/:id', isSelfOrAdmin, userController.getUserById);
router.put('/:id', isSelfOrAdmin, validateUpdateUser, userController.updateUser);
router.put('/:id/change-password', isSelfOrAdmin, validateChangePassword, userController.changePassword);

// Rutas solo para administradores
router.put('/:id/activate', checkRole(['GLOBAL_ADMIN']), userController.activateUser);
router.put('/:id/deactivate', checkRole(['GLOBAL_ADMIN']), userController.deactivateUser);

// Configure multer for memory storage
const upload = multer({ storage: multer.memoryStorage() });

// CSV Import Route
router.post(
  '/csv-import',
  verifyToken,
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']),
  upload.single('csvfile'),
  userController.importUsersFromCSV
);

module.exports = router;
