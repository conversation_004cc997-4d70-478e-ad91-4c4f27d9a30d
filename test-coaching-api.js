/**
 * Script de pruebas para los nuevos endpoints de Coaching y Mentoría
 * Sprint 6 - Sistema completo de coaching
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(70));
  log(`${title}`, 'bold');
  console.log('='.repeat(70));
}

function logTest(testName, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const color = passed ? 'green' : 'red';
  log(`${status} ${testName}`, color);
  if (details) {
    log(`   ${details}`, 'yellow');
  }
}

// Variables globales
let authToken = null;
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

function recordTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
  testResults.tests.push({ name, passed, details });
  logTest(name, passed, details);
}

// Obtener token de autenticación
async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    return response.data.token;
  } catch (error) {
    log(`Error al obtener token: ${error.message}`, 'red');
    return null;
  }
}

// Probar endpoints de sesiones
async function testSessionEndpoints() {
  logSection('🎯 PRUEBAS DE SESIONES DE COACHING');
  
  const headers = { Authorization: `Bearer ${authToken}` };
  
  try {
    // 1. Listar sesiones
    const listResponse = await axios.get(`${API_BASE_URL}/sessions`, { headers });
    recordTest('Listar sesiones', listResponse.status === 200, `Encontradas: ${listResponse.data.sessions?.length || 0}`);
    
    // 2. Crear sesión de prueba
    const newSession = {
      mentorId: 1, // Admin como mentor
      entrepreneurId: 2, // Otro usuario como emprendedor
      acceleratorId: 1,
      title: 'Sesión de prueba - Coaching API',
      description: 'Sesión creada para probar la API de coaching',
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Mañana
      endTime: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(), // Mañana + 1 hora
      location: 'Oficina virtual',
      videoMeetingLink: 'https://meet.google.com/test-session'
    };
    
    const createResponse = await axios.post(`${API_BASE_URL}/sessions`, newSession, { headers });
    const createdSession = createResponse.data.session;
    recordTest('Crear sesión', createResponse.status === 201, `ID: ${createdSession?.id}`);
    
    if (createdSession?.id) {
      // 3. Obtener detalles de la sesión
      const detailsResponse = await axios.get(`${API_BASE_URL}/sessions/${createdSession.id}`, { headers });
      recordTest('Obtener detalles de sesión', detailsResponse.status === 200, `Título: ${detailsResponse.data.session?.title}`);
      
      // 4. Actualizar sesión
      const updateData = { title: 'Sesión actualizada - API Test' };
      const updateResponse = await axios.put(`${API_BASE_URL}/sessions/${createdSession.id}`, updateData, { headers });
      recordTest('Actualizar sesión', updateResponse.status === 200, 'Título actualizado');
      
      // 5. Confirmar sesión
      const confirmResponse = await axios.patch(`${API_BASE_URL}/sessions/${createdSession.id}/confirm`, {}, { headers });
      recordTest('Confirmar sesión', confirmResponse.status === 200, 'Estado: scheduled');
      
      // 6. Completar sesión
      const completeResponse = await axios.patch(`${API_BASE_URL}/sessions/${createdSession.id}/complete`, {}, { headers });
      recordTest('Completar sesión', completeResponse.status === 200, 'Estado: completed');
      
      return createdSession.id;
    }
    
  } catch (error) {
    recordTest('Endpoints de sesiones', false, `Error: ${error.response?.data?.message || error.message}`);
  }
  
  return null;
}

// Probar endpoints de notas de sesión
async function testSessionNoteEndpoints(sessionId) {
  logSection('📝 PRUEBAS DE NOTAS DE SESIÓN');
  
  const headers = { Authorization: `Bearer ${authToken}` };
  
  try {
    // 1. Crear nota de sesión
    const newNote = {
      sessionId: sessionId,
      content: 'Esta es una nota de prueba para la sesión de coaching. El emprendedor mostró gran progreso en su pitch.'
    };
    
    const createResponse = await axios.post(`${API_BASE_URL}/session-notes`, newNote, { headers });
    const createdNote = createResponse.data.note;
    recordTest('Crear nota de sesión', createResponse.status === 201, `ID: ${createdNote?.id}`);
    
    // 2. Listar notas de la sesión
    const listResponse = await axios.get(`${API_BASE_URL}/session-notes/session/${sessionId}`, { headers });
    recordTest('Listar notas de sesión', listResponse.status === 200, `Encontradas: ${listResponse.data.notes?.length || 0}`);
    
    if (createdNote?.id) {
      // 3. Obtener detalles de la nota
      const detailsResponse = await axios.get(`${API_BASE_URL}/session-notes/${createdNote.id}`, { headers });
      recordTest('Obtener detalles de nota', detailsResponse.status === 200, 'Nota encontrada');
      
      // 4. Actualizar nota
      const updateData = { content: 'Nota actualizada: Excelente progreso del emprendedor.' };
      const updateResponse = await axios.put(`${API_BASE_URL}/session-notes/${createdNote.id}`, updateData, { headers });
      recordTest('Actualizar nota', updateResponse.status === 200, 'Contenido actualizado');
      
      return createdNote.id;
    }
    
  } catch (error) {
    recordTest('Endpoints de notas', false, `Error: ${error.response?.data?.message || error.message}`);
  }
  
  return null;
}

// Probar endpoints de tareas
async function testTaskEndpoints(sessionId) {
  logSection('📋 PRUEBAS DE TAREAS');
  
  const headers = { Authorization: `Bearer ${authToken}` };
  
  try {
    // 1. Listar tareas
    const listResponse = await axios.get(`${API_BASE_URL}/tasks`, { headers });
    recordTest('Listar tareas', listResponse.status === 200, `Encontradas: ${listResponse.data.tasks?.length || 0}`);
    
    // 2. Crear tarea
    const newTask = {
      title: 'Preparar presentación de pitch',
      description: 'Crear una presentación de 5 minutos para la próxima sesión de coaching',
      assignedToId: 2, // Asignar a otro usuario
      sessionId: sessionId,
      acceleratorId: 1,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // En una semana
      status: 'pending'
    };
    
    const createResponse = await axios.post(`${API_BASE_URL}/tasks`, newTask, { headers });
    const createdTask = createResponse.data.task;
    recordTest('Crear tarea', createResponse.status === 201, `ID: ${createdTask?.id}`);
    
    if (createdTask?.id) {
      // 3. Obtener detalles de la tarea
      const detailsResponse = await axios.get(`${API_BASE_URL}/tasks/${createdTask.id}`, { headers });
      recordTest('Obtener detalles de tarea', detailsResponse.status === 200, `Título: ${detailsResponse.data.task?.title}`);
      
      // 4. Actualizar tarea
      const updateData = { status: 'in_progress' };
      const updateResponse = await axios.put(`${API_BASE_URL}/tasks/${createdTask.id}`, updateData, { headers });
      recordTest('Actualizar tarea', updateResponse.status === 200, 'Estado: in_progress');
      
      // 5. Completar tarea
      const completeResponse = await axios.patch(`${API_BASE_URL}/tasks/${createdTask.id}/complete`, {}, { headers });
      recordTest('Completar tarea', completeResponse.status === 200, 'Estado: completed');
      
      // 6. Obtener estadísticas de tareas
      const statsResponse = await axios.get(`${API_BASE_URL}/tasks/stats`, { headers });
      recordTest('Obtener estadísticas de tareas', statsResponse.status === 200, 'Estadísticas obtenidas');
      
      return createdTask.id;
    }
    
  } catch (error) {
    recordTest('Endpoints de tareas', false, `Error: ${error.response?.data?.message || error.message}`);
  }
  
  return null;
}

// Función principal
async function runCoachingTests() {
  console.clear();
  logSection('🚀 PRUEBAS DEL SISTEMA DE COACHING Y MENTORÍA - SPRINT 6');
  log('Verificando los nuevos endpoints implementados...', 'blue');
  
  // Obtener token de autenticación
  authToken = await getAuthToken();
  if (!authToken) {
    log('No se pudo obtener token de autenticación. Abortando pruebas.', 'red');
    return;
  }
  
  log('✅ Token de autenticación obtenido', 'green');
  
  // Ejecutar pruebas
  const sessionId = await testSessionEndpoints();
  
  if (sessionId) {
    await testSessionNoteEndpoints(sessionId);
    await testTaskEndpoints(sessionId);
  }
  
  // Generar reporte final
  const endTime = Date.now();
  
  logSection('📊 REPORTE FINAL DE PRUEBAS');
  log(`Total de pruebas: ${testResults.total}`, 'blue');
  log(`Pruebas exitosas: ${testResults.passed}`, 'green');
  log(`Pruebas fallidas: ${testResults.failed}`, 'red');
  
  const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  log(`Tasa de éxito: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  // Mostrar pruebas fallidas si las hay
  if (testResults.failed > 0) {
    logSection('❌ PRUEBAS FALLIDAS');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => {
        log(`• ${test.name}: ${test.details}`, 'red');
      });
  }
  
  logSection('✅ VERIFICACIÓN COMPLETADA');
  
  if (successRate >= 90) {
    log('🎉 ¡Sistema de coaching completamente funcional!', 'green');
  } else if (successRate >= 70) {
    log('⚠️  Sistema de coaching mayormente funcional con algunos problemas menores.', 'yellow');
  } else {
    log('🚨 Sistema de coaching requiere atención antes de continuar.', 'red');
  }
}

// Ejecutar las pruebas
if (require.main === module) {
  runCoachingTests().catch(error => {
    log(`Error fatal en las pruebas: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runCoachingTests };
