import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FiCalendar,
  FiClock,
  FiUsers,
  FiMapPin,
  FiVideo,
  FiEdit,
  FiTrash2,
  FiCheck,
  FiX,
  FiArrowLeft,
  FiFileText,
  FiPlus,
  FiExternalLink
} from 'react-icons/fi';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import Alert from '../../components/common/Alert';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import sessionService from '../../services/sessionService';
import sessionNoteService from '../../services/sessionNoteService';
import taskService from '../../services/taskService';
import useAuth from '../../hooks/useAuth';

const SessionDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);
  const [notes, setNotes] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Estados para modales
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);
  const [showNoteModal, setShowNoteModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);

  // Estados para nueva nota
  const [newNote, setNewNote] = useState('');
  const [savingNote, setSavingNote] = useState(false);

  // Estados para nueva tarea
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    assignedToId: '',
    dueDate: ''
  });
  const [savingTask, setSavingTask] = useState(false);

  useEffect(() => {
    if (id) {
      loadSessionDetails();
    }
  }, [id]);

  const loadSessionDetails = async () => {
    try {
      setLoading(true);

      // Cargar datos en paralelo
      const [sessionRes, notesRes, tasksRes] = await Promise.all([
        sessionService.getSessionById(id),
        sessionNoteService.getSessionNotes(id),
        taskService.getAllTasks({ sessionId: id })
      ]);

      setSession(sessionRes.session);
      setNotes(notesRes.notes || []);
      setTasks(tasksRes.tasks || []);
    } catch (error) {
      console.error('Error al cargar detalles de sesión:', error);
      setError('Error al cargar los detalles de la sesión');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending_confirmation: { color: 'bg-yellow-100 text-yellow-800', text: 'Pendiente Confirmación' },
      scheduled: { color: 'bg-blue-100 text-blue-800', text: 'Programada' },
      completed: { color: 'bg-green-100 text-green-800', text: 'Completada' },
      cancelled_mentor: { color: 'bg-red-100 text-red-800', text: 'Cancelada por Mentor' },
      cancelled_entrepreneur: { color: 'bg-red-100 text-red-800', text: 'Cancelada por Emprendedor' }
    };

    const config = statusConfig[status] || { color: 'bg-gray-100 text-gray-800', text: status };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const canUserModifySession = () => {
    if (!session || !user) return false;

    return (
      user.role === 'GLOBAL_ADMIN' ||
      user.role === 'ACCELERATOR_ADMIN' ||
      session.mentorId === user.id ||
      (session.entrepreneurId === user.id && ['pending_confirmation', 'scheduled'].includes(session.status))
    );
  };

  const handleConfirmSession = async () => {
    try {
      await sessionService.confirmSession(id);
      setSuccess('Sesión confirmada correctamente');
      loadSessionDetails();
    } catch (error) {
      setError('Error al confirmar la sesión');
    }
  };

  const handleCompleteSession = async () => {
    try {
      await sessionService.completeSession(id);
      setSuccess('Sesión marcada como completada');
      loadSessionDetails();
    } catch (error) {
      setError('Error al completar la sesión');
    }
  };

  const handleCancelSession = async () => {
    try {
      await sessionService.cancelSession(id);
      setSuccess('Sesión cancelada correctamente');
      loadSessionDetails();
    } catch (error) {
      setError('Error al cancelar la sesión');
    }
  };

  const handleActionConfirm = (action) => {
    setConfirmAction(action);
    setShowConfirmDialog(true);
  };

  const executeAction = async () => {
    setShowConfirmDialog(false);

    switch (confirmAction) {
      case 'confirm':
        await handleConfirmSession();
        break;
      case 'complete':
        await handleCompleteSession();
        break;
      case 'cancel':
        await handleCancelSession();
        break;
      default:
        break;
    }

    setConfirmAction(null);
  };

  const handleSaveNote = async () => {
    if (!newNote.trim()) return;

    try {
      setSavingNote(true);
      await sessionNoteService.createNote({
        sessionId: parseInt(id),
        content: newNote.trim()
      });

      setNewNote('');
      setShowNoteModal(false);
      setSuccess('Nota agregada correctamente');
      loadSessionDetails();
    } catch (error) {
      setError('Error al guardar la nota');
    } finally {
      setSavingNote(false);
    }
  };

  const handleSaveTask = async () => {
    if (!newTask.title.trim()) return;

    try {
      setSavingTask(true);
      await taskService.createTask({
        ...newTask,
        sessionId: parseInt(id),
        assignedToId: parseInt(newTask.assignedToId),
        acceleratorId: session.acceleratorId
      });

      setNewTask({ title: '', description: '', assignedToId: '', dueDate: '' });
      setShowTaskModal(false);
      setSuccess('Tarea creada correctamente');
      loadSessionDetails();
    } catch (error) {
      setError('Error al crear la tarea');
    } finally {
      setSavingTask(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!session) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900">Sesión no encontrada</h2>
        <p className="mt-2 text-gray-600">La sesión que buscas no existe o no tienes permisos para verla.</p>
        <Button
          onClick={() => navigate('/admin/sessions')}
          className="mt-4"
        >
          Volver a Sesiones
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <button
          onClick={() => navigate('/admin/sessions')}
          className="flex items-center hover:text-blue-600 transition-colors"
        >
          <FiArrowLeft className="mr-1" />
          Sesiones
        </button>
        <span>/</span>
        <span className="text-gray-900">Detalles de Sesión</span>
      </div>

      {/* Alertas */}
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} />
      )}
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} />
      )}

      {/* Información principal de la sesión */}
      <Card>
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{session.title}</h1>
            <div className="mt-2 flex items-center space-x-4">
              {getStatusBadge(session.status)}
              <span className="text-sm text-gray-500">
                ID: {session.id}
              </span>
            </div>
          </div>

          {canUserModifySession() && (
            <div className="flex space-x-2">
              {session.status === 'pending_confirmation' && (
                <Button
                  variant="success"
                  size="sm"
                  onClick={() => handleActionConfirm('confirm')}
                  className="flex items-center"
                >
                  <FiCheck className="mr-1" />
                  Confirmar
                </Button>
              )}

              {session.status === 'scheduled' && (
                <Button
                  variant="success"
                  size="sm"
                  onClick={() => handleActionConfirm('complete')}
                  className="flex items-center"
                >
                  <FiCheck className="mr-1" />
                  Completar
                </Button>
              )}

              {['pending_confirmation', 'scheduled'].includes(session.status) && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/admin/sessions/${id}/edit`)}
                    className="flex items-center"
                  >
                    <FiEdit className="mr-1" />
                    Editar
                  </Button>

                  <Button
                    variant="danger"
                    size="sm"
                    onClick={() => handleActionConfirm('cancel')}
                    className="flex items-center"
                  >
                    <FiX className="mr-1" />
                    Cancelar
                  </Button>
                </>
              )}
            </div>
          )}
        </div>

        {/* Detalles de la sesión */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Información temporal */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <FiCalendar className="mr-2" />
              Información Temporal
            </h3>

            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <span className="font-medium text-gray-700 w-20">Fecha:</span>
                <span className="text-gray-900">{formatDate(session.startTime)}</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="font-medium text-gray-700 w-20">Hora:</span>
                <span className="text-gray-900">
                  {formatTime(session.startTime)} - {formatTime(session.endTime)}
                </span>
              </div>
              <div className="flex items-center text-sm">
                <span className="font-medium text-gray-700 w-20">Duración:</span>
                <span className="text-gray-900">
                  {Math.round((new Date(session.endTime) - new Date(session.startTime)) / (1000 * 60))} minutos
                </span>
              </div>
            </div>
          </div>

          {/* Participantes */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <FiUsers className="mr-2" />
              Participantes
            </h3>

            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <span className="font-medium text-gray-700 w-20">Mentor:</span>
                <span className="text-gray-900">
                  {session.Mentor?.firstName} {session.Mentor?.lastName}
                </span>
              </div>
              <div className="flex items-center text-sm">
                <span className="font-medium text-gray-700 w-20">Emprendedor:</span>
                <span className="text-gray-900">
                  {session.Entrepreneur?.firstName} {session.Entrepreneur?.lastName}
                </span>
              </div>
              {session.Accelerator && (
                <div className="flex items-center text-sm">
                  <span className="font-medium text-gray-700 w-20">Acelerador:</span>
                  <span className="text-gray-900">{session.Accelerator.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Descripción */}
        {session.description && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Descripción</h3>
            <p className="text-gray-700 whitespace-pre-wrap">{session.description}</p>
          </div>
        )}

        {/* Ubicación y enlace */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          {session.location && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 flex items-center mb-2">
                <FiMapPin className="mr-1" />
                Ubicación
              </h4>
              <p className="text-gray-900">{session.location}</p>
            </div>
          )}

          {session.videoMeetingLink && (
            <div>
              <h4 className="text-sm font-medium text-gray-700 flex items-center mb-2">
                <FiVideo className="mr-1" />
                Enlace de Reunión
              </h4>
              <a
                href={session.videoMeetingLink}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 flex items-center"
              >
                Unirse a la reunión
                <FiExternalLink className="ml-1" />
              </a>
            </div>
          )}
        </div>
      </Card>

      {/* Notas de la sesión */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <FiFileText className="mr-2" />
            Notas de la Sesión ({notes.length})
          </h2>
          <Button
            variant="primary"
            size="sm"
            onClick={() => setShowNoteModal(true)}
            className="flex items-center"
          >
            <FiPlus className="mr-1" />
            Agregar Nota
          </Button>
        </div>

        {notes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FiFileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No hay notas para esta sesión</p>
            <p className="text-sm">Agrega la primera nota para documentar la sesión</p>
          </div>
        ) : (
          <div className="space-y-4">
            {notes.map((note) => (
              <div key={note.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      {note.Author?.firstName} {note.Author?.lastName}
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(note.createdAt).toLocaleDateString('es-ES', {
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
                <p className="text-gray-700 whitespace-pre-wrap">{note.content}</p>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Tareas relacionadas */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <FiCheck className="mr-2" />
            Tareas Relacionadas ({tasks.length})
          </h2>
          <Button
            variant="primary"
            size="sm"
            onClick={() => setShowTaskModal(true)}
            className="flex items-center"
          >
            <FiPlus className="mr-1" />
            Crear Tarea
          </Button>
        </div>

        {tasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FiCheck className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No hay tareas relacionadas con esta sesión</p>
            <p className="text-sm">Crea tareas para dar seguimiento a los compromisos</p>
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.map((task) => (
              <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        task.status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : task.status === 'in_progress'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {task.status === 'completed' ? 'Completada' :
                         task.status === 'in_progress' ? 'En Progreso' : 'Pendiente'}
                      </span>
                    </div>

                    {task.description && (
                      <p className="text-gray-600 text-sm mb-2">{task.description}</p>
                    )}

                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>
                        Asignada a: {task.AssignedTo?.firstName} {task.AssignedTo?.lastName}
                      </span>
                      {task.dueDate && (
                        <span>
                          Vence: {new Date(task.dueDate).toLocaleDateString('es-ES')}
                        </span>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/admin/tasks/${task.id}`)}
                  >
                    Ver Detalles
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Modal para agregar nota */}
      <Modal
        isOpen={showNoteModal}
        onClose={() => setShowNoteModal(false)}
        title="Agregar Nota a la Sesión"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="note-content" className="block text-sm font-medium text-gray-700 mb-2">
              Contenido de la Nota
            </label>
            <textarea
              id="note-content"
              rows={6}
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Escribe aquí los puntos importantes de la sesión, acuerdos, próximos pasos, etc."
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowNoteModal(false)}
              disabled={savingNote}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveNote}
              disabled={!newNote.trim() || savingNote}
              className="flex items-center"
            >
              {savingNote ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Guardando...
                </>
              ) : (
                'Guardar Nota'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal para crear tarea */}
      <Modal
        isOpen={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        title="Crear Tarea Relacionada"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="task-title" className="block text-sm font-medium text-gray-700 mb-1">
              Título de la Tarea <span className="text-red-500">*</span>
            </label>
            <input
              id="task-title"
              type="text"
              value={newTask.title}
              onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Ej: Revisar plan de marketing"
            />
          </div>

          <div>
            <label htmlFor="task-description" className="block text-sm font-medium text-gray-700 mb-1">
              Descripción
            </label>
            <textarea
              id="task-description"
              rows={3}
              value={newTask.description}
              onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe los detalles de la tarea..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="task-assigned" className="block text-sm font-medium text-gray-700 mb-1">
                Asignar a <span className="text-red-500">*</span>
              </label>
              <select
                id="task-assigned"
                value={newTask.assignedToId}
                onChange={(e) => setNewTask(prev => ({ ...prev, assignedToId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Seleccionar persona...</option>
                <option value={session.mentorId}>
                  {session.Mentor?.firstName} {session.Mentor?.lastName} (Mentor)
                </option>
                <option value={session.entrepreneurId}>
                  {session.Entrepreneur?.firstName} {session.Entrepreneur?.lastName} (Emprendedor)
                </option>
              </select>
            </div>

            <div>
              <label htmlFor="task-due-date" className="block text-sm font-medium text-gray-700 mb-1">
                Fecha de Vencimiento
              </label>
              <input
                id="task-due-date"
                type="date"
                value={newTask.dueDate}
                onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                min={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowTaskModal(false)}
              disabled={savingTask}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveTask}
              disabled={!newTask.title.trim() || !newTask.assignedToId || savingTask}
              className="flex items-center"
            >
              {savingTask ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creando...
                </>
              ) : (
                'Crear Tarea'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Diálogo de confirmación */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={executeAction}
        title={
          confirmAction === 'confirm' ? 'Confirmar Sesión' :
          confirmAction === 'complete' ? 'Completar Sesión' :
          confirmAction === 'cancel' ? 'Cancelar Sesión' : ''
        }
        message={
          confirmAction === 'confirm' ? '¿Estás seguro de que quieres confirmar esta sesión?' :
          confirmAction === 'complete' ? '¿Estás seguro de que quieres marcar esta sesión como completada?' :
          confirmAction === 'cancel' ? '¿Estás seguro de que quieres cancelar esta sesión? Esta acción no se puede deshacer.' : ''
        }
        confirmText={
          confirmAction === 'confirm' ? 'Confirmar' :
          confirmAction === 'complete' ? 'Completar' :
          confirmAction === 'cancel' ? 'Cancelar Sesión' : 'Confirmar'
        }
        variant={confirmAction === 'cancel' ? 'danger' : 'primary'}
      />
    </div>
  );
};

export default SessionDetails;
