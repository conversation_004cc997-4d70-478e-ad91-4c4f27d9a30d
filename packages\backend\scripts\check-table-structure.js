const { sequelize } = require('../src/config/database');

async function checkTableStructure() {
  try {
    console.log('Verificando estructura de la tabla Forms...');

    // Consulta para obtener la estructura de la tabla Forms
    const [results] = await sequelize.query(`PRAGMA table_info(Forms);`);

    console.log('Columnas de la tabla Forms:');
    console.table(results);

    // Verificar si existen las columnas necesarias
    const createdByColumn = results.find(col => col.name === 'createdBy');
    const createdByIdColumn = results.find(col => col.name === 'createdById');
    const formTypeColumn = results.find(col => col.name === 'formType');
    const statusColumn = results.find(col => col.name === 'status');

    console.log('Verificación de columnas:');
    console.log('- createdBy:', createdByColumn ? 'Existe' : 'NO existe');
    console.log('- createdById:', createdByIdColumn ? 'Existe' : 'NO existe');
    console.log('- formType:', formTypeColumn ? 'Existe' : 'NO existe');
    console.log('- status:', statusColumn ? 'Existe' : 'NO existe');

    // Verificar también la tabla Applications
    console.log('\nVerificando estructura de la tabla Applications...');
    const [appResults] = await sequelize.query(`PRAGMA table_info(Applications);`);

    console.log('Columnas de la tabla Applications:');
    console.table(appResults);

    const isActiveColumn = appResults.find(col => col.name === 'isActive');
    console.log('- isActive en Applications:', isActiveColumn ? 'Existe' : 'NO existe');

    process.exit(0);
  } catch (error) {
    console.error('Error al verificar la estructura de la tabla:', error);
    process.exit(1);
  }
}

checkTableStructure();
