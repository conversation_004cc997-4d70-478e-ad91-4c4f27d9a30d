import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiCalendar, FiClock, FiUsers, FiMapPin, FiVideo, FiSave, FiArrowLeft } from 'react-icons/fi';
import Button from '../../components/common/Button';
import FormInput from '../../components/common/FormInput';
import Card from '../../components/common/Card';
import Alert from '../../components/common/Alert';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import sessionService from '../../services/sessionService';
import userService from '../../services/user.service';
import acceleratorService from '../../services/accelerator.service';
import useAuth from '../../hooks/useAuth';

const NewSession = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Estados para los datos del formulario
  const [formData, setFormData] = useState({
    mentorId: '',
    entrepreneurId: '',
    acceleratorId: '',
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    location: '',
    videoMeetingLink: ''
  });

  // Estados para validaciones
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Estados para datos de selección
  const [mentors, setMentors] = useState([]);
  const [entrepreneurs, setEntrepreneurs] = useState([]);
  const [accelerators, setAccelerators] = useState([]);

  // Cargar datos iniciales
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoadingData(true);

      // Cargar usuarios por rol
      const [mentorsRes, entrepreneursRes, acceleratorsRes] = await Promise.all([
        userService.getUsersByRole('MENTOR'),
        userService.getUsersByRole('ENTREPRENEUR'),
        acceleratorService.getAllAccelerators()
      ]);

      setMentors(mentorsRes.users || []);
      setEntrepreneurs(entrepreneursRes.users || []);
      setAccelerators(acceleratorsRes.accelerators || []);

      // Si el usuario actual es mentor, preseleccionarlo
      if (user.role === 'MENTOR') {
        setFormData(prev => ({ ...prev, mentorId: user.id.toString() }));
      }
    } catch (error) {
      console.error('Error al cargar datos iniciales:', error);
      setError('Error al cargar los datos necesarios para crear la sesión');
    } finally {
      setLoadingData(false);
    }
  };

  // Manejar cambios en el formulario
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Limpiar error del campo si existe
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Manejar blur para validaciones
  const handleBlur = (e) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    validateField(name, formData[name]);
  };

  // Validar campo individual
  const validateField = (name, value) => {
    let error = '';

    switch (name) {
      case 'mentorId':
        if (!value) error = 'Debe seleccionar un mentor';
        break;
      case 'entrepreneurId':
        if (!value) error = 'Debe seleccionar un emprendedor';
        break;
      case 'title':
        if (!value.trim()) error = 'El título es requerido';
        else if (value.trim().length < 3) error = 'El título debe tener al menos 3 caracteres';
        break;
      case 'startTime':
        if (!value) error = 'La fecha y hora de inicio es requerida';
        else if (new Date(value) <= new Date()) error = 'La fecha debe ser futura';
        break;
      case 'endTime':
        if (!value) error = 'La fecha y hora de fin es requerida';
        else if (formData.startTime && new Date(value) <= new Date(formData.startTime)) {
          error = 'La hora de fin debe ser posterior a la de inicio';
        }
        break;
      default:
        break;
    }

    setErrors(prev => ({ ...prev, [name]: error }));
    return error === '';
  };

  // Validar todo el formulario
  const validateForm = () => {
    const fieldsToValidate = ['mentorId', 'entrepreneurId', 'title', 'startTime', 'endTime'];
    let isValid = true;

    fieldsToValidate.forEach(field => {
      const fieldIsValid = validateField(field, formData[field]);
      if (!fieldIsValid) isValid = false;
    });

    // Validación adicional: mentor y emprendedor no pueden ser la misma persona
    if (formData.mentorId && formData.entrepreneurId && formData.mentorId === formData.entrepreneurId) {
      setErrors(prev => ({
        ...prev,
        entrepreneurId: 'El mentor y el emprendedor deben ser personas diferentes'
      }));
      isValid = false;
    }

    return isValid;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Marcar todos los campos como tocados
    const allTouched = {};
    Object.keys(formData).forEach(key => {
      allTouched[key] = true;
    });
    setTouched(allTouched);

    // Validar formulario
    if (!validateForm()) {
      setError('Por favor, corrija los errores en el formulario');
      return;
    }

    try {
      setLoading(true);

      // Preparar datos para envío
      const sessionData = {
        ...formData,
        mentorId: parseInt(formData.mentorId),
        entrepreneurId: parseInt(formData.entrepreneurId),
        acceleratorId: formData.acceleratorId ? parseInt(formData.acceleratorId) : null,
        startTime: new Date(formData.startTime).toISOString(),
        endTime: new Date(formData.endTime).toISOString()
      };

      const response = await sessionService.createSession(sessionData);

      setSuccess('Sesión creada exitosamente');

      // Redirigir después de un breve delay
      setTimeout(() => {
        navigate('/admin/sessions');
      }, 1500);

    } catch (error) {
      console.error('Error al crear sesión:', error);
      setError(error.response?.data?.message || 'Error al crear la sesión');
    } finally {
      setLoading(false);
    }
  };

  // Formatear fecha para input datetime-local
  const formatDateTimeLocal = (date) => {
    if (!date) return '';
    const d = new Date(date);
    d.setMinutes(d.getMinutes() - d.getTimezoneOffset());
    return d.toISOString().slice(0, 16);
  };

  // Obtener fecha mínima (ahora + 1 hora)
  const getMinDateTime = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    return formatDateTimeLocal(now);
  };

  if (loadingData) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <button
          onClick={() => navigate('/admin/sessions')}
          className="flex items-center hover:text-blue-600 transition-colors"
        >
          <FiArrowLeft className="mr-1" />
          Sesiones
        </button>
        <span>/</span>
        <span className="text-gray-900">Nueva Sesión</span>
      </div>

      {/* Título */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Nueva Sesión de Coaching</h1>
        <p className="mt-1 text-sm text-gray-600">
          Programa una nueva sesión de mentoría entre un mentor y un emprendedor
        </p>
      </div>

      {/* Alertas */}
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} />
      )}
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} />
      )}

      {/* Formulario */}
      <Card>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Información básica */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <FiUsers className="mr-2" />
              Información Básica
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Mentor */}
              <div>
                <label htmlFor="mentorId" className="block text-sm font-medium text-gray-700 mb-1">
                  Mentor <span className="text-red-500">*</span>
                </label>
                <select
                  id="mentorId"
                  name="mentorId"
                  value={formData.mentorId}
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  disabled={user.role === 'MENTOR'}
                  className={`appearance-none block w-full px-3 py-2 border ${
                    touched.mentorId && errors.mentorId ? 'border-red-300' : 'border-gray-300'
                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    user.role === 'MENTOR' ? 'bg-gray-100 cursor-not-allowed' : ''
                  }`}
                >
                  <option value="">Seleccionar mentor...</option>
                  {mentors.map(mentor => (
                    <option key={mentor.id} value={mentor.id}>
                      {mentor.firstName} {mentor.lastName} ({mentor.email})
                    </option>
                  ))}
                </select>
                {touched.mentorId && errors.mentorId && (
                  <p className="mt-2 text-sm text-red-600">{errors.mentorId}</p>
                )}
              </div>

              {/* Emprendedor */}
              <div>
                <label htmlFor="entrepreneurId" className="block text-sm font-medium text-gray-700 mb-1">
                  Emprendedor <span className="text-red-500">*</span>
                </label>
                <select
                  id="entrepreneurId"
                  name="entrepreneurId"
                  value={formData.entrepreneurId}
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  className={`appearance-none block w-full px-3 py-2 border ${
                    touched.entrepreneurId && errors.entrepreneurId ? 'border-red-300' : 'border-gray-300'
                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                >
                  <option value="">Seleccionar emprendedor...</option>
                  {entrepreneurs.map(entrepreneur => (
                    <option key={entrepreneur.id} value={entrepreneur.id}>
                      {entrepreneur.firstName} {entrepreneur.lastName} ({entrepreneur.email})
                    </option>
                  ))}
                </select>
                {touched.entrepreneurId && errors.entrepreneurId && (
                  <p className="mt-2 text-sm text-red-600">{errors.entrepreneurId}</p>
                )}
              </div>
            </div>

            {/* Acelerador */}
            <div className="mt-6">
              <label htmlFor="acceleratorId" className="block text-sm font-medium text-gray-700 mb-1">
                Acelerador (Opcional)
              </label>
              <select
                id="acceleratorId"
                name="acceleratorId"
                value={formData.acceleratorId}
                onChange={handleInputChange}
                className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Sin acelerador específico</option>
                {accelerators.map(accelerator => (
                  <option key={accelerator.id} value={accelerator.id}>
                    {accelerator.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Detalles de la sesión */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <FiCalendar className="mr-2" />
              Detalles de la Sesión
            </h3>

            <div className="space-y-6">
              {/* Título */}
              <FormInput
                label="Título"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                onBlur={handleBlur}
                error={errors.title}
                touched={touched.title}
                required
                placeholder="Ej: Revisión de Plan de Negocio"
              />

              {/* Descripción */}
              <FormInput
                label="Descripción"
                id="description"
                name="description"
                type="textarea"
                rows={4}
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe los objetivos y temas a tratar en la sesión..."
              />

              {/* Fechas y horas */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormInput
                  label="Fecha y Hora de Inicio"
                  id="startTime"
                  name="startTime"
                  type="datetime-local"
                  value={formData.startTime}
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  error={errors.startTime}
                  touched={touched.startTime}
                  required
                  min={getMinDateTime()}
                />

                <FormInput
                  label="Fecha y Hora de Fin"
                  id="endTime"
                  name="endTime"
                  type="datetime-local"
                  value={formData.endTime}
                  onChange={handleInputChange}
                  onBlur={handleBlur}
                  error={errors.endTime}
                  touched={touched.endTime}
                  required
                  min={formData.startTime || getMinDateTime()}
                />
              </div>
            </div>
          </div>

          {/* Ubicación y enlace */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <FiMapPin className="mr-2" />
              Ubicación y Acceso
            </h3>

            <div className="space-y-6">
              <FormInput
                label="Ubicación"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="Ej: Oficina virtual, Sala de reuniones A, etc."
                helpText="Especifica dónde se realizará la sesión"
              />

              <FormInput
                label="Enlace de Videoconferencia"
                id="videoMeetingLink"
                name="videoMeetingLink"
                type="url"
                value={formData.videoMeetingLink}
                onChange={handleInputChange}
                placeholder="https://meet.google.com/xxx-xxxx-xxx"
                helpText="Enlace para la reunión virtual (Zoom, Google Meet, etc.)"
              />
            </div>
          </div>

          {/* Botones de acción */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin/sessions')}
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              className="flex items-center"
            >
              {loading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creando...
                </>
              ) : (
                <>
                  <FiSave className="mr-2" />
                  Crear Sesión
                </>
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};

export default NewSession;
