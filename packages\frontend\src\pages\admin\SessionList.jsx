import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiClock,
  FiUsers,
  FiPlus,
  FiSearch,
  FiFilter,
  FiEye,
  FiEdit,
  FiCheck,
  FiX,
  FiFileText
} from 'react-icons/fi';
import { Link } from 'react-router-dom';
import sessionService from '../../services/sessionService';
import useAuth from '../../hooks/useAuth';

const SessionList = () => {
  const { user } = useAuth();
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    status: '',
    sortBy: 'startTime',
    sortOrder: 'DESC'
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalSessions, setTotalSessions] = useState(0);

  useEffect(() => {
    loadSessions();
  }, [filters]);

  const loadSessions = async () => {
    try {
      setLoading(true);

      // Agregar filtros basados en el rol del usuario
      const apiFilters = { ...filters };
      if (user.role === 'MENTOR') {
        apiFilters.mentorId = user.id;
      } else if (user.role === 'ENTREPRENEUR') {
        apiFilters.entrepreneurId = user.id;
      }

      const response = await sessionService.getAllSessions(apiFilters);
      setSessions(response.sessions || []);
      setTotalPages(response.totalPages || 1);
      setTotalSessions(response.totalSessions || 0);
    } catch (error) {
      console.error('Error al cargar sesiones:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset page when filters change
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const handleStatusChange = async (sessionId, action) => {
    try {
      let response;
      switch (action) {
        case 'confirm':
          response = await sessionService.confirmSession(sessionId);
          break;
        case 'complete':
          response = await sessionService.completeSession(sessionId);
          break;
        case 'cancel':
          response = await sessionService.cancelSession(sessionId, {
            cancelledBy: user.role === 'MENTOR' ? 'mentor' : 'entrepreneur',
            reason: 'Cancelada desde la interfaz'
          });
          break;
        default:
          return;
      }

      if (response.success) {
        loadSessions(); // Reload sessions
      }
    } catch (error) {
      console.error('Error al cambiar estado de sesión:', error);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      pending_confirmation: 'bg-yellow-100 text-yellow-800',
      scheduled: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      cancelled_mentor: 'bg-red-100 text-red-800',
      cancelled_entrepreneur: 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status) => {
    const texts = {
      pending_confirmation: 'Pendiente confirmación',
      scheduled: 'Programada',
      completed: 'Completada',
      cancelled_mentor: 'Cancelada por mentor',
      cancelled_entrepreneur: 'Cancelada por emprendedor'
    };
    return texts[status] || status;
  };

  const canPerformAction = (session, action) => {
    const isOwner = session.mentorId === user.id || session.entrepreneurId === user.id;
    const isAdmin = ['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(user.role);

    if (!isOwner && !isAdmin) return false;

    switch (action) {
      case 'confirm':
        return session.status === 'pending_confirmation';
      case 'complete':
        return session.status === 'scheduled' && (user.role === 'MENTOR' || isAdmin);
      case 'cancel':
        return ['pending_confirmation', 'scheduled'].includes(session.status);
      default:
        return false;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Sesiones de Coaching
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Gestiona tus sesiones de coaching y mentoría ({totalSessions} total)
          </p>
        </div>
        <div className="mt-4 flex space-x-3 md:mt-0 md:ml-4">
          <Link
            to="/admin/tasks"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiCheck className="-ml-1 mr-2 h-5 w-5" />
            Ver Tareas
          </Link>

          <Link
            to="/admin/session-notes"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiFileText className="-ml-1 mr-2 h-5 w-5" />
            Ver Notas
          </Link>

          {(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR'].includes(user.role)) && (
            <Link
              to="/admin/sessions/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <FiPlus className="-ml-1 mr-2 h-5 w-5" />
              Nueva Sesión
            </Link>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Buscar</label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                placeholder="Buscar sesiones..."
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Estado</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">Todos los estados</option>
              <option value="pending_confirmation">Pendiente confirmación</option>
              <option value="scheduled">Programada</option>
              <option value="completed">Completada</option>
              <option value="cancelled_mentor">Cancelada por mentor</option>
              <option value="cancelled_entrepreneur">Cancelada por emprendedor</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Ordenar por</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="startTime">Fecha de inicio</option>
              <option value="title">Título</option>
              <option value="status">Estado</option>
              <option value="createdAt">Fecha de creación</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Orden</label>
            <select
              value={filters.sortOrder}
              onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="DESC">Descendente</option>
              <option value="ASC">Ascendente</option>
            </select>
          </div>
        </div>
      </div>

      {/* Sessions List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {sessions.length > 0 ? (
            sessions.map((session) => (
              <li key={session.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-indigo-600 truncate">
                          {session.title}
                        </p>
                        <div className="ml-2 flex-shrink-0 flex">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                            {getStatusText(session.status)}
                          </span>
                        </div>
                      </div>
                      <div className="mt-2 sm:flex sm:justify-between">
                        <div className="sm:flex">
                          <p className="flex items-center text-sm text-gray-500">
                            <FiCalendar className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                            {formatDate(session.startTime)} - {formatDate(session.endTime)}
                          </p>
                          <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                            <FiUsers className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" />
                            {session.Mentor?.firstName} {session.Mentor?.lastName} - {session.Entrepreneur?.firstName} {session.Entrepreneur?.lastName}
                          </p>
                        </div>
                        <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                          {session.location && (
                            <p className="flex items-center">
                              📍 {session.location}
                            </p>
                          )}
                        </div>
                      </div>
                      {session.description && (
                        <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                          {session.description}
                        </p>
                      )}
                    </div>
                    <div className="ml-4 flex-shrink-0 flex space-x-2">
                      <Link
                        to={`/admin/sessions/${session.id}`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <FiEye className="h-4 w-4 mr-1" />
                        Ver
                      </Link>

                      {canPerformAction(session, 'confirm') && (
                        <button
                          onClick={() => handleStatusChange(session.id, 'confirm')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <FiCheck className="h-4 w-4 mr-1" />
                          Confirmar
                        </button>
                      )}

                      {canPerformAction(session, 'complete') && (
                        <button
                          onClick={() => handleStatusChange(session.id, 'complete')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <FiCheck className="h-4 w-4 mr-1" />
                          Completar
                        </button>
                      )}

                      {canPerformAction(session, 'cancel') && (
                        <button
                          onClick={() => handleStatusChange(session.id, 'cancel')}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <FiX className="h-4 w-4 mr-1" />
                          Cancelar
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </li>
            ))
          ) : (
            <li className="px-4 py-12 text-center">
              <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No hay sesiones</h3>
              <p className="mt-1 text-sm text-gray-500">
                Comienza creando una nueva sesión de coaching.
              </p>
              {(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR'].includes(user.role)) && (
                <div className="mt-6">
                  <Link
                    to="/admin/sessions/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <FiPlus className="-ml-1 mr-2 h-5 w-5" />
                    Nueva Sesión
                  </Link>
                </div>
              )}
            </li>
          )}
        </ul>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(filters.page - 1)}
              disabled={filters.page <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button
              onClick={() => handlePageChange(filters.page + 1)}
              disabled={filters.page >= totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Siguiente
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Mostrando página <span className="font-medium">{filters.page}</span> de{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handlePageChange(filters.page - 1)}
                  disabled={filters.page <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Anterior
                </button>
                <button
                  onClick={() => handlePageChange(filters.page + 1)}
                  disabled={filters.page >= totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Siguiente
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionList;
