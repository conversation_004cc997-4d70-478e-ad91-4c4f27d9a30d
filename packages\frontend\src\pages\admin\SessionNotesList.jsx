import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FiFileText,
  FiSearch,
  FiFilter,
  FiCalendar,
  FiUser,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiEye,
  FiExternalLink
} from 'react-icons/fi';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import Alert from '../../components/common/Alert';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import FormInput from '../../components/common/FormInput';
import sessionNoteService from '../../services/sessionNoteService';
import sessionService from '../../services/sessionService';
import useAuth from '../../hooks/useAuth';

const SessionNotesList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [notes, setNotes] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalNotes, setTotalNotes] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Estados para filtros
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    sessionId: '',
    authorId: '',
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  });

  // Estados para modales
  const [showNewNoteModal, setShowNewNoteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);

  // Estados para nueva nota
  const [newNote, setNewNote] = useState({
    sessionId: '',
    content: ''
  });
  const [savingNote, setSavingNote] = useState(false);

  // Estados para datos de selección
  const [sessions, setSessions] = useState([]);

  useEffect(() => {
    loadNotes();
    loadSessions();
  }, [filters]);

  const loadNotes = async () => {
    try {
      setLoading(true);

      // Aplicar filtros basados en el rol del usuario
      const apiFilters = { ...filters };
      if (user.role === 'MENTOR' || user.role === 'ENTREPRENEUR') {
        // Los mentores y emprendedores solo ven sus notas
        apiFilters.authorId = user.id;
      }

      const response = await sessionNoteService.getAllNotes(apiFilters);
      setNotes(response.notes || []);
      setTotalPages(response.totalPages || 1);
      setTotalNotes(response.totalNotes || 0);
    } catch (error) {
      console.error('Error al cargar notas:', error);
      setError('Error al cargar las notas de sesión');
    } finally {
      setLoading(false);
    }
  };

  const loadSessions = async () => {
    try {
      // Cargar sesiones para los filtros y selección
      const response = await sessionService.getAllSessions({ limit: 100 });
      setSessions(response.sessions || []);
    } catch (error) {
      console.error('Error al cargar sesiones:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset page when filters change
    }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const truncateContent = (content, maxLength = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const handleViewNote = (note) => {
    setSelectedNote(note);
    setShowViewModal(true);
  };

  const handleEditNote = (note) => {
    setSelectedNote(note);
    setNewNote({
      sessionId: note.sessionId.toString(),
      content: note.content
    });
    setShowEditModal(true);
  };

  const handleDeleteNote = (note) => {
    setSelectedNote(note);
    setShowDeleteDialog(true);
  };

  const executeDelete = async () => {
    try {
      await sessionNoteService.deleteNote(selectedNote.id);
      setSuccess('Nota eliminada correctamente');
      setShowDeleteDialog(false);
      setSelectedNote(null);
      loadNotes();
    } catch (error) {
      setError('Error al eliminar la nota');
    }
  };

  const handleSaveNote = async () => {
    if (!newNote.content.trim() || !newNote.sessionId) return;

    try {
      setSavingNote(true);

      const noteData = {
        sessionId: parseInt(newNote.sessionId),
        content: newNote.content.trim()
      };

      if (showEditModal && selectedNote) {
        await sessionNoteService.updateNote(selectedNote.id, noteData);
        setSuccess('Nota actualizada correctamente');
      } else {
        await sessionNoteService.createNote(noteData);
        setSuccess('Nota creada correctamente');
      }

      setNewNote({ sessionId: '', content: '' });
      setShowNewNoteModal(false);
      setShowEditModal(false);
      setSelectedNote(null);
      loadNotes();
    } catch (error) {
      setError(showEditModal ? 'Error al actualizar la nota' : 'Error al crear la nota');
    } finally {
      setSavingNote(false);
    }
  };

  const canUserModifyNotes = () => {
    return ['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR'].includes(user.role);
  };

  const canUserModifyNote = (note) => {
    if (!canUserModifyNotes()) return false;

    return (
      user.role === 'GLOBAL_ADMIN' ||
      user.role === 'ACCELERATOR_ADMIN' ||
      note.authorId === user.id
    );
  };

  return (
    <div className="space-y-6">
      {/* Título y botón de nueva nota */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Notas de Sesión</h1>
          <p className="mt-1 text-sm text-gray-600">
            Gestiona y consulta las notas documentadas en las sesiones de coaching
          </p>
        </div>

        {canUserModifyNotes() && (
          <Button
            variant="primary"
            onClick={() => setShowNewNoteModal(true)}
            className="flex items-center"
          >
            <FiPlus className="mr-2" />
            Nueva Nota
          </Button>
        )}
      </div>

      {/* Alertas */}
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} />
      )}
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} />
      )}

      {/* Filtros */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Búsqueda */}
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar en contenido..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Sesión */}
          <select
            value={filters.sessionId}
            onChange={(e) => handleFilterChange('sessionId', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Todas las sesiones</option>
            {sessions.map(session => (
              <option key={session.id} value={session.id}>
                {session.title} - {formatDate(session.startTime)}
              </option>
            ))}
          </select>

          {/* Autor (solo para admins) */}
          {['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(user.role) && (
            <select
              value={filters.authorId}
              onChange={(e) => handleFilterChange('authorId', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Cualquier autor</option>
              {/* Aquí podrías cargar una lista de usuarios si es necesario */}
            </select>
          )}

          {/* Ordenamiento */}
          <select
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="createdAt-DESC">Más recientes</option>
            <option value="createdAt-ASC">Más antiguas</option>
            <option value="updatedAt-DESC">Últimas actualizadas</option>
          </select>
        </div>
      </Card>

      {/* Lista de notas */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">
            Notas ({totalNotes})
          </h2>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : notes.length === 0 ? (
          <div className="text-center py-12">
            <FiFileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay notas</h3>
            <p className="text-gray-600 mb-4">
              {filters.search || filters.sessionId || filters.authorId
                ? 'No se encontraron notas con los filtros aplicados'
                : 'Aún no se han creado notas de sesión'}
            </p>
            {canUserModifyNotes() && !filters.search && !filters.sessionId && !filters.authorId && (
              <Button
                variant="primary"
                onClick={() => setShowNewNoteModal(true)}
                className="flex items-center mx-auto"
              >
                <FiPlus className="mr-2" />
                Crear Primera Nota
              </Button>
            )}
          </div>
        ) : (
          <>
            {/* Grid de notas */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {notes.map((note) => (
                <div key={note.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  {/* Header de la nota */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center space-x-2">
                      <FiFileText className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium text-gray-900">
                        Nota #{note.id}
                      </span>
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewNote(note)}
                        className="p-1"
                      >
                        <FiEye className="h-4 w-4" />
                      </Button>
                      {canUserModifyNote(note) && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditNote(note)}
                            className="p-1"
                          >
                            <FiEdit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="danger"
                            size="sm"
                            onClick={() => handleDeleteNote(note)}
                            className="p-1"
                          >
                            <FiTrash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Información de la sesión */}
                  <div className="mb-3">
                    <div className="flex items-center space-x-2 mb-1">
                      <FiCalendar className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {note.Session?.title || 'Sesión sin título'}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <FiUser className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {note.Author?.firstName} {note.Author?.lastName}
                      </span>
                    </div>
                  </div>

                  {/* Contenido de la nota */}
                  <div className="mb-3">
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {truncateContent(note.content)}
                    </p>
                  </div>

                  {/* Footer */}
                  <div className="flex justify-between items-center text-xs text-gray-500">
                    <span>Creada: {formatDate(note.createdAt)}</span>
                    {note.Session && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/sessions/${note.Session.id}`)}
                        className="flex items-center text-xs"
                      >
                        Ver Sesión
                        <FiExternalLink className="ml-1 h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Paginación */}
            {totalPages > 1 && (
              <div className="mt-6">
                <Pagination
                  currentPage={filters.page}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </Card>

      {/* Modal para nueva nota */}
      <Modal
        isOpen={showNewNoteModal}
        onClose={() => setShowNewNoteModal(false)}
        title="Crear Nueva Nota"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="note-session" className="block text-sm font-medium text-gray-700 mb-1">
              Sesión <span className="text-red-500">*</span>
            </label>
            <select
              id="note-session"
              value={newNote.sessionId}
              onChange={(e) => setNewNote(prev => ({ ...prev, sessionId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Seleccionar sesión...</option>
              {sessions.map(session => (
                <option key={session.id} value={session.id}>
                  {session.title} - {formatDate(session.startTime)}
                </option>
              ))}
            </select>
          </div>

          <FormInput
            label="Contenido de la Nota"
            id="note-content"
            name="content"
            type="textarea"
            rows={8}
            value={newNote.content}
            onChange={(e) => setNewNote(prev => ({ ...prev, content: e.target.value }))}
            required
            placeholder="Escribe aquí los puntos importantes de la sesión, acuerdos, próximos pasos, observaciones, etc."
          />

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowNewNoteModal(false)}
              disabled={savingNote}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveNote}
              disabled={!newNote.content.trim() || !newNote.sessionId || savingNote}
              className="flex items-center"
            >
              {savingNote ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creando...
                </>
              ) : (
                'Crear Nota'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal para editar nota */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Editar Nota"
        size="lg"
      >
        <div className="space-y-4">
          <div>
            <label htmlFor="edit-note-session" className="block text-sm font-medium text-gray-700 mb-1">
              Sesión <span className="text-red-500">*</span>
            </label>
            <select
              id="edit-note-session"
              value={newNote.sessionId}
              onChange={(e) => setNewNote(prev => ({ ...prev, sessionId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Seleccionar sesión...</option>
              {sessions.map(session => (
                <option key={session.id} value={session.id}>
                  {session.title} - {formatDate(session.startTime)}
                </option>
              ))}
            </select>
          </div>

          <FormInput
            label="Contenido de la Nota"
            id="edit-note-content"
            name="content"
            type="textarea"
            rows={8}
            value={newNote.content}
            onChange={(e) => setNewNote(prev => ({ ...prev, content: e.target.value }))}
            required
            placeholder="Escribe aquí los puntos importantes de la sesión, acuerdos, próximos pasos, observaciones, etc."
          />

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowEditModal(false)}
              disabled={savingNote}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveNote}
              disabled={!newNote.content.trim() || !newNote.sessionId || savingNote}
              className="flex items-center"
            >
              {savingNote ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Actualizando...
                </>
              ) : (
                'Actualizar Nota'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal para ver nota completa */}
      <Modal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        title={`Nota #${selectedNote?.id}`}
        size="lg"
      >
        {selectedNote && (
          <div className="space-y-4">
            {/* Información de la sesión */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Información de la Sesión</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Título:</span>
                  <span className="ml-2 text-gray-900">{selectedNote.Session?.title}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Fecha:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedNote.Session?.startTime && formatDate(selectedNote.Session.startTime)}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Mentor:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedNote.Session?.Mentor?.firstName} {selectedNote.Session?.Mentor?.lastName}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Emprendedor:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedNote.Session?.Entrepreneur?.firstName} {selectedNote.Session?.Entrepreneur?.lastName}
                  </span>
                </div>
              </div>
            </div>

            {/* Información de la nota */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Detalles de la Nota</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                <div>
                  <span className="font-medium text-gray-700">Autor:</span>
                  <span className="ml-2 text-gray-900">
                    {selectedNote.Author?.firstName} {selectedNote.Author?.lastName}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Creada:</span>
                  <span className="ml-2 text-gray-900">{formatDate(selectedNote.createdAt)}</span>
                </div>
                {selectedNote.updatedAt !== selectedNote.createdAt && (
                  <div>
                    <span className="font-medium text-gray-700">Última actualización:</span>
                    <span className="ml-2 text-gray-900">{formatDate(selectedNote.updatedAt)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Contenido de la nota */}
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Contenido</h4>
              <div className="bg-white border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
                <p className="text-gray-700 whitespace-pre-wrap">{selectedNote.content}</p>
              </div>
            </div>

            {/* Acciones */}
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowViewModal(false)}
              >
                Cerrar
              </Button>
              {selectedNote.Session && (
                <Button
                  variant="primary"
                  onClick={() => {
                    setShowViewModal(false);
                    navigate(`/admin/sessions/${selectedNote.Session.id}`);
                  }}
                  className="flex items-center"
                >
                  Ver Sesión
                  <FiExternalLink className="ml-2 h-4 w-4" />
                </Button>
              )}
              {canUserModifyNote(selectedNote) && (
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowViewModal(false);
                    handleEditNote(selectedNote);
                  }}
                  className="flex items-center"
                >
                  <FiEdit className="mr-2 h-4 w-4" />
                  Editar
                </Button>
              )}
            </div>
          </div>
        )}
      </Modal>

      {/* Diálogo de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={executeDelete}
        title="Eliminar Nota"
        message={`¿Estás seguro de que quieres eliminar esta nota? Esta acción no se puede deshacer.`}
        confirmText="Eliminar"
        variant="danger"
      />
    </div>
  );
};

export default SessionNotesList;
