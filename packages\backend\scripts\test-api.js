const axios = require('axios');

// Configuración
const API_URL = 'http://localhost:5000/api';
const EMAIL = '<EMAIL>';
const PASSWORD = 'password123'; // Contraseña del admin global

async function testAPI() {
  try {
    console.log('Iniciando prueba de API...');

    // 1. Autenticación
    console.log('1. Intentando autenticación...');
    const authResponse = await axios.post(`${API_URL}/auth/login`, {
      email: EMAIL,
      password: PASSWORD
    });

    if (authResponse.data.success) {
      console.log('✅ Autenticación exitosa');
      const token = authResponse.data.token;

      // Configurar axios con el token para las siguientes peticiones
      const authAxios = axios.create({
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // 2. Probar obtener formularios
      console.log('\n2. Intentando obtener formularios...');
      try {
        const formsResponse = await authAxios.get(`${API_URL}/forms?page=1&limit=10`);
        console.log('✅ Formularios obtenidos correctamente');
        console.log('Respuesta:', JSON.stringify(formsResponse.data, null, 2));
      } catch (error) {
        console.error('❌ Error al obtener formularios:', error.response?.data || error.message);
      }

      // 3. Probar obtener aplicaciones
      console.log('\n3. Intentando obtener aplicaciones...');
      try {
        const applicationsResponse = await authAxios.get(`${API_URL}/applications?page=1&limit=10`);
        console.log('✅ Aplicaciones obtenidas correctamente');
        console.log('Respuesta:', JSON.stringify(applicationsResponse.data, null, 2));
      } catch (error) {
        console.error('❌ Error al obtener aplicaciones:', error.response?.data || error.message);
      }

    } else {
      console.error('❌ Error de autenticación:', authResponse.data.message);
    }

  } catch (error) {
    console.error('❌ Error en la prueba:', error.response?.data || error.message);
  }
}

testAPI();
