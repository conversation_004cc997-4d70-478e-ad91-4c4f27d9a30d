const express = require('express');
const router = express.Router();
const taskController = require('../controllers/task.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateTask, validateUpdateTask } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para obtener tareas
router.get('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), taskController.getAllTasks);
router.get('/stats', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), taskController.getTaskStats);
router.get('/user/:userId?', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), taskController.getUserTasks);
router.get('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), taskController.getTaskById);

// Rutas para crear, actualizar y eliminar tareas
router.post('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), validateCreateTask, taskController.createTask);
router.put('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), validateUpdateTask, taskController.updateTask);
router.delete('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), taskController.deleteTask);

// Rutas para cambiar estado de tareas
router.patch('/:id/complete', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), taskController.completeTask);

module.exports = router;
