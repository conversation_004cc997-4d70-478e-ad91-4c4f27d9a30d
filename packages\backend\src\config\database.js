const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config();
const { debugLog, debugError, DEBUG_MODE } = require('../utils/debug');

// Obtener la ruta de la base de datos desde las variables de entorno o usar la predeterminada
const dbPath = process.env.DB_PATH || path.join(__dirname, '../../database.sqlite');

// Configuración de la base de datos SQLite
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: dbPath,
  logging: DEBUG_MODE ? console.log : false, // Mostrar consultas SQL solo en modo debug
});

debugLog(`Usando base de datos en: ${dbPath}`);
console.log(`Base de datos: ${dbPath}`);

// Función para probar la conexión
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    debugLog('Conexión a la base de datos establecida correctamente.');
    console.log('Conexión a la base de datos establecida correctamente.');
    return true;
  } catch (error) {
    debugError('Error al conectar con la base de datos:', error);
    console.error('Error al conectar con la base de datos:', error.message);
    return false;
  }
};

module.exports = {
  sequelize,
  testConnection,
};
