import api from './api';

class SessionNoteService {
  // Obtener todas las notas con filtros y paginación
  async getAllNotes(filters = {}) {
    try {
      const params = new URLSearchParams({
        page: filters.page || 1,
        limit: filters.limit || 10,
        search: filters.search || '',
        sessionId: filters.sessionId || '',
        authorId: filters.authorId || '',
        sortBy: filters.sortBy || 'createdAt',
        sortOrder: filters.sortOrder || 'DESC'
      });

      const response = await api.get(`/session-notes?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener notas:', error);
      throw error;
    }
  }

  // Obtener todas las notas de una sesión
  async getSessionNotes(sessionId) {
    try {
      const response = await api.get(`/session-notes/session/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener notas de sesión:', error);
      throw error;
    }
  }

  // Obtener una nota por ID
  async getNoteById(noteId) {
    try {
      const response = await api.get(`/session-notes/${noteId}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener nota:', error);
      throw error;
    }
  }

  // Crear una nueva nota
  async createNote(noteData) {
    try {
      const response = await api.post('/session-notes', noteData);
      return response.data;
    } catch (error) {
      console.error('Error al crear nota:', error);
      throw error;
    }
  }

  // Actualizar una nota
  async updateNote(noteId, noteData) {
    try {
      const response = await api.put(`/session-notes/${noteId}`, noteData);
      return response.data;
    } catch (error) {
      console.error('Error al actualizar nota:', error);
      throw error;
    }
  }

  // Eliminar una nota
  async deleteNote(noteId) {
    try {
      const response = await api.delete(`/session-notes/${noteId}`);
      return response.data;
    } catch (error) {
      console.error('Error al eliminar nota:', error);
      throw error;
    }
  }

  // Obtener notas de un usuario
  async getUserNotes(userId, filters = {}) {
    try {
      const params = new URLSearchParams({
        page: filters.page || 1,
        limit: filters.limit || 10
      });

      const response = await api.get(`/session-notes/user/${userId}?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener notas del usuario:', error);
      throw error;
    }
  }

  // Obtener notas recientes
  async getRecentNotes(limit = 5) {
    try {
      const response = await api.get(`/session-notes/user?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener notas recientes:', error);
      throw error;
    }
  }

  // Buscar notas por contenido
  async searchNotes(searchTerm, filters = {}) {
    try {
      // Obtener todas las notas del usuario y filtrar localmente
      const allNotes = await this.getUserNotes(filters.userId, { limit: 1000 });
      const notes = allNotes.notes || [];

      const filteredNotes = notes.filter(note =>
        note.content.toLowerCase().includes(searchTerm.toLowerCase())
      );

      return {
        success: true,
        notes: filteredNotes,
        totalNotes: filteredNotes.length
      };
    } catch (error) {
      console.error('Error al buscar notas:', error);
      throw error;
    }
  }

  // Exportar notas de una sesión
  async exportSessionNotes(sessionId, format = 'text') {
    try {
      const response = await this.getSessionNotes(sessionId);
      const notes = response.notes || [];

      if (format === 'text') {
        return notes.map(note => {
          const date = new Date(note.createdAt).toLocaleString();
          const author = `${note.Author?.firstName} ${note.Author?.lastName}`;
          return `[${date}] ${author}:\n${note.content}\n\n`;
        }).join('');
      }

      if (format === 'json') {
        return JSON.stringify(notes, null, 2);
      }

      return notes;
    } catch (error) {
      console.error('Error al exportar notas:', error);
      throw error;
    }
  }

  // Obtener estadísticas de notas
  async getNotesStats(userId) {
    try {
      const response = await this.getUserNotes(userId, { limit: 1000 });
      const notes = response.notes || [];

      const stats = {
        total: notes.length,
        thisMonth: 0,
        thisWeek: 0,
        averageLength: 0,
        bySessions: {}
      };

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));

      let totalLength = 0;

      notes.forEach(note => {
        const noteDate = new Date(note.createdAt);

        // Contar este mes
        if (noteDate >= startOfMonth) {
          stats.thisMonth++;
        }

        // Contar esta semana
        if (noteDate >= startOfWeek) {
          stats.thisWeek++;
        }

        // Calcular longitud promedio
        totalLength += note.content.length;

        // Contar por sesión
        const sessionId = note.Session?.id;
        if (sessionId) {
          stats.bySessions[sessionId] = (stats.bySessions[sessionId] || 0) + 1;
        }
      });

      stats.averageLength = notes.length > 0 ? Math.round(totalLength / notes.length) : 0;

      return stats;
    } catch (error) {
      console.error('Error al obtener estadísticas de notas:', error);
      throw error;
    }
  }
}

export default new SessionNoteService();
