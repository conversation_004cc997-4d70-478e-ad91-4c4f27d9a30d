# Bumeran Platform

Plataforma SaaS de aceleración de startups

## Project Structure

This is a monorepo containing the following packages:

- `packages/backend`: Express/SQLite backend API
- `packages/frontend`: React/Vite frontend application
- `packages/emails`: Email templates and utilities

## Features

- **User Management**: Complete user management with role-based access control
- **Accelerator Management**: Create and manage accelerator programs
- **Registration Codes**: Generate and manage registration codes for inviting users
- **Form Builder**: Create dynamic forms for collecting information from entrepreneurs
- **Application Management**: Review and evaluate applications through a customizable funnel
- **Email Notifications**: Send email notifications for important events

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm (v8 or higher)

### Installation

```bash
# Install all dependencies
npm install
```

### Development

```bash
# Start both backend and frontend in development mode
npm run dev

# Start only backend
npm run start:backend

# Start only frontend
npm run start:frontend
```

### Building

```bash
# Build all packages
npm run build
```

## License

ISC
