# Bumeran API Documentation

This document provides information about the Bumeran API endpoints, their parameters, and responses.

## Base URL

All API endpoints are relative to the base URL:

```
/api
```

## Authentication

Most endpoints require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <your_token>
```

## Error Handling

All API endpoints return a consistent error format:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information (only in development mode)"
}
```

## Authentication API

### Register a new user

```
POST /auth/register
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "securepassword",
  "roleId": 4
}
```

**Response:**
```json
{
  "success": true,
  "message": "Usuario registrado correctamente",
  "user": {
    "id": 1,
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "ENTREPRENEUR"
  },
  "token": "jwt_token_here"
}
```

### Login

```
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Inicio de sesión exitoso",
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "ENTREPRENEUR"
  },
  "token": "jwt_token_here"
}
```

### Forgot Password

```
POST /auth/forgot
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Se ha enviado un correo con instrucciones para restablecer la contraseña"
}
```

### Reset Password

```
POST /auth/reset/:token
```

**Request Body:**
```json
{
  "password": "newpassword"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Contraseña restablecida correctamente"
}
```

### Get Current User Profile

```
GET /auth/profile
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "profilePicture": null,
    "role": "ENTREPRENEUR",
    "profileData": {},
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z",
    "lastLogin": "2023-01-01T00:00:00.000Z"
  }
}
```

### Test SMTP Connection

```
GET /auth/test-smtp
```

**Response:**
```json
{
  "success": true,
  "message": "Conexión SMTP exitosa"
}
```

## User Management API

### Get All Users (Admin Only)

```
GET /users
```

**Query Parameters:**
- `page` (default: 1) - Page number
- `limit` (default: 10) - Number of users per page
- `search` - Search term for firstName, lastName, or email
- `roleId` - Filter by role ID
- `status` - Filter by status ('active' or 'inactive')
- `sortBy` - Sort field (firstName, lastName, email, createdAt, lastLogin)
- `sortOrder` - Sort direction ('ASC' or 'DESC')

**Response:**
```json
{
  "success": true,
  "users": [
    {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "isActive": true,
      "Role": {
        "id": 4,
        "name": "ENTREPRENEUR",
        "description": "Emprendedor"
      }
    }
  ],
  "totalUsers": 50,
  "totalPages": 5,
  "currentPage": 1
}
```

### Create User (Admin Only)

```
POST /users
```

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "password": "securepassword",
  "roleId": 3,
  "profileData": {}
}
```

**Response:**
```json
{
  "success": true,
  "message": "Usuario creado correctamente",
  "user": {
    "id": 2,
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "isActive": true,
    "Role": {
      "id": 3,
      "name": "MENTOR",
      "description": "Mentor de startups"
    }
  }
}
```

### Get All Roles (Admin Only)

```
GET /users/roles
```

**Response:**
```json
{
  "success": true,
  "roles": [
    {
      "id": 1,
      "name": "GLOBAL_ADMIN",
      "description": "Administrador global del sistema"
    },
    {
      "id": 2,
      "name": "ACCELERATOR_ADMIN",
      "description": "Administrador de aceleradora"
    },
    {
      "id": 3,
      "name": "MENTOR",
      "description": "Mentor de startups"
    },
    {
      "id": 4,
      "name": "ENTREPRENEUR",
      "description": "Emprendedor"
    }
  ]
}
```

### Get User by ID (Self or Admin)

```
GET /users/:id
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "profilePicture": null,
    "isActive": true,
    "lastLogin": "2023-01-01T00:00:00.000Z",
    "profileData": {},
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z",
    "Role": {
      "id": 4,
      "name": "ENTREPRENEUR",
      "description": "Emprendedor"
    }
  }
}
```

### Update User (Self or Admin)

```
PUT /users/:id
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "roleId": 4,
  "isActive": true,
  "profileData": {}
}
```

**Response:**
```json
{
  "success": true,
  "message": "Usuario actualizado correctamente",
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "isActive": true,
    "Role": {
      "id": 4,
      "name": "ENTREPRENEUR",
      "description": "Emprendedor"
    }
  }
}
```

### Change Password (Self or Admin)

```
PUT /users/:id/change-password
```

**Request Body:**
```json
{
  "currentPassword": "oldpassword",
  "newPassword": "newpassword"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Contraseña actualizada correctamente"
}
```

### Activate User (Admin Only)

```
PUT /users/:id/activate
```

**Response:**
```json
{
  "success": true,
  "message": "Usuario activado correctamente"
}
```

### Deactivate User (Admin Only)

```
PUT /users/:id/deactivate
```

**Response:**
```json
{
  "success": true,
  "message": "Usuario desactivado correctamente"
}
```

## Accelerator Management API

### Get All Accelerators (Admin Only)

```
GET /accelerators
```

**Query Parameters:**
- `page` (default: 1) - Page number
- `limit` (default: 10) - Number of accelerators per page
- `search` - Search term for name or description
- `status` - Filter by status ('active' or 'inactive')
- `sortBy` - Sort field (name, createdAt, location)
- `sortOrder` - Sort direction ('ASC' or 'DESC')

**Response:**
```json
{
  "success": true,
  "accelerators": [
    {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups",
      "logo": "logo_url.png",
      "website": "https://example.com",
      "location": "Madrid, España",
      "contactEmail": "<EMAIL>",
      "contactPhone": "+34 123 456 789",
      "isActive": true,
      "additionalData": {},
      "administrators": [
        {
          "id": 2,
          "firstName": "Admin",
          "lastName": "User",
          "email": "<EMAIL>"
        }
      ]
    }
  ],
  "totalAccelerators": 5,
  "totalPages": 1,
  "currentPage": 1
}
```

### Create Accelerator (Global Admin Only)

```
POST /accelerators
```

**Request Body:**
```json
{
  "name": "New Accelerator",
  "description": "Description of the accelerator",
  "logo": "logo_url.png",
  "website": "https://example.com",
  "location": "Barcelona, España",
  "contactEmail": "<EMAIL>",
  "contactPhone": "+34 123 456 789",
  "isActive": true,
  "additionalData": {
    "industry": "Technology",
    "programs": ["Seed", "Growth"]
  },
  "administratorIds": [2, 3]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Aceleradora creada correctamente",
  "accelerator": {
    "id": 2,
    "name": "New Accelerator",
    "description": "Description of the accelerator",
    "logo": "logo_url.png",
    "website": "https://example.com",
    "location": "Barcelona, España",
    "contactEmail": "<EMAIL>",
    "contactPhone": "+34 123 456 789",
    "isActive": true,
    "additionalData": {
      "industry": "Technology",
      "programs": ["Seed", "Growth"]
    },
    "administrators": [
      {
        "id": 2,
        "firstName": "Admin",
        "lastName": "User",
        "email": "<EMAIL>"
      },
      {
        "id": 3,
        "firstName": "Another",
        "lastName": "Admin",
        "email": "<EMAIL>"
      }
    ]
  }
}
```

### Get Accelerator by ID (Admin Only)

```
GET /accelerators/:id
```

**Response:**
```json
{
  "success": true,
  "accelerator": {
    "id": 1,
    "name": "Startup Accelerator",
    "description": "Accelerator for tech startups",
    "logo": "logo_url.png",
    "website": "https://example.com",
    "location": "Madrid, España",
    "contactEmail": "<EMAIL>",
    "contactPhone": "+34 123 456 789",
    "isActive": true,
    "additionalData": {},
    "administrators": [
      {
        "id": 2,
        "firstName": "Admin",
        "lastName": "User",
        "email": "<EMAIL>"
      }
    ]
  }
}
```

### Update Accelerator (Admin Only)

```
PUT /accelerators/:id
```

**Request Body:**
```json
{
  "name": "Updated Accelerator Name",
  "description": "Updated description",
  "website": "https://updated-example.com",
  "administratorIds": [2, 4]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Aceleradora actualizada correctamente",
  "accelerator": {
    "id": 1,
    "name": "Updated Accelerator Name",
    "description": "Updated description",
    "logo": "logo_url.png",
    "website": "https://updated-example.com",
    "location": "Madrid, España",
    "contactEmail": "<EMAIL>",
    "contactPhone": "+34 123 456 789",
    "isActive": true,
    "additionalData": {},
    "administrators": [
      {
        "id": 2,
        "firstName": "Admin",
        "lastName": "User",
        "email": "<EMAIL>"
      },
      {
        "id": 4,
        "firstName": "New",
        "lastName": "Admin",
        "email": "<EMAIL>"
      }
    ]
  }
}
```

### Activate Accelerator (Global Admin Only)

```
PUT /accelerators/:id/activate
```

**Response:**
```json
{
  "success": true,
  "message": "Aceleradora activada correctamente"
}
```

### Deactivate Accelerator (Global Admin Only)

```
PUT /accelerators/:id/deactivate
```

**Response:**
```json
{
  "success": true,
  "message": "Aceleradora desactivada correctamente"
}
```

### Assign Administrators to Accelerator (Global Admin Only)

```
POST /accelerators/:id/administrators
```

**Request Body:**
```json
{
  "administratorIds": [2, 3, 4]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Administradores asignados correctamente",
  "assignedCount": 3
}
```

## Registration Codes API

### Create Registration Code (Admin Only)

```
POST /registration-codes
```

**Request Body:**
```json
{
  "roleId": 3,
  "acceleratorId": 1,
  "maxUses": 5,
  "expiresAt": "2023-12-31T23:59:59Z",
  "description": "Códigos para mentores del programa de verano"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Código de registro creado correctamente",
  "registrationCode": {
    "id": 1,
    "code": "ABC123XY",
    "roleId": 3,
    "acceleratorId": 1,
    "createdBy": 1,
    "maxUses": 5,
    "usedCount": 0,
    "expiresAt": "2023-12-31T23:59:59.000Z",
    "isActive": true,
    "description": "Códigos para mentores del programa de verano",
    "Role": {
      "id": 3,
      "name": "MENTOR",
      "description": "Mentor de startups"
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    },
    "creator": {
      "id": 1,
      "firstName": "Admin",
      "lastName": "Global",
      "email": "<EMAIL>"
    }
  }
}
```

### Get All Registration Codes (Admin Only)

```
GET /registration-codes
```

**Query Parameters:**
- `page` (default: 1) - Page number
- `limit` (default: 10) - Number of codes per page
- `roleId` - Filter by role ID
- `acceleratorId` - Filter by accelerator ID
- `isActive` - Filter by active status ('true' or 'false')
- `search` - Search term for code

**Response:**
```json
{
  "success": true,
  "registrationCodes": [
    {
      "id": 1,
      "code": "ABC123XY",
      "roleId": 3,
      "acceleratorId": 1,
      "createdBy": 1,
      "maxUses": 5,
      "usedCount": 2,
      "expiresAt": "2023-12-31T23:59:59.000Z",
      "isActive": true,
      "description": "Códigos para mentores del programa de verano",
      "Role": {
        "id": 3,
        "name": "MENTOR",
        "description": "Mentor de startups"
      },
      "accelerator": {
        "id": 1,
        "name": "Startup Accelerator",
        "description": "Accelerator for tech startups"
      },
      "creator": {
        "id": 1,
        "firstName": "Admin",
        "lastName": "Global",
        "email": "<EMAIL>"
      }
    }
  ],
  "totalCodes": 5,
  "totalPages": 1,
  "currentPage": 1
}
```

### Get Registration Code by ID (Admin Only)

```
GET /registration-codes/:id
```

**Response:**
```json
{
  "success": true,
  "registrationCode": {
    "id": 1,
    "code": "ABC123XY",
    "roleId": 3,
    "acceleratorId": 1,
    "createdBy": 1,
    "maxUses": 5,
    "usedCount": 2,
    "expiresAt": "2023-12-31T23:59:59.000Z",
    "isActive": true,
    "description": "Códigos para mentores del programa de verano",
    "Role": {
      "id": 3,
      "name": "MENTOR",
      "description": "Mentor de startups"
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    },
    "creator": {
      "id": 1,
      "firstName": "Admin",
      "lastName": "Global",
      "email": "<EMAIL>"
    }
  }
}
```

### Update Registration Code (Admin Only)

```
PUT /registration-codes/:id
```

**Request Body:**
```json
{
  "isActive": true,
  "maxUses": 10,
  "expiresAt": "2024-06-30T23:59:59Z",
  "description": "Códigos para mentores (actualizado)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Código de registro actualizado correctamente",
  "registrationCode": {
    "id": 1,
    "code": "ABC123XY",
    "roleId": 3,
    "acceleratorId": 1,
    "createdBy": 1,
    "maxUses": 10,
    "usedCount": 2,
    "expiresAt": "2024-06-30T23:59:59.000Z",
    "isActive": true,
    "description": "Códigos para mentores (actualizado)"
  }
}
```

### Delete Registration Code (Admin Only)

```
DELETE /registration-codes/:id
```

**Response:**
```json
{
  "success": true,
  "message": "Código de registro eliminado correctamente"
}
```

### Verify Registration Code (Public)

```
POST /registration-codes/verify
```

**Request Body:**
```json
{
  "code": "ABC123XY"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Código de registro válido",
  "codeInfo": {
    "roleId": 3,
    "roleName": "MENTOR",
    "acceleratorId": 1,
    "acceleratorName": "Startup Accelerator"
  }
}
```

### Generate Bulk Registration Codes (Admin Only)

```
POST /registration-codes/bulk
```

**Request Body:**
```json
{
  "roleId": 3,
  "acceleratorId": 1,
  "count": 10,
  "maxUses": 1,
  "expiresAt": "2023-12-31T23:59:59Z",
  "description": "Códigos para mentores del programa de verano"
}
```

**Response:**
```json
{
  "success": true,
  "message": "10 códigos de registro generados correctamente",
  "generatedCodes": [
    {
      "id": 1,
      "code": "ABC123XY",
      "roleId": 3,
      "acceleratorId": 1,
      "createdBy": 1,
      "maxUses": 1,
      "usedCount": 0,
      "expiresAt": "2023-12-31T23:59:59.000Z",
      "isActive": true,
      "description": "Códigos para mentores del programa de verano (1/10)"
    },
    // ... más códigos
  ]
}
```

## Form Builder API

### Create Form

```
POST /forms
```

**Request Body:**
```json
{
  "title": "Formulario de Postulación",
  "description": "Formulario para postular a nuestro programa de aceleración",
  "acceleratorId": 1,
  "formType": "application",
  "config": {
    "showProgressBar": true,
    "allowSave": true,
    "submitButtonText": "Enviar Postulación"
  },
  "startDate": "2023-06-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.000Z",
  "isPublished": true,
  "fields": [
    {
      "name": "startup_name",
      "label": "Nombre de la Startup",
      "type": "text",
      "placeholder": "Ingrese el nombre de su startup",
      "helpText": "Nombre comercial de su empresa o proyecto",
      "required": true,
      "order": 1
    },
    {
      "name": "description",
      "label": "Descripción",
      "type": "textarea",
      "placeholder": "Describa brevemente su startup",
      "helpText": "Explique en qué consiste su producto o servicio",
      "required": true,
      "order": 2
    },
    {
      "name": "industry",
      "label": "Industria",
      "type": "select",
      "required": true,
      "order": 3,
      "options": [
        {"label": "Tecnología", "value": "tech"},
        {"label": "Salud", "value": "health"},
        {"label": "Finanzas", "value": "finance"},
        {"label": "Educación", "value": "education"},
        {"label": "Otro", "value": "other"}
      ]
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Formulario creado correctamente",
  "form": {
    "id": 1,
    "title": "Formulario de Postulación",
    "description": "Formulario para postular a nuestro programa de aceleración",
    "acceleratorId": 1,
    "createdBy": 1,
    "formType": "application",
    "config": {
      "showProgressBar": true,
      "allowSave": true,
      "submitButtonText": "Enviar Postulación"
    },
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.000Z",
    "isPublished": true,
    "isActive": true,
    "Fields": [
      {
        "id": 1,
        "formId": 1,
        "name": "startup_name",
        "label": "Nombre de la Startup",
        "type": "text",
        "placeholder": "Ingrese el nombre de su startup",
        "helpText": "Nombre comercial de su empresa o proyecto",
        "required": true,
        "order": 1
      },
      // ... más campos
    ],
    "creator": {
      "id": 1,
      "firstName": "Admin",
      "lastName": "Global",
      "email": "<EMAIL>"
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    }
  }
}
```

### Get All Forms

```
GET /forms
```

**Query Parameters:**
- `page` (default: 1) - Número de página
- `limit` (default: 10) - Número de formularios por página
- `acceleratorId` - Filtrar por aceleradora
- `isPublished` - Filtrar por estado de publicación (true/false)
- `isActive` - Filtrar por estado activo (true/false)
- `formType` - Filtrar por tipo de formulario (application, evaluation, feedback)

**Response:**
```json
{
  "success": true,
  "forms": [
    {
      "id": 1,
      "title": "Formulario de Postulación",
      "description": "Formulario para postular a nuestro programa de aceleración",
      "acceleratorId": 1,
      "createdBy": 1,
      "formType": "application",
      "config": {
        "showProgressBar": true,
        "allowSave": true,
        "submitButtonText": "Enviar Postulación"
      },
      "startDate": "2023-06-01T00:00:00.000Z",
      "endDate": "2023-12-31T23:59:59.000Z",
      "isPublished": true,
      "isActive": true,
      "creator": {
        "id": 1,
        "firstName": "Admin",
        "lastName": "Global",
        "email": "<EMAIL>"
      },
      "accelerator": {
        "id": 1,
        "name": "Startup Accelerator",
        "description": "Accelerator for tech startups"
      }
    }
  ],
  "totalForms": 5,
  "totalPages": 1,
  "currentPage": 1
}
```

### Get Form by ID

```
GET /forms/:id
```

**Response:**
```json
{
  "success": true,
  "form": {
    "id": 1,
    "title": "Formulario de Postulación",
    "description": "Formulario para postular a nuestro programa de aceleración",
    "acceleratorId": 1,
    "createdBy": 1,
    "formType": "application",
    "config": {
      "showProgressBar": true,
      "allowSave": true,
      "submitButtonText": "Enviar Postulación"
    },
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.000Z",
    "isPublished": true,
    "isActive": true,
    "Fields": [
      {
        "id": 1,
        "formId": 1,
        "name": "startup_name",
        "label": "Nombre de la Startup",
        "type": "text",
        "placeholder": "Ingrese el nombre de su startup",
        "helpText": "Nombre comercial de su empresa o proyecto",
        "required": true,
        "order": 1
      },
      // ... más campos
    ],
    "creator": {
      "id": 1,
      "firstName": "Admin",
      "lastName": "Global",
      "email": "<EMAIL>"
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    }
  }
}
```

### Update Form

```
PUT /forms/:id
```

**Request Body:**
```json
{
  "title": "Formulario de Postulación Actualizado",
  "description": "Formulario actualizado para postular a nuestro programa",
  "formType": "application",
  "config": {
    "showProgressBar": true,
    "allowSave": true,
    "submitButtonText": "Enviar"
  },
  "startDate": "2023-06-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.000Z",
  "isPublished": true,
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Formulario actualizado correctamente",
  "form": {
    "id": 1,
    "title": "Formulario de Postulación Actualizado",
    "description": "Formulario actualizado para postular a nuestro programa",
    // ... resto de datos actualizados
  }
}
```

### Delete Form

```
DELETE /forms/:id
```

**Response:**
```json
{
  "success": true,
  "message": "Formulario eliminado correctamente"
}
```

### Create Field

```
POST /forms/fields
```

**Request Body:**
```json
{
  "formId": 1,
  "name": "funding",
  "label": "Financiamiento Actual",
  "type": "select",
  "placeholder": "Seleccione su nivel de financiamiento",
  "helpText": "Indique el nivel de financiamiento que ha recibido hasta ahora",
  "required": true,
  "order": 4,
  "options": [
    {"label": "Pre-seed", "value": "pre-seed"},
    {"label": "Seed", "value": "seed"},
    {"label": "Series A", "value": "series-a"},
    {"label": "Series B o posterior", "value": "series-b-plus"},
    {"label": "Bootstrapped", "value": "bootstrapped"}
  ],
  "validations": {
    "required": true
  },
  "config": {
    "multiple": false
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Campo creado correctamente",
  "field": {
    "id": 4,
    "formId": 1,
    "name": "funding",
    "label": "Financiamiento Actual",
    "type": "select",
    "placeholder": "Seleccione su nivel de financiamiento",
    "helpText": "Indique el nivel de financiamiento que ha recibido hasta ahora",
    "required": true,
    "order": 4,
    "options": [
      {"label": "Pre-seed", "value": "pre-seed"},
      {"label": "Seed", "value": "seed"},
      {"label": "Series A", "value": "series-a"},
      {"label": "Series B o posterior", "value": "series-b-plus"},
      {"label": "Bootstrapped", "value": "bootstrapped"}
    ],
    "validations": {
      "required": true
    },
    "config": {
      "multiple": false
    },
    "isActive": true
  }
}
```

### Get Fields by Form ID

```
GET /forms/:formId/fields
```

**Response:**
```json
{
  "success": true,
  "fields": [
    {
      "id": 1,
      "formId": 1,
      "name": "startup_name",
      "label": "Nombre de la Startup",
      "type": "text",
      "placeholder": "Ingrese el nombre de su startup",
      "helpText": "Nombre comercial de su empresa o proyecto",
      "required": true,
      "order": 1,
      "options": null,
      "validations": null,
      "config": null,
      "isActive": true
    },
    // ... más campos
  ]
}
```

### Update Field

```
PUT /forms/fields/:id
```

**Request Body:**
```json
{
  "label": "Nombre de la Startup o Proyecto",
  "placeholder": "Ingrese el nombre de su startup o proyecto",
  "required": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Campo actualizado correctamente",
  "field": {
    "id": 1,
    "formId": 1,
    "name": "startup_name",
    "label": "Nombre de la Startup o Proyecto",
    "placeholder": "Ingrese el nombre de su startup o proyecto",
    // ... resto de datos actualizados
  }
}
```

### Delete Field

```
DELETE /forms/fields/:id
```

**Response:**
```json
{
  "success": true,
  "message": "Campo eliminado correctamente"
}
```

### Reorder Fields

```
PUT /forms/:formId/fields/reorder
```

**Request Body:**
```json
{
  "fieldOrders": [
    {"id": 1, "order": 2},
    {"id": 2, "order": 1},
    {"id": 3, "order": 3}
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Campos reordenados correctamente",
  "fields": [
    {
      "id": 2,
      "formId": 1,
      "name": "description",
      "label": "Descripción",
      "order": 1,
      // ... otros datos
    },
    {
      "id": 1,
      "formId": 1,
      "name": "startup_name",
      "label": "Nombre de la Startup",
      "order": 2,
      // ... otros datos
    },
    {
      "id": 3,
      "formId": 1,
      "name": "industry",
      "label": "Industria",
      "order": 3,
      // ... otros datos
    }
  ]
}
```

## Application Management API

### Create Application

```
POST /applications
```

**Request Body:**
```json
{
  "formId": 1,
  "responses": {
    "startup_name": "TechInnovate",
    "description": "Plataforma de inteligencia artificial para optimización de procesos industriales",
    "industry": "tech",
    "funding": "seed"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Solicitud enviada correctamente",
  "application": {
    "id": 1,
    "formId": 1,
    "applicantId": 5,
    "acceleratorId": 1,
    "funnelStageId": 1,
    "responses": {
      "startup_name": "TechInnovate",
      "description": "Plataforma de inteligencia artificial para optimización de procesos industriales",
      "industry": "tech",
      "funding": "seed"
    },
    "status": "pending",
    "score": null,
    "internalNotes": null,
    "feedback": null,
    "submittedAt": "2023-06-15T10:30:00.000Z",
    "statusUpdatedAt": null,
    "isActive": true,
    "Form": {
      "id": 1,
      "title": "Formulario de Postulación",
      "description": "Formulario para postular a nuestro programa de aceleración"
    },
    "applicant": {
      "id": 5,
      "firstName": "Juan",
      "lastName": "Pérez",
      "email": "<EMAIL>"
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    },
    "stage": {
      "id": 1,
      "name": "Recibida",
      "description": "Solicitud recibida y pendiente de revisión inicial",
      "order": 1,
      "color": "#3B82F6"
    }
  }
}
```

### Get All Applications

```
GET /applications
```

**Query Parameters:**
- `page` (default: 1) - Número de página
- `limit` (default: 10) - Número de solicitudes por página
- `acceleratorId` - Filtrar por aceleradora
- `formId` - Filtrar por formulario
- `status` - Filtrar por estado (pending, reviewing, approved, rejected)
- `funnelStageId` - Filtrar por etapa del embudo
- `startDate` - Filtrar por fecha de envío (desde)
- `endDate` - Filtrar por fecha de envío (hasta)

**Response:**
```json
{
  "success": true,
  "applications": [
    {
      "id": 1,
      "formId": 1,
      "applicantId": 5,
      "acceleratorId": 1,
      "funnelStageId": 1,
      "responses": {
        "startup_name": "TechInnovate",
        "description": "Plataforma de inteligencia artificial para optimización de procesos industriales",
        "industry": "tech",
        "funding": "seed"
      },
      "status": "pending",
      "submittedAt": "2023-06-15T10:30:00.000Z",
      "Form": {
        "id": 1,
        "title": "Formulario de Postulación"
      },
      "applicant": {
        "id": 5,
        "firstName": "Juan",
        "lastName": "Pérez",
        "email": "<EMAIL>"
      },
      "accelerator": {
        "id": 1,
        "name": "Startup Accelerator"
      },
      "stage": {
        "id": 1,
        "name": "Recibida",
        "color": "#3B82F6"
      }
    }
  ],
  "totalApplications": 25,
  "totalPages": 3,
  "currentPage": 1
}
```

### Get Application by ID

```
GET /applications/:id
```

**Response:**
```json
{
  "success": true,
  "application": {
    "id": 1,
    "formId": 1,
    "applicantId": 5,
    "acceleratorId": 1,
    "funnelStageId": 1,
    "responses": {
      "startup_name": "TechInnovate",
      "description": "Plataforma de inteligencia artificial para optimización de procesos industriales",
      "industry": "tech",
      "funding": "seed"
    },
    "status": "pending",
    "score": null,
    "internalNotes": null,
    "feedback": null,
    "submittedAt": "2023-06-15T10:30:00.000Z",
    "statusUpdatedAt": null,
    "isActive": true,
    "Form": {
      "id": 1,
      "title": "Formulario de Postulación",
      "description": "Formulario para postular a nuestro programa de aceleración",
      "Fields": [
        {
          "id": 1,
          "name": "startup_name",
          "label": "Nombre de la Startup",
          "type": "text"
        },
        // ... más campos
      ]
    },
    "applicant": {
      "id": 5,
      "firstName": "Juan",
      "lastName": "Pérez",
      "email": "<EMAIL>",
      "profileData": {
        "phone": "+34 123 456 789",
        "position": "CEO"
      }
    },
    "accelerator": {
      "id": 1,
      "name": "Startup Accelerator",
      "description": "Accelerator for tech startups"
    },
    "stage": {
      "id": 1,
      "name": "Recibida",
      "description": "Solicitud recibida y pendiente de revisión inicial",
      "order": 1,
      "color": "#3B82F6"
    }
  }
}
```

### Update Application Status

```
PUT /applications/:id/status
```

**Request Body:**
```json
{
  "status": "reviewing",
  "funnelStageId": 2,
  "internalNotes": "Startup interesante con potencial de crecimiento",
  "feedback": "Gracias por su postulación. Estamos revisando su propuesta.",
  "score": 7.5
}
```

**Response:**
```json
{
  "success": true,
  "message": "Estado de solicitud actualizado correctamente",
  "application": {
    "id": 1,
    "status": "reviewing",
    "funnelStageId": 2,
    "internalNotes": "Startup interesante con potencial de crecimiento",
    "feedback": "Gracias por su postulación. Estamos revisando su propuesta.",
    "score": 7.5,
    "statusUpdatedAt": "2023-06-20T15:45:00.000Z",
    // ... resto de datos
  }
}
```

## Funnel Stage API

### Create Funnel Stage

```
POST /funnel-stages
```

**Request Body:**
```json
{
  "name": "Finalista",
  "description": "Startup seleccionada como finalista para el programa",
  "acceleratorId": 1,
  "order": 4,
  "color": "#10B981",
  "config": {
    "requireApproval": true,
    "notifyApplicant": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Etapa del embudo creada correctamente",
  "stage": {
    "id": 6,
    "name": "Finalista",
    "description": "Startup seleccionada como finalista para el programa",
    "acceleratorId": 1,
    "order": 4,
    "color": "#10B981",
    "config": {
      "requireApproval": true,
      "notifyApplicant": true
    },
    "isActive": true
  }
}
```

### Get Funnel Stages by Accelerator

```
GET /funnel-stages/accelerator/:acceleratorId
```

**Response:**
```json
{
  "success": true,
  "stages": [
    {
      "id": 1,
      "name": "Recibida",
      "description": "Solicitud recibida y pendiente de revisión inicial",
      "acceleratorId": 1,
      "order": 1,
      "color": "#3B82F6",
      "config": {},
      "isActive": true,
      "applicationCount": 15
    },
    {
      "id": 2,
      "name": "Revisión",
      "description": "En proceso de evaluación por el equipo",
      "acceleratorId": 1,
      "order": 2,
      "color": "#8B5CF6",
      "config": {},
      "isActive": true,
      "applicationCount": 8
    },
    // ... más etapas
  ]
}
```

### Get Funnel Stage by ID

```
GET /funnel-stages/:id
```

**Response:**
```json
{
  "success": true,
  "stage": {
    "id": 1,
    "name": "Recibida",
    "description": "Solicitud recibida y pendiente de revisión inicial",
    "acceleratorId": 1,
    "order": 1,
    "color": "#3B82F6",
    "config": {},
    "isActive": true,
    "applicationCount": 15
  }
}
```

### Update Funnel Stage

```
PUT /funnel-stages/:id
```

**Request Body:**
```json
{
  "name": "Recibida y Confirmada",
  "description": "Solicitud recibida, confirmada y pendiente de revisión",
  "color": "#4B93F6",
  "config": {
    "autoNotify": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Etapa del embudo actualizada correctamente",
  "stage": {
    "id": 1,
    "name": "Recibida y Confirmada",
    "description": "Solicitud recibida, confirmada y pendiente de revisión",
    "acceleratorId": 1,
    "order": 1,
    "color": "#4B93F6",
    "config": {
      "autoNotify": true
    },
    "isActive": true
  }
}
```

### Delete Funnel Stage

```
DELETE /funnel-stages/:id
```

**Response:**
```json
{
  "success": true,
  "message": "Etapa del embudo eliminada correctamente"
}
```

### Reorder Funnel Stages

```
PUT /funnel-stages/accelerator/:acceleratorId/reorder
```

**Request Body:**
```json
{
  "stageOrders": [
    {"id": 1, "order": 1},
    {"id": 3, "order": 2},
    {"id": 2, "order": 3}
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Etapas reordenadas correctamente",
  "stages": [
    {
      "id": 1,
      "name": "Recibida",
      "order": 1,
      // ... otros datos
    },
    {
      "id": 3,
      "name": "Entrevista",
      "order": 2,
      // ... otros datos
    },
    {
      "id": 2,
      "name": "Revisión",
      "order": 3,
      // ... otros datos
    }
  ]
}
```

### Initialize Default Stages

```
POST /funnel-stages/accelerator/:acceleratorId/init-default
```

**Response:**
```json
{
  "success": true,
  "message": "Etapas predeterminadas inicializadas correctamente",
  "stages": [
    {
      "id": 1,
      "name": "Recibida",
      "description": "Solicitud recibida y pendiente de revisión inicial",
      "acceleratorId": 1,
      "order": 1,
      "color": "#3B82F6",
      "config": {},
      "isActive": true
    },
    // ... más etapas predeterminadas
  ]
}
```
