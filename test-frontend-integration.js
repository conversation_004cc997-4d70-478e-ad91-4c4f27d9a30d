/**
 * Script de Pruebas de Integración Frontend-Backend
 * Proyecto Bumeran - Verificación Completa del Sistema
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuración de la API
const API_BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:5173';

// Credenciales de prueba
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Función para logging con colores
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Función para crear encabezados de sección
function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`${title}`, 'bold');
  console.log('='.repeat(60));
}

// Función para mostrar resultados de pruebas
function logTest(testName, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const color = passed ? 'green' : 'red';
  log(`${status} ${testName}`, color);
  if (details) {
    log(`   ${details}`, 'yellow');
  }
}

// Variables globales para almacenar datos de prueba
let authToken = null;
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  tests: []
};

// Función para registrar resultado de prueba
function recordTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
  testResults.tests.push({ name, passed, details });
  logTest(name, passed, details);
}

// Función para verificar si los servicios están ejecutándose
async function checkServices() {
  logSection('🔍 VERIFICACIÓN DE SERVICIOS');

  try {
    // Verificar Backend usando la ruta raíz
    const backendResponse = await axios.get('http://localhost:5000/', { timeout: 5000 });
    recordTest('Backend disponible', backendResponse.status === 200, `Puerto 5000 - Status: ${backendResponse.status}`);
  } catch (error) {
    recordTest('Backend disponible', false, `Error: ${error.message}`);
    return false;
  }

  try {
    // Verificar Frontend (simulando una petición HTTP)
    const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
    recordTest('Frontend disponible', frontendResponse.status === 200, `Puerto 5173 - Status: ${frontendResponse.status}`);
  } catch (error) {
    recordTest('Frontend disponible', false, `Error: ${error.message}`);
  }

  return true;
}

// Función para probar autenticación
async function testAuthentication() {
  logSection('🔐 PRUEBAS DE AUTENTICACIÓN');

  try {
    // Probar login con credenciales correctas
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    const loginSuccess = loginResponse.status === 200 && loginResponse.data.token;

    if (loginSuccess) {
      authToken = loginResponse.data.token;
      recordTest('Login exitoso', true, `Token recibido: ${authToken.substring(0, 20)}...`);
      recordTest('Datos de usuario recibidos', !!loginResponse.data.user, `Usuario: ${loginResponse.data.user?.email}`);
      recordTest('Rol de administrador', loginResponse.data.user?.role === 'GLOBAL_ADMIN', `Rol: ${loginResponse.data.user?.role}`);
    } else {
      recordTest('Login exitoso', false, 'No se recibió token de autenticación');
      return false;
    }

    // Probar acceso a perfil con token
    const profileResponse = await axios.get(`${API_BASE_URL}/auth/profile`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    recordTest('Acceso a perfil autenticado', profileResponse.status === 200, `Email: ${profileResponse.data.email}`);

  } catch (error) {
    recordTest('Autenticación', false, `Error: ${error.response?.data?.message || error.message}`);
    return false;
  }

  try {
    // Probar login con credenciales incorrectas
    const badLoginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    recordTest('Rechazo de credenciales incorrectas', false, 'Debería haber fallado pero no lo hizo');
  } catch (error) {
    recordTest('Rechazo de credenciales incorrectas', error.response?.status === 401, `Status: ${error.response?.status}`);
  }

  return true;
}

// Función para probar gestión de aceleradoras
async function testAcceleratorManagement() {
  logSection('🚀 PRUEBAS DE GESTIÓN DE ACELERADORAS');

  if (!authToken) {
    recordTest('Gestión de aceleradoras', false, 'No hay token de autenticación');
    return false;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Listar aceleradoras
    const listResponse = await axios.get(`${API_BASE_URL}/accelerators`, { headers });
    recordTest('Listar aceleradoras', listResponse.status === 200, `Encontradas: ${listResponse.data.accelerators?.length || 0}`);

    // Crear nueva aceleradora
    const newAccelerator = {
      name: `Test Accelerator ${Date.now()}`,
      description: 'Aceleradora de prueba para testing',
      website: 'https://test-accelerator.com',
      status: 'ACTIVE'
    };

    const createResponse = await axios.post(`${API_BASE_URL}/accelerators`, newAccelerator, { headers });
    const createdAccelerator = createResponse.data;
    recordTest('Crear aceleradora', createResponse.status === 201, `ID: ${createdAccelerator.id}`);

    if (createdAccelerator.id) {
      // Obtener detalles de la aceleradora creada
      const detailsResponse = await axios.get(`${API_BASE_URL}/accelerators/${createdAccelerator.id}`, { headers });
      recordTest('Obtener detalles de aceleradora', detailsResponse.status === 200, `Nombre: ${detailsResponse.data.name}`);

      // Actualizar aceleradora
      const updateData = { ...newAccelerator, name: `${newAccelerator.name} - Updated` };
      const updateResponse = await axios.put(`${API_BASE_URL}/accelerators/${createdAccelerator.id}`, updateData, { headers });
      recordTest('Actualizar aceleradora', updateResponse.status === 200, 'Datos actualizados correctamente');

      // Eliminar aceleradora de prueba
      const deleteResponse = await axios.delete(`${API_BASE_URL}/accelerators/${createdAccelerator.id}`, { headers });
      recordTest('Eliminar aceleradora', deleteResponse.status === 200, 'Aceleradora eliminada correctamente');
    }

  } catch (error) {
    recordTest('Gestión de aceleradoras', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

// Función para probar el Form Builder
async function testFormBuilder() {
  logSection('📝 PRUEBAS DEL FORM BUILDER');

  if (!authToken) {
    recordTest('Form Builder', false, 'No hay token de autenticación');
    return false;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Listar formularios
    const listResponse = await axios.get(`${API_BASE_URL}/forms`, { headers });
    recordTest('Listar formularios', listResponse.status === 200, `Encontrados: ${listResponse.data.forms?.length || 0}`);

    // Crear nuevo formulario
    const newForm = {
      title: `Test Form ${Date.now()}`,
      description: 'Formulario de prueba para testing',
      fields: [
        {
          type: 'text',
          label: 'Nombre',
          name: 'name',
          required: true,
          placeholder: 'Ingresa tu nombre'
        },
        {
          type: 'email',
          label: 'Email',
          name: 'email',
          required: true,
          placeholder: '<EMAIL>'
        },
        {
          type: 'select',
          label: 'País',
          name: 'country',
          required: false,
          options: ['México', 'Colombia', 'Argentina', 'Chile']
        }
      ],
      status: 'DRAFT'
    };

    const createResponse = await axios.post(`${API_BASE_URL}/forms`, newForm, { headers });
    const createdForm = createResponse.data;
    recordTest('Crear formulario', createResponse.status === 201, `ID: ${createdForm.id}`);

    if (createdForm.id) {
      // Obtener detalles del formulario
      const detailsResponse = await axios.get(`${API_BASE_URL}/forms/${createdForm.id}`, { headers });
      recordTest('Obtener detalles de formulario', detailsResponse.status === 200, `Título: ${detailsResponse.data.title}`);

      // Publicar formulario
      const publishResponse = await axios.patch(`${API_BASE_URL}/forms/${createdForm.id}/publish`, {}, { headers });
      recordTest('Publicar formulario', publishResponse.status === 200, 'Formulario publicado correctamente');

      // Eliminar formulario de prueba
      const deleteResponse = await axios.delete(`${API_BASE_URL}/forms/${createdForm.id}`, { headers });
      recordTest('Eliminar formulario', deleteResponse.status === 200, 'Formulario eliminado correctamente');
    }

  } catch (error) {
    recordTest('Form Builder', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

// Función para probar gestión de aplicaciones
async function testApplicationManagement() {
  logSection('📋 PRUEBAS DE GESTIÓN DE APLICACIONES');

  if (!authToken) {
    recordTest('Gestión de aplicaciones', false, 'No hay token de autenticación');
    return false;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Listar aplicaciones
    const listResponse = await axios.get(`${API_BASE_URL}/applications`, { headers });
    recordTest('Listar aplicaciones', listResponse.status === 200, `Encontradas: ${listResponse.data.applications?.length || 0}`);

    // Listar etapas del embudo
    const stagesResponse = await axios.get(`${API_BASE_URL}/funnel-stages`, { headers });
    recordTest('Listar etapas del embudo', stagesResponse.status === 200, `Etapas: ${stagesResponse.data.stages?.length || 0}`);

    // Si hay aplicaciones, probar filtros
    if (listResponse.data.applications && listResponse.data.applications.length > 0) {
      const filterResponse = await axios.get(`${API_BASE_URL}/applications?status=PENDING`, { headers });
      recordTest('Filtrar aplicaciones por estado', filterResponse.status === 200, 'Filtros funcionando correctamente');
    }

  } catch (error) {
    recordTest('Gestión de aplicaciones', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

// Función para probar gestión de usuarios
async function testUserManagement() {
  logSection('👥 PRUEBAS DE GESTIÓN DE USUARIOS');

  if (!authToken) {
    recordTest('Gestión de usuarios', false, 'No hay token de autenticación');
    return false;
  }

  const headers = { Authorization: `Bearer ${authToken}` };

  try {
    // Listar usuarios
    const listResponse = await axios.get(`${API_BASE_URL}/users`, { headers });
    recordTest('Listar usuarios', listResponse.status === 200, `Encontrados: ${listResponse.data.users?.length || 0}`);

    // Listar códigos de registro
    const codesResponse = await axios.get(`${API_BASE_URL}/registration-codes`, { headers });
    recordTest('Listar códigos de registro', codesResponse.status === 200, `Códigos: ${codesResponse.data.codes?.length || 0}`);

    // Crear código de registro
    const newCode = {
      code: `TEST${Date.now()}`,
      role: 'ENTREPRENEUR',
      maxUses: 1,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 horas
    };

    const createCodeResponse = await axios.post(`${API_BASE_URL}/registration-codes`, newCode, { headers });
    recordTest('Crear código de registro', createCodeResponse.status === 201, `Código: ${createCodeResponse.data.code}`);

  } catch (error) {
    recordTest('Gestión de usuarios', false, `Error: ${error.response?.data?.message || error.message}`);
  }
}

// Función principal para ejecutar todas las pruebas
async function runAllTests() {
  console.clear();
  logSection('🧪 PRUEBAS DE INTEGRACIÓN FRONTEND-BACKEND - BUMERAN');
  log('Iniciando verificación completa del sistema...', 'blue');

  const startTime = Date.now();

  // Ejecutar todas las pruebas
  const servicesOk = await checkServices();

  if (servicesOk) {
    await testAuthentication();
    await testAcceleratorManagement();
    await testFormBuilder();
    await testApplicationManagement();
    await testUserManagement();
  }

  // Generar reporte final
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  logSection('📊 REPORTE FINAL DE PRUEBAS');
  log(`Tiempo total de ejecución: ${duration} segundos`, 'blue');
  log(`Total de pruebas: ${testResults.total}`, 'blue');
  log(`Pruebas exitosas: ${testResults.passed}`, 'green');
  log(`Pruebas fallidas: ${testResults.failed}`, 'red');

  const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  log(`Tasa de éxito: ${successRate}%`, successRate >= 80 ? 'green' : 'red');

  // Guardar reporte detallado
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${duration}s`,
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: `${successRate}%`
    },
    tests: testResults.tests
  };

  const reportPath = path.join(__dirname, 'frontend-integration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\nReporte detallado guardado en: ${reportPath}`, 'yellow');

  // Mostrar pruebas fallidas si las hay
  if (testResults.failed > 0) {
    logSection('❌ PRUEBAS FALLIDAS');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => {
        log(`• ${test.name}: ${test.details}`, 'red');
      });
  }

  logSection('✅ VERIFICACIÓN COMPLETADA');

  if (successRate >= 90) {
    log('🎉 ¡Sistema completamente funcional! Listo para Sprint 6.', 'green');
  } else if (successRate >= 70) {
    log('⚠️  Sistema mayormente funcional con algunos problemas menores.', 'yellow');
  } else {
    log('🚨 Sistema requiere atención antes de continuar.', 'red');
  }
}

// Ejecutar las pruebas
if (require.main === module) {
  runAllTests().catch(error => {
    log(`Error fatal en las pruebas: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runAllTests, testResults };
