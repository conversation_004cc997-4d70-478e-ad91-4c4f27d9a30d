/**
 * Script para generar datos sintéticos para pruebas
 *
 * Este script crea datos de prueba para todas las tablas principales del sistema:
 * - Roles (ya existen por defecto)
 * - Usuarios (diferentes roles)
 * - Aceleradoras
 * - Eta<PERSON> de embudo (FunnelStages)
 * - Formularios (Forms)
 * - Campos de formulario (Fields)
 * - Solicitudes (Applications)
 * - Códigos de registro (RegistrationCodes)
 */

const bcrypt = require('bcryptjs');
const { faker } = require('@faker-js/faker/locale/es');
const {
  sequelize,
  User,
  Role,
  Accelerator,
  FunnelStage,
  Form,
  Field,
  Application,
  RegistrationCode
} = require('../src/models');

// Configuración
const NUM_ACCELERATORS = 5;
const NUM_ADMIN_USERS = 10;
const NUM_MENTORS = 15;
const NUM_ENTREPRENEURS = 30;
const NUM_FORMS_PER_ACCELERATOR = 3;
const NUM_FIELDS_PER_FORM = 8;
const NUM_APPLICATIONS_PER_FORM = 10;
const NUM_REGISTRATION_CODES = 20;

// Función principal
async function generateTestData() {
  try {
    console.log('Iniciando generación de datos de prueba...');

    // Verificar conexión a la base de datos
    await sequelize.authenticate();
    console.log('Conexión a la base de datos establecida correctamente.');

    // Obtener roles existentes
    const roles = await Role.findAll();
    if (roles.length === 0) {
      console.error('No se encontraron roles. Ejecute primero la inicialización de la base de datos.');
      process.exit(1);
    }

    // Mapear roles por nombre para facilitar su uso
    const roleMap = {};
    roles.forEach(role => {
      roleMap[role.name] = role;
    });

    // 1. Crear aceleradoras
    console.log('\n1. Creando aceleradoras...');
    const accelerators = await createAccelerators(NUM_ACCELERATORS);
    console.log(`✅ ${accelerators.length} aceleradoras creadas.`);

    // 2. Crear usuarios administradores de aceleradoras
    console.log('\n2. Creando usuarios administradores...');
    const adminUsers = await createAdminUsers(NUM_ADMIN_USERS, roleMap['ACCELERATOR_ADMIN'], accelerators);
    console.log(`✅ ${adminUsers.length} administradores creados.`);

    // 3. Crear mentores
    console.log('\n3. Creando mentores...');
    const mentors = await createMentors(NUM_MENTORS, roleMap['MENTOR'], accelerators);
    console.log(`✅ ${mentors.length} mentores creados.`);

    // 4. Crear emprendedores
    console.log('\n4. Creando emprendedores...');
    const entrepreneurs = await createEntrepreneurs(NUM_ENTREPRENEURS, roleMap['ENTREPRENEUR']);
    console.log(`✅ ${entrepreneurs.length} emprendedores creados.`);

    // 5. Crear etapas de embudo para cada aceleradora
    console.log('\n5. Creando etapas de embudo...');
    const funnelStages = await createFunnelStages(accelerators);
    console.log(`✅ Etapas de embudo creadas para ${accelerators.length} aceleradoras.`);

    // 6. Crear formularios para cada aceleradora
    console.log('\n6. Creando formularios...');
    const forms = await createForms(NUM_FORMS_PER_ACCELERATOR, accelerators, adminUsers);
    console.log(`✅ ${forms.length} formularios creados.`);

    // 7. Crear campos para cada formulario
    console.log('\n7. Creando campos de formulario...');
    const fields = await createFields(forms, NUM_FIELDS_PER_FORM);
    console.log(`✅ Campos creados para ${forms.length} formularios.`);

    // 8. Crear solicitudes
    console.log('\n8. Creando solicitudes...');
    const applications = await createApplications(forms, entrepreneurs, accelerators, funnelStages);
    console.log(`✅ ${applications.length} solicitudes creadas.`);

    // 9. Crear códigos de registro
    console.log('\n9. Creando códigos de registro...');
    const registrationCodes = await createRegistrationCodes(NUM_REGISTRATION_CODES, roles, accelerators, adminUsers);
    console.log(`✅ ${registrationCodes.length} códigos de registro creados.`);

    console.log('\n✅ Generación de datos de prueba completada con éxito!');

  } catch (error) {
    console.error('Error al generar datos de prueba:', error);
  } finally {
    // Cerrar conexión a la base de datos
    await sequelize.close();
  }
}

// Función para crear aceleradoras
async function createAccelerators(count) {
  const accelerators = [];

  for (let i = 0; i < count; i++) {
    const accelerator = await Accelerator.create({
      name: `${faker.company.name()} Accelerator`,
      description: faker.company.catchPhrase(),
      logo: faker.image.url(),
      website: faker.internet.url(),
      location: faker.location.city() + ', ' + faker.location.country(),
      contactEmail: faker.internet.email(),
      contactPhone: faker.phone.number(),
      isActive: true,
      additionalData: {
        industry: faker.helpers.arrayElement(['Tecnología', 'Salud', 'Finanzas', 'Educación', 'Sostenibilidad']),
        foundedYear: faker.date.past().getFullYear(),
        programDuration: faker.helpers.arrayElement([3, 4, 6, 12]) + ' meses',
        investmentRange: faker.helpers.arrayElement(['$10K-$50K', '$50K-$100K', '$100K-$500K', 'Hasta $1M']),
        batchSize: faker.number.int({ min: 5, max: 20 })
      }
    });

    accelerators.push(accelerator);
  }

  return accelerators;
}

// Función para crear usuarios administradores
async function createAdminUsers(count, adminRole, accelerators) {
  const adminUsers = [];

  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();

    const admin = await User.create({
      firstName,
      lastName,
      email: faker.internet.email({ firstName, lastName }),
      password: await bcrypt.hash('password123', 10),
      profilePicture: faker.image.avatar(),
      isActive: true,
      roleId: adminRole.id,
      profileData: {
        position: faker.person.jobTitle(),
        bio: faker.person.bio(),
        linkedin: `https://linkedin.com/in/${firstName.toLowerCase()}${lastName.toLowerCase()}`,
        specialties: faker.helpers.arrayElements(['Mentoring', 'Fundraising', 'Marketing', 'Product', 'Technology'], { min: 1, max: 3 })
      }
    });

    // Asignar a una aceleradora aleatoria
    try {
      const randomAccelerator = accelerators[Math.floor(Math.random() * accelerators.length)];
      await randomAccelerator.addAdministrator(admin);
    } catch (error) {
      console.warn(`No se pudo asignar el administrador ${admin.id} a la aceleradora: ${error.message}`);
    }

    adminUsers.push(admin);
  }

  return adminUsers;
}

// Función para crear mentores
async function createMentors(count, mentorRole, accelerators) {
  const mentors = [];

  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();

    const mentor = await User.create({
      firstName,
      lastName,
      email: faker.internet.email({ firstName, lastName }),
      password: await bcrypt.hash('password123', 10),
      profilePicture: faker.image.avatar(),
      isActive: true,
      roleId: mentorRole.id,
      profileData: {
        position: faker.person.jobTitle(),
        company: faker.company.name(),
        bio: faker.person.bio(),
        expertise: faker.helpers.arrayElements(['Marketing', 'Ventas', 'Tecnología', 'Finanzas', 'Operaciones', 'Producto'], { min: 1, max: 3 }),
        linkedin: `https://linkedin.com/in/${firstName.toLowerCase()}${lastName.toLowerCase()}`,
        yearsOfExperience: faker.number.int({ min: 3, max: 25 })
      }
    });

    // Asignar a 1-3 aceleradoras aleatorias
    try {
      const numAccelerators = faker.number.int({ min: 1, max: 3 });
      const shuffled = [...accelerators].sort(() => 0.5 - Math.random());
      const selectedAccelerators = shuffled.slice(0, numAccelerators);

      for (const accelerator of selectedAccelerators) {
        try {
          await accelerator.addAdministrator(mentor);
        } catch (error) {
          console.warn(`No se pudo asignar el mentor ${mentor.id} a la aceleradora ${accelerator.id}: ${error.message}`);
        }
      }
    } catch (error) {
      console.warn(`Error al asignar aceleradoras al mentor ${mentor.id}: ${error.message}`);
    }

    mentors.push(mentor);
  }

  return mentors;
}

// Función para crear emprendedores
async function createEntrepreneurs(count, entrepreneurRole) {
  const entrepreneurs = [];

  for (let i = 0; i < count; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();

    const entrepreneur = await User.create({
      firstName,
      lastName,
      email: faker.internet.email({ firstName, lastName }),
      password: await bcrypt.hash('password123', 10),
      profilePicture: faker.image.avatar(),
      isActive: true,
      roleId: entrepreneurRole.id,
      profileData: {
        startupName: `${faker.company.buzzNoun()} ${faker.company.buzzAdjective()}`,
        industry: faker.helpers.arrayElement(['SaaS', 'Fintech', 'Healthtech', 'Edtech', 'E-commerce', 'AI', 'Blockchain']),
        foundedYear: faker.date.past({ years: 5 }).getFullYear(),
        teamSize: faker.number.int({ min: 1, max: 20 }),
        fundingStage: faker.helpers.arrayElement(['Pre-seed', 'Seed', 'Series A', 'Bootstrapped']),
        pitchDeck: faker.helpers.maybe(() => faker.internet.url(), { probability: 0.7 }),
        website: faker.helpers.maybe(() => faker.internet.url(), { probability: 0.8 })
      }
    });

    entrepreneurs.push(entrepreneur);
  }

  return entrepreneurs;
}

// Función para crear etapas de embudo para cada aceleradora
async function createFunnelStages(accelerators) {
  const allStages = [];

  for (const accelerator of accelerators) {
    // Usar el método de inicialización de etapas predeterminadas
    await FunnelStage.initDefaultStages(accelerator.id);

    // Obtener las etapas creadas
    const stages = await FunnelStage.findAll({
      where: { acceleratorId: accelerator.id }
    });

    allStages.push(...stages);
  }

  return allStages;
}

// Función para crear formularios
async function createForms(countPerAccelerator, accelerators, adminUsers) {
  const forms = [];

  for (const accelerator of accelerators) {
    // Obtener administradores de esta aceleradora
    const admins = await accelerator.getAdministrators();

    for (let i = 0; i < countPerAccelerator; i++) {
      // Seleccionar un administrador aleatorio
      const admin = admins[Math.floor(Math.random() * admins.length)];

      // Determinar si el formulario está publicado
      const isPublished = faker.datatype.boolean(0.7); // 70% de probabilidad de estar publicado

      const form = await Form.create({
        title: faker.helpers.arrayElement([
          'Formulario de Postulación',
          'Aplicación para Startups',
          'Registro de Proyecto',
          'Evaluación Inicial',
          'Perfil de Startup'
        ]) + ' - ' + faker.company.buzzPhrase(),
        description: faker.lorem.paragraph(),
        acceleratorId: accelerator.id,
        createdById: admin.id,
        status: isPublished ? 'published' : 'draft',
        isTemplate: faker.datatype.boolean(0.2), // 20% de probabilidad de ser plantilla
        settings: {
          formType: faker.helpers.arrayElement(['application', 'evaluation', 'feedback']),
          config: {
            showProgressBar: faker.datatype.boolean(),
            allowSave: faker.datatype.boolean(),
            submitButtonText: 'Enviar Solicitud'
          },
          startDate: faker.date.recent({ days: 30 }).toISOString(),
          endDate: faker.date.soon({ days: 60 }).toISOString()
        },
        metadata: {
          tags: faker.helpers.arrayElements(['innovación', 'tecnología', 'sostenibilidad', 'impacto social'], { min: 1, max: 3 }),
          category: faker.helpers.arrayElement(['general', 'tecnología', 'social', 'verde']),
          expectedCompletionTime: faker.helpers.arrayElement(['5-10 min', '10-15 min', '15-30 min'])
        }
      });

      forms.push(form);
    }
  }

  return forms;
}

// Función para crear campos de formulario
async function createFields(forms, fieldsPerForm) {
  const allFields = [];

  // Definir tipos de campos comunes
  const fieldTypes = [
    { type: 'text', name: 'name', label: 'Nombre del Proyecto' },
    { type: 'textarea', name: 'description', label: 'Descripción' },
    { type: 'email', name: 'contact_email', label: 'Email de Contacto' },
    { type: 'select', name: 'industry', label: 'Industria',
      options: [
        { label: 'Tecnología', value: 'tech' },
        { label: 'Salud', value: 'health' },
        { label: 'Finanzas', value: 'finance' },
        { label: 'Educación', value: 'education' },
        { label: 'Sostenibilidad', value: 'sustainability' }
      ]
    },
    { type: 'select', name: 'funding_stage', label: 'Etapa de Financiamiento',
      options: [
        { label: 'Pre-seed', value: 'pre-seed' },
        { label: 'Seed', value: 'seed' },
        { label: 'Series A', value: 'series-a' },
        { label: 'Series B o posterior', value: 'series-b-plus' },
        { label: 'Bootstrapped', value: 'bootstrapped' }
      ]
    },
    { type: 'number', name: 'team_size', label: 'Tamaño del Equipo' },
    { type: 'textarea', name: 'problem', label: 'Problema que Resuelve' },
    { type: 'textarea', name: 'solution', label: 'Solución Propuesta' },
    { type: 'textarea', name: 'business_model', label: 'Modelo de Negocio' },
    { type: 'textarea', name: 'market', label: 'Mercado Objetivo' },
    { type: 'textarea', name: 'competition', label: 'Competencia' },
    { type: 'textarea', name: 'traction', label: 'Tracción Actual' },
    { type: 'checkbox', name: 'interests', label: 'Áreas de Interés',
      options: [
        { label: 'Mentoría', value: 'mentoring' },
        { label: 'Financiamiento', value: 'funding' },
        { label: 'Networking', value: 'networking' },
        { label: 'Acceso a Mercado', value: 'market_access' },
        { label: 'Desarrollo de Producto', value: 'product_development' }
      ]
    },
    { type: 'radio', name: 'previous_accelerator', label: '¿Has participado en otra aceleradora?',
      options: [
        { label: 'Sí', value: 'yes' },
        { label: 'No', value: 'no' }
      ]
    },
    { type: 'date', name: 'founded_date', label: 'Fecha de Fundación' }
  ];

  for (const form of forms) {
    // Mezclar y seleccionar campos aleatorios
    const shuffledFields = [...fieldTypes].sort(() => 0.5 - Math.random());
    const selectedFields = shuffledFields.slice(0, fieldsPerForm);

    // Crear los campos seleccionados
    for (let i = 0; i < selectedFields.length; i++) {
      const fieldTemplate = selectedFields[i];

      const field = await Field.create({
        formId: form.id,
        name: fieldTemplate.name,
        label: fieldTemplate.label,
        type: fieldTemplate.type,
        placeholder: `Ingrese ${fieldTemplate.label.toLowerCase()}`,
        helpText: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.7 }),
        required: faker.datatype.boolean(0.8), // 80% de probabilidad de ser requerido
        order: i,
        options: fieldTemplate.options || null,
        validations: fieldTemplate.type === 'number' ? { min: 1, max: 1000 } : null,
        config: {},
        isActive: true
      });

      allFields.push(field);
    }
  }

  return allFields;
}

// Función para crear solicitudes
async function createApplications(forms, entrepreneurs, accelerators, funnelStages) {
  console.log('Saltando creación de solicitudes debido a incompatibilidad de esquema...');
  return [];

  // Esta función está deshabilitada porque la tabla Applications no tiene las columnas necesarias
}

// Función para crear códigos de registro
async function createRegistrationCodes(count, roles, accelerators, adminUsers) {
  console.log('Saltando creación de códigos de registro debido a incompatibilidad de esquema...');
  return [];

  // Esta función está deshabilitada porque la tabla RegistrationCodes no tiene la columna createdBy
}

// Ejecutar el script
generateTestData();
