# Sistema de Formularios y Gestión de Aplicaciones

Este documento describe el sistema de formularios dinámicos y gestión de aplicaciones implementado en Bumeran.

## Descripción General

El sistema permite a los administradores de aceleradoras crear formularios personalizados para recopilar información de los emprendedores. Los formularios pueden ser utilizados para diferentes propósitos, como postulaciones, evaluaciones o feedback.

Una vez que los emprendedores completan y envían un formulario, se crea una aplicación que puede ser revisada y evaluada por los administradores de la aceleradora.

## Componentes Principales

### 1. Form Builder (Constructor de Formularios)

Permite a los administradores:
- Crear formularios con diferentes tipos de campos
- Personalizar la apariencia y comportamiento de los formularios
- Establecer validaciones para los campos
- Publicar o despublicar formularios
- Establecer fechas de inicio y fin para la disponibilidad de los formularios

### 2. Application Management (Gestión de Aplicaciones)

Permite a los administradores:
- Ver todas las aplicaciones recibidas
- Filtrar aplicaciones por diferentes criterios
- Evaluar y puntuar aplicaciones
- Mover aplicaciones a través de diferentes etapas del embudo de selección
- Proporcionar feedback a los aplicantes

### 3. Funnel Stages (Etapas del Embudo)

Permite a los administradores:
- Definir etapas personalizadas para el proceso de selección
- Asignar colores y descripciones a cada etapa
- Reordenar las etapas según sea necesario
- Ver estadísticas de aplicaciones en cada etapa

## Modelos de Datos

### Form (Formulario)

Representa un formulario creado por un administrador.

Atributos principales:
- `title`: Título del formulario
- `description`: Descripción del formulario
- `acceleratorId`: ID de la aceleradora a la que pertenece
- `createdBy`: ID del usuario que creó el formulario
- `formType`: Tipo de formulario (application, evaluation, feedback)
- `config`: Configuración adicional del formulario (JSON)
- `startDate`: Fecha de inicio de disponibilidad
- `endDate`: Fecha de fin de disponibilidad
- `isPublished`: Indica si el formulario está publicado
- `isActive`: Indica si el formulario está activo

### Field (Campo)

Representa un campo dentro de un formulario.

Atributos principales:
- `formId`: ID del formulario al que pertenece
- `name`: Nombre único del campo (para identificación)
- `label`: Etiqueta visible para el usuario
- `type`: Tipo de campo (text, textarea, number, email, date, select, radio, checkbox, file)
- `placeholder`: Texto de ejemplo
- `helpText`: Texto de ayuda para el usuario
- `required`: Indica si el campo es obligatorio
- `order`: Orden del campo en el formulario
- `options`: Opciones para campos de tipo select, radio, checkbox (JSON)
- `validations`: Validaciones adicionales (JSON)
- `config`: Configuración adicional del campo (JSON)
- `isActive`: Indica si el campo está activo

### FunnelStage (Etapa del Embudo)

Representa una etapa en el proceso de selección.

Atributos principales:
- `name`: Nombre de la etapa
- `description`: Descripción de la etapa
- `acceleratorId`: ID de la aceleradora a la que pertenece
- `order`: Orden de la etapa en el embudo
- `color`: Color para representar visualmente la etapa
- `config`: Configuración adicional de la etapa (JSON)
- `isActive`: Indica si la etapa está activa

### Application (Aplicación)

Representa una solicitud enviada por un emprendedor.

Atributos principales:
- `formId`: ID del formulario al que corresponde
- `applicantId`: ID del usuario que envió la solicitud
- `acceleratorId`: ID de la aceleradora a la que se envía
- `funnelStageId`: ID de la etapa actual del embudo
- `responses`: Respuestas del formulario (JSON)
- `status`: Estado de la solicitud (pending, reviewing, approved, rejected)
- `score`: Puntuación de la solicitud
- `internalNotes`: Notas internas sobre la solicitud
- `feedback`: Feedback para el solicitante
- `submittedAt`: Fecha de envío de la solicitud
- `statusUpdatedAt`: Fecha de última actualización del estado
- `isActive`: Indica si la solicitud está activa

## Flujo de Trabajo

1. Un administrador de aceleradora crea un formulario con los campos necesarios
2. El administrador publica el formulario para hacerlo disponible
3. Los emprendedores completan y envían el formulario
4. Se crea una aplicación con las respuestas del emprendedor
5. La aplicación se asigna a la primera etapa del embudo de selección
6. Los administradores revisan, evalúan y mueven las aplicaciones a través de las etapas del embudo
7. Los administradores proporcionan feedback a los emprendedores

## Permisos

- **GLOBAL_ADMIN**: Acceso completo a todos los formularios y aplicaciones
- **ACCELERATOR_ADMIN**: Acceso a formularios y aplicaciones de su aceleradora
- **MENTOR**: Acceso de lectura a aplicaciones de su aceleradora
- **ENTREPRENEUR**: Acceso para completar formularios publicados y ver sus propias aplicaciones

## API Endpoints

Ver la documentación completa de la API en `API.md`.
