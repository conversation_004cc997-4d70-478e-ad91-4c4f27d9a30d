const { User, Role, RegistrationCode, Accelerator } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');
const csv = require('csv-parser');
const streamifier = require('streamifier');
const EmailService = require('../services/email.service');

// Obtener todos los usuarios (con paginación, filtros y búsqueda)
exports.getAllUsers = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const roleId = req.query.roleId;
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'DESC';

    // Construir condiciones de búsqueda
    const whereConditions = {};

    // Búsqueda por nombre, apellido o email
    if (search) {
      whereConditions[Op.or] = [
        { firstName: { [Op.like]: `%${search}%` } },
        { lastName: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } },
      ];
    }

    // Filtro por rol
    if (roleId) {
      whereConditions.roleId = roleId;
    }

    // Filtro por estado (activo/inactivo)
    if (status !== undefined) {
      whereConditions.isActive = status === 'active';
    }

    // Validar campo de ordenamiento
    const validSortFields = ['firstName', 'lastName', 'email', 'createdAt', 'lastLogin'];
    const orderField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Validar dirección de ordenamiento
    const orderDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await User.findAndCountAll({
      where: whereConditions,
      include: [{ model: Role }],
      attributes: { exclude: ['password'] },
      limit,
      offset,
      order: [[orderField, orderDirection]],
    });

    res.json({
      success: true,
      users: rows,
      totalUsers: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener usuarios:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener usuarios',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Import users from CSV
exports.importUsersFromCSV = async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: 'No CSV file uploaded.' });
  }

  const results = [];
  const errors = [];
  let processedCount = 0;
  const importedUsers = [];

  streamifier.createReadStream(req.file.buffer)
    .pipe(csv())
    .on('data', (data) => results.push(data))
    .on('end', async () => {
      for (const row of results) {
        processedCount++;
        try {
          const { firstName, lastName, email, roleName, acceleratorId: csvAcceleratorId } = row;

          if (!firstName || !lastName || !email || !roleName) {
            errors.push({ row: processedCount, message: 'Missing required fields (firstName, lastName, email, roleName).', data: row });
            continue;
          }

          const existingUser = await User.findOne({ where: { email } });
          if (existingUser) {
            errors.push({ row: processedCount, message: `User with email ${email} already exists.`, data: row });
            continue;
          }

          const role = await Role.findOne({ where: { name: roleName.toUpperCase() } });
          if (!role) {
            errors.push({ row: processedCount, message: `Role '${roleName}' not found.`, data: row });
            continue;
          }

          let acceleratorIdForCode = null;
          const rolesRequiringAccelerator = ['MENTOR', 'ENTREPRENEUR', 'STARTUP']; // Define roles that need an accelerator context

          if (req.user.role === 'ACCELERATOR_ADMIN') {
            const adminUser = await User.findByPk(req.user.id);
            acceleratorIdForCode = adminUser.acceleratorId;

            if (role.name === 'GLOBAL_ADMIN') {
              errors.push({ row: processedCount, message: `Accelerator Admins cannot create Global Admins. Email: ${email}`, data: row });
              continue;
            }

            if (rolesRequiringAccelerator.includes(role.name.toUpperCase()) && !acceleratorIdForCode) {
              errors.push({ row: processedCount, message: `Accelerator Admin ${req.user.email} (ID: ${adminUser.id}) is not associated with an accelerator and cannot import users for role ${roleName}. Email: ${email}`, data: row });
              continue;
            }
            // If role is ACCELERATOR_ADMIN, acceleratorIdForCode will be adminUser.acceleratorId (which can be null)
            // This means the new registration code will have the same acceleratorId as the importing admin.
            // If adminUser.acceleratorId is null, new ACCELERATOR_ADMIN code has acceleratorId=null.

          } else if (req.user.role === 'GLOBAL_ADMIN') {
            if (csvAcceleratorId) {
              acceleratorIdForCode = parseInt(csvAcceleratorId);
              if (isNaN(acceleratorIdForCode)) {
                errors.push({ row: processedCount, message: `Invalid acceleratorId format '${csvAcceleratorId}' in CSV for email ${email}.`, data: row });
                continue;
              }
              if (acceleratorIdForCode) { // Check if the parsed ID is a truthy value (not 0 or NaN)
                const accelerator = await Accelerator.findByPk(acceleratorIdForCode);
                if (!accelerator) {
                  errors.push({ row: processedCount, message: `Accelerator with ID ${acceleratorIdForCode} not found for email ${email}.`, data: row });
                  continue;
                }
              } else {
                 // if csvAcceleratorId was provided but parsed to 0 or NaN (e.g. "0" or "abc")
                 // and it's not explicitly allowed to be null for GLOBAL_ADMIN imports for certain roles.
                 // For now, if csvAcceleratorId is provided, it must be a valid, existing ID.
                 // If it's empty/null in CSV, acceleratorIdForCode remains null.
                 if (csvAcceleratorId !== "" && csvAcceleratorId !== null) { // e.g. csvAcceleratorId was "0"
                    errors.push({ row: processedCount, message: `Accelerator ID '${csvAcceleratorId}' is not valid for email ${email}.`, data: row });
                    continue;
                 }
                 acceleratorIdForCode = null; // Ensure it's null if CSV had empty or non-numeric value
              }
            } else {
              // No acceleratorId in CSV for GLOBAL_ADMIN import
              acceleratorIdForCode = null;
            }

            // If role requires an accelerator, but none was provided or found for GLOBAL_ADMIN
            if (rolesRequiringAccelerator.includes(role.name.toUpperCase()) && !acceleratorIdForCode) {
              // This case might be debatable: should GLOBAL_ADMIN be allowed to create MENTORs without accelerator?
              // For now, let's be strict: if role is in rolesRequiringAccelerator, acceleratorIdForCode must be set and valid.
              errors.push({ row: processedCount, message: `Role ${roleName} requires an accelerator, but no valid acceleratorId was provided in CSV for email ${email}.`, data: row });
              continue;
            }
          }

          // Create a registration code for the user
          const newRegCode = await RegistrationCode.create({
            roleId: role.id,
            acceleratorId: acceleratorIdForCode,
            maxUses: 1,
            // expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Example: 30 days expiration
            description: `Generated for CSV import for ${email} (Invited: ${firstName} ${lastName})`,
          });

          // Store minimal info for response, actual user creation happens via registration
          importedUsers.push({
            email,
            firstName,
            lastName,
            roleName: role.name,
            registrationCode: newRegCode.code,
            acceleratorId: acceleratorIdForCode
          });

        } catch (error) {
          errors.push({ row: processedCount, message: `Error processing row: ${error.message}`, data: row });
        }
      }

      res.status(200).json({
        success: true,
        message: 'CSV import process completed.',
        importedCount: importedUsers.length,
        errorCount: errors.length,
        importedUsers: importedUsers,
        errors: errors,
      });
    });
};

// Obtener un usuario por ID
exports.getUserById = async (req, res) => {
  try {
    const userId = req.params.id;

    const user = await User.findByPk(userId, {
      include: [{ model: Role }],
      attributes: { exclude: ['password'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    res.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error('Error al obtener usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Crear un nuevo usuario
exports.createUser = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { firstName, lastName, email, password, roleId, profileData } = req.body;

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'El correo electrónico ya está registrado',
      });
    }

    // Verificar si el rol existe
    const role = await Role.findByPk(roleId);
    if (!role) {
      return res.status(400).json({
        success: false,
        message: 'El rol especificado no existe',
      });
    }

    // Crear nuevo usuario
    const newUser = await User.create({
      firstName,
      lastName,
      email,
      password,
      roleId,
      profileData: profileData || {},
      isActive: true,
    });

    // Obtener usuario creado con su rol
    const createdUser = await User.findByPk(newUser.id, {
      include: [{ model: Role }],
      attributes: { exclude: ['password'] },
    });

    res.status(201).json({
      success: true,
      message: 'Usuario creado correctamente',
      user: createdUser,
    });
  } catch (error) {
    console.error('Error al crear usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Actualizar un usuario
exports.updateUser = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const userId = req.params.id;
    const { firstName, lastName, email, roleId, isActive, profileData } = req.body;

    // Verificar si el usuario existe
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    // Verificar si el email ya está en uso por otro usuario
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'El correo electrónico ya está en uso',
        });
      }
    }

    // Verificar si el rol existe (si se está actualizando)
    if (roleId && roleId !== user.roleId) {
      const role = await Role.findByPk(roleId);
      if (!role) {
        return res.status(400).json({
          success: false,
          message: 'El rol especificado no existe',
        });
      }
    }

    // Actualizar usuario
    await user.update({
      firstName: firstName || user.firstName,
      lastName: lastName || user.lastName,
      email: email || user.email,
      roleId: roleId || user.roleId,
      isActive: isActive !== undefined ? isActive : user.isActive,
      profileData: profileData || user.profileData,
    });

    // Obtener información actualizada con el rol
    const updatedUser = await User.findByPk(userId, {
      include: [{ model: Role }],
      attributes: { exclude: ['password'] },
    });

    res.json({
      success: true,
      message: 'Usuario actualizado correctamente',
      user: updatedUser,
    });
  } catch (error) {
    console.error('Error al actualizar usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Cambiar contraseña de usuario
exports.changePassword = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const userId = req.params.id;
    const { currentPassword, newPassword } = req.body;

    // Verificar si el usuario existe
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    // Verificar si es el propio usuario quien cambia su contraseña
    if (req.user.id.toString() === userId) {
      // Verificar contraseña actual
      const isPasswordValid = await user.comparePassword(currentPassword);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Contraseña actual incorrecta',
        });
      }
    } else {
      // Si no es el propio usuario, verificar si es un administrador
      if (req.user.role !== 'GLOBAL_ADMIN') {
        return res.status(403).json({
          success: false,
          message: 'No tiene permisos para cambiar la contraseña de otro usuario',
        });
      }
    }

    // Actualizar contraseña
    user.password = newPassword;
    await user.save();

    res.json({
      success: true,
      message: 'Contraseña actualizada correctamente',
    });
  } catch (error) {
    console.error('Error al cambiar contraseña:', error);
    res.status(500).json({
      success: false,
      message: 'Error al cambiar contraseña',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Desactivar un usuario
exports.deactivateUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Verificar si el usuario existe
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    // No permitir desactivar al administrador global principal
    if (user.email === '<EMAIL>') {
      return res.status(403).json({
        success: false,
        message: 'No se puede desactivar al administrador global principal',
      });
    }

    // Desactivar usuario
    await user.update({ isActive: false });

    res.json({
      success: true,
      message: 'Usuario desactivado correctamente',
    });
  } catch (error) {
    console.error('Error al desactivar usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al desactivar usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Activar un usuario
exports.activateUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Verificar si el usuario existe
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    // Activar usuario
    await user.update({ isActive: true });

    res.json({
      success: true,
      message: 'Usuario activado correctamente',
    });
  } catch (error) {
    console.error('Error al activar usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al activar usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener todos los roles
exports.getAllRoles = async (req, res) => {
  try {
    const roles = await Role.findAll({
      order: [['id', 'ASC']],
    });

    res.json({
      success: true,
      roles,
    });
  } catch (error) {
    console.error('Error al obtener roles:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener roles',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener usuarios por rol
exports.getUsersByRole = async (req, res) => {
  try {
    const { role } = req.params;

    // Verificar que el rol existe
    const roleRecord = await Role.findOne({ where: { name: role } });
    if (!roleRecord) {
      return res.status(404).json({
        success: false,
        message: 'Rol no encontrado',
      });
    }

    // Obtener usuarios con ese rol
    const users = await User.findAll({
      where: {
        roleId: roleRecord.id,
        isActive: true // Solo usuarios activos
      },
      include: [{ model: Role }],
      attributes: { exclude: ['password'] },
      order: [['firstName', 'ASC'], ['lastName', 'ASC']],
    });

    res.json({
      success: true,
      users,
      role: roleRecord.name,
      totalUsers: users.length,
    });
  } catch (error) {
    console.error('Error al obtener usuarios por rol:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener usuarios por rol',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
