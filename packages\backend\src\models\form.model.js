const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Form - Representa un formulario dinámico
 *
 * Este modelo define la estructura de los formularios que pueden ser creados
 * por los administradores de aceleradoras para recopilar información de los emprendedores.
 */
const Form = sequelize.define('Form', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  // ID de la aceleradora a la que pertenece el formulario
  acceleratorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Accelerators',
      key: 'id',
    },
  },
  // ID del usuario que creó el formulario
  createdById: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  // Estado del formulario (draft, published, archived, etc.)
  status: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: 'draft',
  },
  // Indica si el formulario es una plantilla
  isTemplate: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  // Configuración adicional del formulario
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
  // Metadatos adicionales
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
  },
}, {
  timestamps: true,
});

module.exports = Form;
