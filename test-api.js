#!/usr/bin/env node

/**
 * Script de pruebas automatizadas para la API de Bumeran
 * Ejecuta pruebas de todas las funcionalidades principales
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';
let entrepreneurToken = '';
let testData = {};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  const color = passed ? 'green' : 'red';
  log(`${status} ${testName}${details ? ' - ' + details : ''}`, color);
}

async function makeRequest(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {}
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

async function testAuthentication() {
  log('\n🔐 Probando Autenticación...', 'blue');

  // Test 1: Login con credenciales correctas
  const loginResult = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'admin123'
  });

  if (loginResult.success && loginResult.data.token) {
    adminToken = loginResult.data.token;
    logTest('Login administrador global', true);
  } else {
    logTest('Login administrador global', false, loginResult.error?.message);
    return false;
  }

  // Test 2: Login con credenciales incorrectas
  const badLoginResult = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'wrongpassword'
  });

  logTest('Login con credenciales incorrectas', !badLoginResult.success);

  return true;
}

async function testAcceleratorManagement() {
  log('\n🚀 Probando Gestión de Aceleradoras...', 'blue');

  // Test 1: Crear aceleradora
  const createResult = await makeRequest('POST', '/accelerators', {
    name: 'Test Accelerator API',
    description: 'Aceleradora creada por script de pruebas',
    website: 'https://test-api.com',
    location: 'Ciudad de México',
    industry: 'Tecnología'
  }, adminToken);

  if (createResult.success) {
    testData.acceleratorId = createResult.data.accelerator.id;
    logTest('Crear aceleradora', true);
  } else {
    logTest('Crear aceleradora', false, createResult.error?.message);
    return false;
  }

  // Test 2: Listar aceleradoras
  const listResult = await makeRequest('GET', '/accelerators', null, adminToken);
  logTest('Listar aceleradoras', listResult.success && Array.isArray(listResult.data.accelerators));

  return true;
}

async function testUserManagement() {
  log('\n👥 Probando Gestión de Usuarios...', 'blue');

  // Test 1: Obtener roles
  const rolesResult = await makeRequest('GET', '/users/roles', null, adminToken);
  if (rolesResult.success) {
    const roles = rolesResult.data.roles || rolesResult.data;
    testData.entrepreneurRoleId = roles.find(r => r.name === 'ENTREPRENEUR')?.id;
    logTest('Obtener roles', true);
  } else {
    logTest('Obtener roles', false, rolesResult.error?.message);
    return false;
  }

  // Test 2: Crear usuario emprendedor
  const createUserResult = await makeRequest('POST', '/users', {
    firstName: 'Test',
    lastName: 'Entrepreneur',
    email: '<EMAIL>',
    password: 'password123',
    roleId: testData.entrepreneurRoleId
  }, adminToken);

  if (createUserResult.success) {
    testData.entrepreneurId = createUserResult.data.user.id;
    logTest('Crear usuario emprendedor', true);
  } else {
    logTest('Crear usuario emprendedor', false, createUserResult.error?.message);
    return false;
  }

  // Test 3: Login del emprendedor
  const entrepreneurLoginResult = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'password123'
  });

  if (entrepreneurLoginResult.success) {
    entrepreneurToken = entrepreneurLoginResult.data.token;
    logTest('Login emprendedor', true);
  } else {
    logTest('Login emprendedor', false, entrepreneurLoginResult.error?.message);
    return false;
  }

  return true;
}

async function testRegistrationCodes() {
  log('\n🎫 Probando Códigos de Registro...', 'blue');

  // Test 1: Crear código de registro
  const createCodeResult = await makeRequest('POST', '/registration-codes', {
    acceleratorId: testData.acceleratorId,
    roleId: testData.entrepreneurRoleId,
    maxUses: 10,
    expiresAt: '2025-12-31T23:59:59.000Z'
  }, adminToken);

  if (createCodeResult.success) {
    testData.registrationCode = createCodeResult.data.registrationCode.code;
    logTest('Crear código de registro', true);
  } else {
    logTest('Crear código de registro', false, createCodeResult.error?.message);
    return false;
  }

  return true;
}

async function testFormBuilder() {
  log('\n📝 Probando Form Builder...', 'blue');

  // Test 1: Crear formulario
  const createFormResult = await makeRequest('POST', '/forms', {
    title: 'Formulario API Test',
    description: 'Formulario creado por script de pruebas',
    acceleratorId: testData.acceleratorId,
    isActive: true
  }, adminToken);

  if (createFormResult.success) {
    testData.formId = createFormResult.data.form.id;
    logTest('Crear formulario', true);
  } else {
    logTest('Crear formulario', false, createFormResult.error?.message);
    return false;
  }

  // Test 2: Agregar campo al formulario
  const addFieldResult = await makeRequest('POST', '/forms/fields', {
    formId: testData.formId,
    name: 'company_name',
    type: 'text',
    label: 'Nombre de la Empresa',
    placeholder: 'Ingrese el nombre de su empresa',
    required: true,
    order: 1
  }, adminToken);

  logTest('Agregar campo al formulario', addFieldResult.success);

  // Test 3: Publicar formulario
  const publishFormResult = await makeRequest('PUT', `/forms/${testData.formId}`, {
    isPublished: true
  }, adminToken);

  logTest('Publicar formulario', publishFormResult.success);

  return true;
}

async function testFunnelStages() {
  log('\n🔄 Probando Etapas del Embudo...', 'blue');

  // Test 1: Inicializar etapas predeterminadas
  const initStagesResult = await makeRequest('POST', `/funnel-stages/accelerator/${testData.acceleratorId}/init-default`, null, adminToken);
  logTest('Inicializar etapas del embudo', initStagesResult.success);

  return true;
}

async function testApplications() {
  log('\n📋 Probando Sistema de Aplicaciones...', 'blue');

  // Test 1: Crear aplicación
  const createAppResult = await makeRequest('POST', '/applications', {
    formId: testData.formId,
    responses: {
      company_name: 'Mi Startup API Test'
    }
  }, entrepreneurToken);

  if (createAppResult.success) {
    testData.applicationId = createAppResult.data.application.id;
    logTest('Crear aplicación', true);
  } else {
    logTest('Crear aplicación', false, createAppResult.error?.message);
    return false;
  }

  // Test 2: Actualizar estado de aplicación
  const updateStatusResult = await makeRequest('PUT', `/applications/${testData.applicationId}/status`, {
    status: 'reviewing',
    funnelStageId: 2,
    score: 9.0,
    internalNotes: 'Excelente propuesta generada por API test',
    feedback: 'Su propuesta muestra gran potencial'
  }, adminToken);

  logTest('Actualizar estado de aplicación', updateStatusResult.success);

  // Test 3: Listar aplicaciones
  const listAppsResult = await makeRequest('GET', '/applications', null, adminToken);
  logTest('Listar aplicaciones', listAppsResult.success && Array.isArray(listAppsResult.data.applications));

  return true;
}

async function runAllTests() {
  log('🧪 Iniciando Pruebas Automatizadas de la API de Bumeran', 'yellow');
  log('=' .repeat(60), 'yellow');

  const tests = [
    { name: 'Autenticación', fn: testAuthentication },
    { name: 'Gestión de Aceleradoras', fn: testAcceleratorManagement },
    { name: 'Gestión de Usuarios', fn: testUserManagement },
    { name: 'Códigos de Registro', fn: testRegistrationCodes },
    { name: 'Form Builder', fn: testFormBuilder },
    { name: 'Etapas del Embudo', fn: testFunnelStages },
    { name: 'Sistema de Aplicaciones', fn: testApplications }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) passedTests++;
    } catch (error) {
      log(`❌ Error en ${test.name}: ${error.message}`, 'red');
    }
  }

  log('\n' + '=' .repeat(60), 'yellow');
  log(`📊 Resumen: ${passedTests}/${totalTests} pruebas pasaron`, passedTests === totalTests ? 'green' : 'yellow');

  if (passedTests === totalTests) {
    log('🎉 ¡Todas las pruebas pasaron exitosamente!', 'green');
  } else {
    log('⚠️  Algunas pruebas fallaron. Revisar los detalles arriba.', 'yellow');
  }

  log('\n📋 Datos de prueba creados:', 'blue');
  console.log(JSON.stringify(testData, null, 2));
}

// Ejecutar las pruebas si el script se ejecuta directamente
if (require.main === module) {
  runAllTests().catch(error => {
    log(`💥 Error fatal: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runAllTests };
