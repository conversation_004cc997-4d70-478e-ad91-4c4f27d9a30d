import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FiAlertTriangle,
  FiPlus,
  FiFilter,
  FiSearch,
  FiCalendar,
  FiUser,
  FiEdit,
  FiTrash2,
  <PERSON>Eye
} from 'react-icons/fi';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import Alert from '../../components/common/Alert';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import FormInput from '../../components/common/FormInput';
import taskService from '../../services/taskService';
import userService from '../../services/user.service';
import useAuth from '../../hooks/useAuth';

const TaskList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [tasks, setTasks] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTasks, setTotalTasks] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Estados para filtros
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    status: '',
    assignedToId: '',
    assignedById: '',
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  });

  // Estados para modales
  const [showNewTaskModal, setShowNewTaskModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // Estados para nueva tarea
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    assignedToId: '',
    dueDate: '',
    status: 'pending'
  });
  const [savingTask, setSavingTask] = useState(false);

  // Estados para datos de selección
  const [users, setUsers] = useState([]);

  useEffect(() => {
    loadTasks();
    loadUsers();
  }, [filters]);

  const loadTasks = async () => {
    try {
      setLoading(true);

      // Aplicar filtros basados en el rol del usuario
      const apiFilters = { ...filters };
      if (user.role === 'MENTOR' || user.role === 'ENTREPRENEUR') {
        // Los mentores y emprendedores solo ven sus tareas asignadas
        apiFilters.assignedToId = user.id;
      }

      const response = await taskService.getAllTasks(apiFilters);
      setTasks(response.tasks || []);
      setTotalPages(response.totalPages || 1);
      setTotalTasks(response.totalTasks || 0);
    } catch (error) {
      console.error('Error al cargar tareas:', error);
      setError('Error al cargar las tareas');
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      // Cargar usuarios para los filtros y selección
      const [mentorsRes, entrepreneursRes] = await Promise.all([
        userService.getUsersByRole('MENTOR'),
        userService.getUsersByRole('ENTREPRENEUR')
      ]);

      const allUsers = [
        ...(mentorsRes.users || []),
        ...(entrepreneursRes.users || [])
      ];

      setUsers(allUsers);
    } catch (error) {
      console.error('Error al cargar usuarios:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset page when filters change
    }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', text: 'Pendiente', icon: FiClock },
      in_progress: { color: 'bg-blue-100 text-blue-800', text: 'En Progreso', icon: FiClock },
      completed: { color: 'bg-green-100 text-green-800', text: 'Completada', icon: FiCheck },
      archived: { color: 'bg-gray-100 text-gray-800', text: 'Archivada', icon: FiCheck }
    };

    const config = statusConfig[status] || { color: 'bg-gray-100 text-gray-800', text: status, icon: FiClock };
    const IconComponent = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <IconComponent className="mr-1 h-3 w-3" />
        {config.text}
      </span>
    );
  };

  const getPriorityIcon = (dueDate) => {
    if (!dueDate) return null;

    const today = new Date();
    const due = new Date(dueDate);
    const diffDays = Math.ceil((due - today) / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return <FiAlertTriangle className="h-4 w-4 text-red-500" title="Vencida" />;
    } else if (diffDays <= 3) {
      return <FiAlertTriangle className="h-4 w-4 text-yellow-500" title="Próxima a vencer" />;
    }

    return null;
  };

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const handleCompleteTask = async (taskId) => {
    try {
      await taskService.completeTask(taskId);
      setSuccess('Tarea marcada como completada');
      loadTasks();
    } catch (error) {
      setError('Error al completar la tarea');
    }
  };

  const handleEditTask = (task) => {
    setSelectedTask(task);
    setNewTask({
      title: task.title,
      description: task.description || '',
      assignedToId: task.assignedToId.toString(),
      dueDate: task.dueDate ? task.dueDate.split('T')[0] : '',
      status: task.status
    });
    setShowEditModal(true);
  };

  const handleDeleteTask = (task) => {
    setSelectedTask(task);
    setShowDeleteDialog(true);
  };

  const executeDelete = async () => {
    try {
      await taskService.deleteTask(selectedTask.id);
      setSuccess('Tarea eliminada correctamente');
      setShowDeleteDialog(false);
      setSelectedTask(null);
      loadTasks();
    } catch (error) {
      setError('Error al eliminar la tarea');
    }
  };

  const handleSaveTask = async () => {
    if (!newTask.title.trim() || !newTask.assignedToId) return;

    try {
      setSavingTask(true);

      const taskData = {
        ...newTask,
        assignedToId: parseInt(newTask.assignedToId),
        dueDate: newTask.dueDate || null
      };

      if (showEditModal && selectedTask) {
        await taskService.updateTask(selectedTask.id, taskData);
        setSuccess('Tarea actualizada correctamente');
      } else {
        await taskService.createTask(taskData);
        setSuccess('Tarea creada correctamente');
      }

      setNewTask({ title: '', description: '', assignedToId: '', dueDate: '', status: 'pending' });
      setShowNewTaskModal(false);
      setShowEditModal(false);
      setSelectedTask(null);
      loadTasks();
    } catch (error) {
      setError(showEditModal ? 'Error al actualizar la tarea' : 'Error al crear la tarea');
    } finally {
      setSavingTask(false);
    }
  };

  const canUserModifyTasks = () => {
    return ['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR'].includes(user.role);
  };

  const canUserModifyTask = (task) => {
    if (!canUserModifyTasks()) return false;

    return (
      user.role === 'GLOBAL_ADMIN' ||
      user.role === 'ACCELERATOR_ADMIN' ||
      task.assignedById === user.id
    );
  };

  return (
    <div className="space-y-6">
      {/* Título y botón de nueva tarea */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gestión de Tareas</h1>
          <p className="mt-1 text-sm text-gray-600">
            Administra y da seguimiento a las tareas del sistema de coaching
          </p>
        </div>

        {canUserModifyTasks() && (
          <Button
            variant="primary"
            onClick={() => setShowNewTaskModal(true)}
            className="flex items-center"
          >
            <FiPlus className="mr-2" />
            Nueva Tarea
          </Button>
        )}
      </div>

      {/* Alertas */}
      {error && (
        <Alert type="error" message={error} onClose={() => setError('')} />
      )}
      {success && (
        <Alert type="success" message={success} onClose={() => setSuccess('')} />
      )}

      {/* Filtros */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Búsqueda */}
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar tareas..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Estado */}
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Todos los estados</option>
            <option value="pending">Pendiente</option>
            <option value="in_progress">En Progreso</option>
            <option value="completed">Completada</option>
            <option value="archived">Archivada</option>
          </select>

          {/* Asignado a (solo para admins) */}
          {['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(user.role) && (
            <select
              value={filters.assignedToId}
              onChange={(e) => handleFilterChange('assignedToId', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Asignado a cualquiera</option>
              {users.map(u => (
                <option key={u.id} value={u.id}>
                  {u.firstName} {u.lastName}
                </option>
              ))}
            </select>
          )}

          {/* Ordenamiento */}
          <select
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              handleFilterChange('sortBy', sortBy);
              handleFilterChange('sortOrder', sortOrder);
            }}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="createdAt-DESC">Más recientes</option>
            <option value="createdAt-ASC">Más antiguas</option>
            <option value="dueDate-ASC">Vencimiento próximo</option>
            <option value="title-ASC">Título A-Z</option>
            <option value="title-DESC">Título Z-A</option>
          </select>
        </div>
      </Card>

      {/* Lista de tareas */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">
            Tareas ({totalTasks})
          </h2>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-12">
            <FiCheck className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No hay tareas</h3>
            <p className="text-gray-600 mb-4">
              {filters.search || filters.status || filters.assignedToId
                ? 'No se encontraron tareas con los filtros aplicados'
                : 'Aún no se han creado tareas en el sistema'}
            </p>
            {canUserModifyTasks() && !filters.search && !filters.status && !filters.assignedToId && (
              <Button
                variant="primary"
                onClick={() => setShowNewTaskModal(true)}
                className="flex items-center mx-auto"
              >
                <FiPlus className="mr-2" />
                Crear Primera Tarea
              </Button>
            )}
          </div>
        ) : (
          <>
            {/* Tabla de tareas */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tarea
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Asignado a
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vencimiento
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Creada
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {tasks.map((task) => (
                    <tr key={task.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getPriorityIcon(task.dueDate)}
                          <div className={getPriorityIcon(task.dueDate) ? 'ml-2' : ''}>
                            <div className="text-sm font-medium text-gray-900">
                              {task.title}
                            </div>
                            {task.description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {task.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(task.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiUser className="h-4 w-4 text-gray-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {task.AssignedTo?.firstName} {task.AssignedTo?.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {task.AssignedTo?.Role?.name}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FiCalendar className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="text-sm text-gray-900">
                            {formatDate(task.dueDate)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(task.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          {/* Botón completar (solo si no está completada) */}
                          {task.status !== 'completed' && task.status !== 'archived' &&
                           (task.assignedToId === user.id || canUserModifyTask(task)) && (
                            <Button
                              variant="success"
                              size="sm"
                              onClick={() => handleCompleteTask(task.id)}
                              className="flex items-center"
                            >
                              <FiCheck className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botón editar */}
                          {canUserModifyTask(task) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditTask(task)}
                              className="flex items-center"
                            >
                              <FiEdit className="h-4 w-4" />
                            </Button>
                          )}

                          {/* Botón eliminar */}
                          {canUserModifyTask(task) && (
                            <Button
                              variant="danger"
                              size="sm"
                              onClick={() => handleDeleteTask(task)}
                              className="flex items-center"
                            >
                              <FiTrash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Paginación */}
            {totalPages > 1 && (
              <div className="mt-6">
                <Pagination
                  currentPage={filters.page}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}
      </Card>

      {/* Modal para nueva tarea */}
      <Modal
        isOpen={showNewTaskModal}
        onClose={() => setShowNewTaskModal(false)}
        title="Crear Nueva Tarea"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="Título"
            id="task-title"
            name="title"
            value={newTask.title}
            onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
            required
            placeholder="Ej: Revisar propuesta de negocio"
          />

          <FormInput
            label="Descripción"
            id="task-description"
            name="description"
            type="textarea"
            rows={3}
            value={newTask.description}
            onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Describe los detalles de la tarea..."
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="task-assigned" className="block text-sm font-medium text-gray-700 mb-1">
                Asignar a <span className="text-red-500">*</span>
              </label>
              <select
                id="task-assigned"
                value={newTask.assignedToId}
                onChange={(e) => setNewTask(prev => ({ ...prev, assignedToId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Seleccionar persona...</option>
                {users.map(u => (
                  <option key={u.id} value={u.id}>
                    {u.firstName} {u.lastName} ({u.Role?.name})
                  </option>
                ))}
              </select>
            </div>

            <FormInput
              label="Fecha de Vencimiento"
              id="task-due-date"
              name="dueDate"
              type="date"
              value={newTask.dueDate}
              onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowNewTaskModal(false)}
              disabled={savingTask}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveTask}
              disabled={!newTask.title.trim() || !newTask.assignedToId || savingTask}
              className="flex items-center"
            >
              {savingTask ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Creando...
                </>
              ) : (
                'Crear Tarea'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Modal para editar tarea */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Editar Tarea"
        size="lg"
      >
        <div className="space-y-4">
          <FormInput
            label="Título"
            id="edit-task-title"
            name="title"
            value={newTask.title}
            onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
            required
            placeholder="Ej: Revisar propuesta de negocio"
          />

          <FormInput
            label="Descripción"
            id="edit-task-description"
            name="description"
            type="textarea"
            rows={3}
            value={newTask.description}
            onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Describe los detalles de la tarea..."
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="edit-task-assigned" className="block text-sm font-medium text-gray-700 mb-1">
                Asignar a <span className="text-red-500">*</span>
              </label>
              <select
                id="edit-task-assigned"
                value={newTask.assignedToId}
                onChange={(e) => setNewTask(prev => ({ ...prev, assignedToId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Seleccionar persona...</option>
                {users.map(u => (
                  <option key={u.id} value={u.id}>
                    {u.firstName} {u.lastName} ({u.Role?.name})
                  </option>
                ))}
              </select>
            </div>

            <FormInput
              label="Fecha de Vencimiento"
              id="edit-task-due-date"
              name="dueDate"
              type="date"
              value={newTask.dueDate}
              onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
              min={new Date().toISOString().split('T')[0]}
            />

            <div>
              <label htmlFor="edit-task-status" className="block text-sm font-medium text-gray-700 mb-1">
                Estado
              </label>
              <select
                id="edit-task-status"
                value={newTask.status}
                onChange={(e) => setNewTask(prev => ({ ...prev, status: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="pending">Pendiente</option>
                <option value="in_progress">En Progreso</option>
                <option value="completed">Completada</option>
                <option value="archived">Archivada</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowEditModal(false)}
              disabled={savingTask}
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveTask}
              disabled={!newTask.title.trim() || !newTask.assignedToId || savingTask}
              className="flex items-center"
            >
              {savingTask ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Actualizando...
                </>
              ) : (
                'Actualizar Tarea'
              )}
            </Button>
          </div>
        </div>
      </Modal>

      {/* Diálogo de confirmación para eliminar */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={executeDelete}
        title="Eliminar Tarea"
        message={`¿Estás seguro de que quieres eliminar la tarea "${selectedTask?.title}"? Esta acción no se puede deshacer.`}
        confirmText="Eliminar"
        variant="danger"
      />
    </div>
  );
};

export default TaskList;
