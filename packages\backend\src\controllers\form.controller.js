const { Form, Field, User, Accelerator } = require('../models');
const { validationResult } = require('express-validator');

/**
 * Controlador para la gestión de formularios
 */

// Crear un nuevo formulario
exports.createForm = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const {
      title,
      description,
      acceleratorId,
      formType,
      config,
      startDate,
      endDate,
      isPublished,
      fields
    } = req.body;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'La aceleradora especificada no existe'
      });
    }

    // Verificar si el usuario tiene permisos para crear formularios en esta aceleradora
    // Si es GLOBAL_ADMIN puede crear en cualquier aceleradora
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para crear formularios en esta aceleradora'
        });
      }
    }

    // Crear el formulario
    const newForm = await Form.create({
      title,
      description,
      acceleratorId,
      createdById: req.user.id,
      status: isPublished ? 'published' : 'draft',
      isTemplate: false,
      settings: {
        formType: formType || 'application',
        config: config || {},
        startDate: startDate ? new Date(startDate).toISOString() : null,
        endDate: endDate ? new Date(endDate).toISOString() : null,
      },
      metadata: {}
    });

    // Si se proporcionaron campos, crearlos
    if (fields && Array.isArray(fields) && fields.length > 0) {
      const fieldsToCreate = fields.map((field, index) => ({
        ...field,
        formId: newForm.id,
        order: field.order || index
      }));

      await Field.bulkCreate(fieldsToCreate);
    }

    // Obtener el formulario creado con sus campos
    const createdForm = await Form.findByPk(newForm.id, {
      include: [
        { model: Field },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Formulario creado correctamente',
      form: createdForm
    });
  } catch (error) {
    console.error('Error al crear formulario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear formulario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener todos los formularios
exports.getAllForms = async (req, res) => {
  try {
    const { acceleratorId, isPublished, isActive, formType } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Construir condiciones de búsqueda
    const where = {};

    if (acceleratorId) {
      where.acceleratorId = acceleratorId;
    }

    if (isPublished !== undefined) {
      where.status = isPublished === 'true' ? 'published' : 'draft';
    }

    // No hay isActive en la tabla, pero podemos usar status != 'archived' como equivalente
    if (isActive !== undefined && isActive === 'false') {
      where.status = 'archived';
    }

    // formType está en settings.formType, no podemos filtrar directamente por él en SQLite

    // Si no es GLOBAL_ADMIN, solo mostrar formularios de aceleradoras que administra
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const userAccelerators = await req.user.getManagedAccelerators();
      const acceleratorIds = userAccelerators.map(acc => acc.id);

      if (acceleratorId) {
        // Si se especificó una aceleradora, verificar que el usuario tenga acceso
        if (!acceleratorIds.includes(parseInt(acceleratorId))) {
          return res.status(403).json({
            success: false,
            message: 'No tienes permisos para ver formularios de esta aceleradora'
          });
        }
      } else {
        // Si no se especificó, mostrar solo de las aceleradoras que administra
        where.acceleratorId = acceleratorIds;
      }
    }

    // Obtener formularios con paginación
    const { count, rows: forms } = await Form.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    res.status(200).json({
      success: true,
      forms,
      totalForms: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page
    });
  } catch (error) {
    console.error('Error al obtener formularios:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener formularios',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener un formulario por ID
exports.getFormById = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar el formulario con sus campos
    const form = await Form.findByPk(id, {
      include: [
        {
          model: Field,
          order: [['order', 'ASC']]
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
      ]
    });

    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    // Verificar permisos si no es GLOBAL_ADMIN
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);

      if (!isAdmin && !form.isPublished) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver este formulario'
        });
      }
    }

    res.status(200).json({
      success: true,
      form
    });
  } catch (error) {
    console.error('Error al obtener formulario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener formulario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Actualizar un formulario
exports.updateForm = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { id } = req.params;
    const {
      title,
      description,
      formType,
      config,
      startDate,
      endDate,
      isPublished,
      isActive
    } = req.body;

    // Buscar el formulario
    const form = await Form.findByPk(id);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);

      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para actualizar este formulario'
        });
      }
    }

    // Obtener los settings actuales
    const currentSettings = form.settings || {};

    // Determinar el nuevo status
    let newStatus = form.status;
    if (isActive !== undefined && isActive === false) {
      newStatus = 'archived';
    } else if (isPublished !== undefined) {
      newStatus = isPublished ? 'published' : 'draft';
    }

    // Actualizar el formulario
    await form.update({
      title: title || form.title,
      description: description !== undefined ? description : form.description,
      status: newStatus,
      settings: {
        ...currentSettings,
        formType: formType || currentSettings.formType || 'application',
        config: config || currentSettings.config || {},
        startDate: startDate ? new Date(startDate).toISOString() : currentSettings.startDate,
        endDate: endDate ? new Date(endDate).toISOString() : currentSettings.endDate,
      }
    });

    // Obtener el formulario actualizado con sus campos
    const updatedForm = await Form.findByPk(id, {
      include: [
        { model: Field },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'Formulario actualizado correctamente',
      form: updatedForm
    });
  } catch (error) {
    console.error('Error al actualizar formulario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar formulario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Eliminar un formulario (desactivar)
exports.deleteForm = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar el formulario
    const form = await Form.findByPk(id);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);

      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para eliminar este formulario'
        });
      }
    }

    // Desactivar el formulario en lugar de eliminarlo (marcar como archivado)
    await form.update({ status: 'archived' });

    res.status(200).json({
      success: true,
      message: 'Formulario eliminado correctamente'
    });
  } catch (error) {
    console.error('Error al eliminar formulario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar formulario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
