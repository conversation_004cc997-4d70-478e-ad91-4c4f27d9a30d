const { sequelize } = require('../config/database');
const { Role, User } = require('../models');
const fs = require('fs');
const path = require('path');

/**
 * Función para sincronizar la base de datos con manejo de errores
 * @param {boolean} forceSync - Si es true, fuerza la recreación de tablas
 * @returns {Promise<boolean>} - true si la sincronización fue exitosa
 */
const syncDatabase = async (forceSync = false) => {
  try {
    if (forceSync) {
      // Recrear todas las tablas (elimina datos existentes)
      await sequelize.sync({ force: true });
      console.log('Base de datos sincronizada (force: true)');
    } else {
      try {
        // Intentar sincronizar con alteración (preserva datos)
        await sequelize.sync({ alter: true });
        console.log('Base de datos sincronizada (alter: true)');
      } catch (alterError) {
        // Si hay un error de restricción única en SQLite
        if (alterError.name === 'SequelizeUniqueConstraintError') {
          console.log('Error de restricción única detectado, intentando sincronización sin alteración...');

          // Intentar sincronizar sin alterar las tablas existentes
          await sequelize.sync({ alter: false });
          console.log('Base de datos sincronizada sin alteraciones');
        } else {
          // Si es otro tipo de error, intentar sincronización simple
          console.log('Error al alterar tablas, intentando sincronización simple...');
          await sequelize.sync();
          console.log('Base de datos sincronizada (sync simple)');
        }
      }
    }
    return true;
  } catch (error) {
    console.error('Error al sincronizar la base de datos:', error);
    return false;
  }
};

// Función para inicializar la base de datos
const initializeDatabase = async (options = {}) => {
  const { forceSync = false } = options;

  try {
    // Crear una copia de seguridad de la base de datos antes de sincronizar
    const dbPath = path.join(__dirname, '../../database.sqlite');
    const backupPath = path.join(__dirname, `../../database.sqlite.backup.${Date.now()}`);

    // Solo crear backup si la base de datos existe
    if (fs.existsSync(dbPath)) {
      try {
        fs.copyFileSync(dbPath, backupPath);
        console.log(`Copia de seguridad de la base de datos creada: ${backupPath}`);
      } catch (backupError) {
        console.warn('No se pudo crear copia de seguridad:', backupError.message);
      }
    }

    // Sincronizar la base de datos
    const syncSuccess = await syncDatabase(forceSync);

    if (!syncSuccess) {
      console.warn('La sincronización de la base de datos falló, continuando con la inicialización...');
    }

    // Inicializar roles (incluso si la sincronización falló)
    try {
      await Role.initRoles();
      console.log('Roles inicializados correctamente');
    } catch (roleError) {
      console.error('Error al inicializar roles:', roleError);
    }

    // Crear o actualizar administrador global
    try {
      const globalAdminRole = await Role.findOne({ where: { name: 'GLOBAL_ADMIN' } });
      if (!globalAdminRole) {
        console.error('No se pudo encontrar el rol de administrador global');
        console.log('Intentando crear el rol GLOBAL_ADMIN...');

        // Crear el rol si no existe
        const newRole = await Role.create({
          name: 'GLOBAL_ADMIN',
          description: 'Administrador global del sistema'
        });

        console.log('Rol GLOBAL_ADMIN creado con ID:', newRole.id);

        // Crear o actualizar el administrador global con el nuevo rol
        await User.createGlobalAdmin(newRole.id);
      } else {
        console.log('Rol GLOBAL_ADMIN encontrado con ID:', globalAdminRole.id);

        // Crear o actualizar el administrador global
        const adminUser = await User.createGlobalAdmin(globalAdminRole.id);

        // Verificación adicional
        if (adminUser && adminUser.roleId === globalAdminRole.id) {
          console.log('Administrador global verificado correctamente');
        } else {
          console.error('Error en la verificación del administrador global');
        }
      }
    } catch (adminError) {
      console.error('Error al crear/actualizar administrador global:', adminError);
    }

    console.log('Base de datos inicializada correctamente');
    return true;
  } catch (error) {
    console.error('Error al inicializar la base de datos:', error);

    // Propagar el error para que el servidor pueda manejarlo
    throw error;
  }
};

module.exports = { initializeDatabase };
