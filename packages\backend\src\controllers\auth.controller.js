const jwt = require('jsonwebtoken');
const { User, Role, RegistrationCode, Accelerator } = require('../models');
const { validationResult } = require('express-validator');
const crypto = require('crypto');
const { Op } = require('sequelize');
const EmailService = require('../services/email.service');

// Configuración de JWT
const JWT_SECRET = process.env.JWT_SECRET || 'bumeran-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const REFRESH_TOKEN_EXPIRES_IN_DAYS = process.env.REFRESH_TOKEN_EXPIRES_IN_DAYS || 7;

// Controlador para registro de usuarios
exports.register = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { firstName, lastName, email, password, roleId, registrationCode, acceleratorId } = req.body;

    // Verificar si el usuario ya existe
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'El correo electrónico ya está registrado',
      });
    }

    // Verificar el código de registro
    if (!registrationCode) {
      return res.status(400).json({
        success: false,
        message: 'Se requiere un código de registro para crear una cuenta',
      });
    }

    // Buscar el código de registro
    const code = await RegistrationCode.findOne({
      where: { code: registrationCode },
      include: [
        { model: Role },
        { model: Accelerator, as: 'accelerator' }
      ]
    });

    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'Código de registro inválido',
      });
    }

    // Verificar si el código es válido
    const validationResult = code.isValid();
    if (!validationResult.valid) {
      return res.status(400).json({
        success: false,
        message: validationResult.reason,
      });
    }

    // Usar el rol del código de registro
    const userRoleId = code.roleId;

    // Verificar si el código está asociado a una aceleradora
    let userAcceleratorId = null;
    if (code.acceleratorId) {
      userAcceleratorId = code.acceleratorId;
    }

    // Crear nuevo usuario
    const newUser = await User.create({
      firstName,
      lastName,
      email,
      password,
      roleId: userRoleId,
      acceleratorId: userAcceleratorId,
    });

    // Marcar el código como usado
    await code.use();

    // Generar token JWT
    const token = jwt.sign(
      { id: newUser.id, email: newUser.email, roleId: newUser.roleId },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Obtener información del rol
    const role = await Role.findByPk(newUser.roleId);

    res.status(201).json({
      success: true,
      message: 'Usuario registrado correctamente',
      user: {
        id: newUser.id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        role: role ? role.name : null,
      },
      token,
    });
  } catch (error) {
    console.error('Error en registro:', error);
    res.status(500).json({
      success: false,
      message: 'Error al registrar usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para logout
exports.logout = async (req, res) => {
  try {
    const userId = req.user.id; // Asumiendo que verifyToken añade el usuario a req

    const user = await User.findByPk(userId);

    if (user) {
      user.refreshToken = null;
      user.refreshTokenExpires = null;
      await user.save();
    }

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    });

    res.status(200).json({ success: true, message: 'Cierre de sesión exitoso' });
  } catch (error) {
    console.error('Error en logout:', error);
    res.status(500).json({
      success: false,
      message: 'Error al cerrar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para refrescar el token
exports.refreshToken = async (req, res) => {
  const refreshToken = req.cookies.refreshToken;

  if (!refreshToken) {
    return res.status(401).json({ success: false, message: 'No refresh token provided' });
  }

  try {
    const user = await User.findOne({
      where: {
        refreshToken: refreshToken,
        refreshTokenExpires: { [Op.gt]: new Date() },
      },
      include: [{ model: Role }],
    });

    if (!user) {
      return res.status(403).json({ success: false, message: 'Invalid or expired refresh token' });
    }

    // Información del rol para el token
    let roleId = user.roleId;
    let roleName = user.Role ? user.Role.name : null;

    // Generar nuevo token JWT
    const newAccessToken = jwt.sign(
      {
        id: user.id,
        email: user.email,
        roleId: roleId,
        role: roleName,
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Opcional: Implementar rolling refresh tokens (generar uno nuevo y actualizarlo)
    // Por ahora, mantenemos el mismo refresh token hasta que expire o se cierre sesión.

    res.json({
      success: true,
      accessToken: newAccessToken,
      user: { // Devolver información actualizada del usuario también es útil
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: roleName,
        roleId: roleId,
        profilePicture: user.profilePicture,
      }
    });
  } catch (error) {
    console.error('Error en refreshToken:', error);
    res.status(500).json({
      success: false,
      message: 'Error al refrescar el token',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para login
exports.login = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { email, password } = req.body;

    // Buscar usuario por email
    const user = await User.findOne({
      where: { email },
      include: [{ model: Role }]
    });

    // Verificar si el usuario existe
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Credenciales inválidas',
      });
    }

    // Verificar si el usuario está activo
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Usuario desactivado. Contacte al administrador',
      });
    }

    // Verificar contraseña
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Credenciales inválidas',
      });
    }

    // Actualizar último login
    user.lastLogin = new Date();
    await user.save();

    // Generar refresh token
    const refreshToken = crypto.randomBytes(64).toString('hex');
    user.refreshToken = refreshToken;
    user.refreshTokenExpires = new Date(Date.now() + REFRESH_TOKEN_EXPIRES_IN_DAYS * 24 * 60 * 60 * 1000);
    await user.save();

    // Establecer cookie HttpOnly para el refresh token
    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: REFRESH_TOKEN_EXPIRES_IN_DAYS * 24 * 60 * 60 * 1000, // milliseconds
    });

    // Generar token JWT
    // Verificar que el usuario tenga un rol asignado
    if (!user.Role && user.roleId) {
      console.warn(`Usuario ${user.id} (${user.email}) tiene roleId ${user.roleId} pero no tiene objeto Role. Intentando obtener el rol...`);
      // Intentar obtener el rol
      const role = await Role.findByPk(user.roleId);
      if (role) {
        user.Role = role;
        console.log(`Rol recuperado: ${role.name}`);
      }
    }

    // Información del rol para el token
    let roleId = user.roleId;
    let roleName = user.Role ? user.Role.name : null;

    // Verificar si el <NAME_EMAIL> y no tiene rol
    if (user.email === '<EMAIL>' && !roleName) {
      console.error(`ADVERTENCIA: El usuario administrador global (${user.email}) no tiene un rol asignado.`);

      // Intentar corregir el rol del administrador global
      const globalAdminRole = await Role.findOne({ where: { name: 'GLOBAL_ADMIN' } });
      if (globalAdminRole) {
        console.log(`Corrigiendo rol del administrador global a GLOBAL_ADMIN (ID: ${globalAdminRole.id})`);
        await user.update({ roleId: globalAdminRole.id });
        user.Role = globalAdminRole;
        roleName = 'GLOBAL_ADMIN';
        roleId = globalAdminRole.id;
      }
    }

    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        roleId: roleId,
        role: roleName
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    res.json({
      success: true,
      message: 'Inicio de sesión exitoso',
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: roleName,
        roleId: roleId,
        profilePicture: user.profilePicture,
        profileData: user.profileData,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
      },
      token,
    });
  } catch (error) {
    console.error('Error en login:', error);
    res.status(500).json({
      success: false,
      message: 'Error al iniciar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para obtener el perfil del usuario actual
exports.getProfile = async (req, res) => {
  try {
    // El middleware de autenticación ya ha verificado el token y añadido el usuario a req
    const userId = req.user.id;

    const user = await User.findByPk(userId, {
      include: [{ model: Role }],
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    // Verificar que el rol esté presente
    if (!user.Role) {
      console.error(`Usuario ${userId} no tiene un rol asociado en la base de datos`);
    } else {
      console.log(`Usuario ${userId} tiene el rol: ${user.Role.name}`);
    }

    res.json({
      success: true,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        profilePicture: user.profilePicture,
        role: user.Role ? user.Role.name : null,
        roleId: user.roleId,
        profileData: user.profileData,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
      },
    });
  } catch (error) {
    console.error('Error al obtener perfil:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener perfil de usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para solicitar restablecimiento de contraseña
exports.forgotPassword = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { email } = req.body;

    // Buscar usuario por email
    const user = await User.findOne({ where: { email } });

    // Si no existe el usuario, no revelar esta información por seguridad
    // pero devolver éxito para evitar enumeración de usuarios
    if (!user) {
      return res.json({
        success: true,
        message: 'Si el correo existe, recibirás instrucciones para restablecer tu contraseña',
      });
    }

    // Generar token aleatorio
    const resetToken = crypto.randomBytes(32).toString('hex');

    // Guardar token y fecha de expiración (1 hora)
    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = new Date(Date.now() + 3600000); // 1 hora
    await user.save();

    // Enviar correo con el token
    await EmailService.sendPasswordResetEmail(user.email, resetToken, user.id);

    res.json({
      success: true,
      message: 'Si el correo existe, recibirás instrucciones para restablecer tu contraseña',
    });
  } catch (error) {
    console.error('Error en solicitud de restablecimiento:', error);
    res.status(500).json({
      success: false,
      message: 'Error al procesar la solicitud',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Controlador para restablecer contraseña
exports.resetPassword = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { token } = req.params;
    const { password } = req.body;

    // Buscar usuario con el token y que no haya expirado
    const user = await User.findOne({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: { [Op.gt]: new Date() }, // Fecha de expiración mayor que ahora
      },
    });

    // Si no existe el usuario o el token ha expirado
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'El enlace de restablecimiento es inválido o ha expirado',
      });
    }

    // Actualizar contraseña
    user.password = password;
    user.resetPasswordToken = null;
    user.resetPasswordExpires = null;
    await user.save();

    res.json({
      success: true,
      message: 'Contraseña restablecida correctamente',
    });
  } catch (error) {
    console.error('Error al restablecer contraseña:', error);
    res.status(500).json({
      success: false,
      message: 'Error al restablecer contraseña',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
