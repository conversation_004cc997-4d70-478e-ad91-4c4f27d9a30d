import React, { useState, useEffect } from 'react';
import {
  FiBell,
  FiX,
  FiCalendar,
  FiTarget,
  FiClock,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';
import Card from '../common/Card';
import Button from '../common/Button';
import { useAuth } from '../../contexts/AuthContext';
import sessionService from '../../services/sessionService';
import taskService from '../../services/taskService';

const NotificationCenter = ({ isOpen, onClose }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (isOpen) {
      loadNotifications();
    }
  }, [isOpen, user]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const notificationsList = [];
      const now = new Date();

      // Obtener sesiones próximas (próximas 24 horas)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const sessionsResponse = await sessionService.getAllSessions({ limit: 50 });
      const sessions = sessionsResponse.sessions || [];

      const upcomingSessions = sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return session.status === 'CONFIRMED' &&
               sessionDate > now &&
               sessionDate <= tomorrow &&
               (session.mentorId === user.id || session.entrepreneurId === user.id);
      });

      upcomingSessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const hoursUntil = Math.ceil((sessionDate - now) / (1000 * 60 * 60));

        notificationsList.push({
          id: `session-${session.id}`,
          type: 'session',
          title: 'Sesión próxima',
          message: `Tienes una sesión "${session.title}" en ${hoursUntil} hora(s)`,
          time: session.startTime,
          priority: hoursUntil <= 2 ? 'high' : 'medium',
          icon: FiCalendar,
          data: session
        });
      });

      // Obtener tareas vencidas y próximas a vencer
      const tasksResponse = await taskService.getAllTasks({ limit: 50 });
      const tasks = tasksResponse.tasks || [];

      const userTasks = tasks.filter(task =>
        task.assignedToId === user.id && task.status !== 'COMPLETED'
      );

      userTasks.forEach(task => {
        const dueDate = new Date(task.dueDate);
        const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

        if (daysUntilDue < 0) {
          // Tarea vencida
          notificationsList.push({
            id: `task-overdue-${task.id}`,
            type: 'task-overdue',
            title: 'Tarea vencida',
            message: `La tarea "${task.title}" venció hace ${Math.abs(daysUntilDue)} día(s)`,
            time: task.dueDate,
            priority: 'high',
            icon: FiAlertCircle,
            data: task
          });
        } else if (daysUntilDue <= 2) {
          // Tarea próxima a vencer
          notificationsList.push({
            id: `task-due-${task.id}`,
            type: 'task-due',
            title: 'Tarea próxima a vencer',
            message: `La tarea "${task.title}" vence en ${daysUntilDue} día(s)`,
            time: task.dueDate,
            priority: daysUntilDue === 0 ? 'high' : 'medium',
            icon: FiTarget,
            data: task
          });
        }
      });

      // Obtener sesiones sin confirmar (solo para mentores y admins)
      if (['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR'].includes(user.role)) {
        const pendingSessions = sessions.filter(session =>
          session.status === 'PENDING' &&
          (session.mentorId === user.id || ['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(user.role))
        );

        pendingSessions.forEach(session => {
          notificationsList.push({
            id: `session-pending-${session.id}`,
            type: 'session-pending',
            title: 'Sesión pendiente de confirmación',
            message: `La sesión "${session.title}" requiere confirmación`,
            time: session.createdAt,
            priority: 'medium',
            icon: FiClock,
            data: session
          });
        });
      }

      // Ordenar por prioridad y fecha
      notificationsList.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return new Date(a.time) - new Date(b.time);
      });

      setNotifications(notificationsList);
      setUnreadCount(notificationsList.length);
    } catch (error) {
      console.error('Error al cargar notificaciones:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (timeString) => {
    return new Date(timeString).toLocaleString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleNotificationClick = (notification) => {
    // Aquí puedes agregar lógica para navegar a la página correspondiente
    console.log('Notification clicked:', notification);
  };

  const markAsRead = (notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose}></div>
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 p-4">
            <div className="flex items-center space-x-2">
              <FiBell className="h-5 w-5 text-gray-600" />
              <h2 className="text-lg font-semibold text-gray-900">Notificaciones</h2>
              {unreadCount > 0 && (
                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                  {unreadCount}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="text-xs"
                >
                  Marcar todas como leídas
                </Button>
              )}
              <Button variant="ghost" size="sm" onClick={onClose}>
                <FiX className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-8 text-center">
                <FiCheckCircle className="h-12 w-12 text-green-500 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">¡Todo al día!</h3>
                <p className="text-gray-600">No tienes notificaciones pendientes</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {notifications.map((notification) => {
                  const IconComponent = notification.icon;
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer border-l-4 ${getPriorityColor(notification.priority)}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 p-2 rounded-full ${getPriorityColor(notification.priority)}`}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900">
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            {formatTime(notification.time)}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            markAsRead(notification.id);
                          }}
                          className="flex-shrink-0"
                        >
                          <FiX className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationCenter;
