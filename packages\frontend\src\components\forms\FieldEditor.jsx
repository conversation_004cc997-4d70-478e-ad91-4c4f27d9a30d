import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { FiSave, FiX, FiPlus, FiTrash2 } from 'react-icons/fi';
import FormInput from '../common/FormInput';
import Button from '../common/Button';
import Card from '../common/Card';

/**
 * Componente para editar un campo de formulario
 */
const FieldEditor = ({ field, onSave, onCancel, isNew = true }) => {
  const [options, setOptions] = useState(field.options || []);
  const [newOption, setNewOption] = useState({ label: '', value: '' });
  const [showOptionForm, setShowOptionForm] = useState(false);

  // Esquema de validación para el campo
  const validationSchema = Yup.object({
    name: Yup.string()
      .required('El nombre es requerido')
      .min(1, 'El nombre debe tener al menos 1 caracter')
      .max(50, 'El nombre no debe exceder los 50 caracteres')
      .matches(/^[a-zA-Z0-9_]+$/, 'El nombre solo puede contener letras, números y guiones bajos'),
    label: Yup.string()
      .required('La etiqueta es requerida')
      .min(1, 'La etiqueta debe tener al menos 1 caracter')
      .max(100, 'La etiqueta no debe exceder los 100 caracteres'),
    type: Yup.string()
      .required('El tipo es requerido')
      .oneOf(['text', 'textarea', 'number', 'email', 'date', 'select', 'radio', 'checkbox', 'file'], 'Tipo no válido'),
    placeholder: Yup.string()
      .max(200, 'El placeholder no debe exceder los 200 caracteres'),
    helpText: Yup.string()
      .max(500, 'El texto de ayuda no debe exceder los 500 caracteres'),
  });

  // Configuración de Formik
  const formik = useFormik({
    initialValues: {
      name: field.name || '',
      label: field.label || '',
      type: field.type || 'text',
      placeholder: field.placeholder || '',
      helpText: field.helpText || '',
      required: field.required || false,
      order: field.order || 0,
      validations: field.validations || {},
      config: field.config || {}
    },
    validationSchema,
    onSubmit: (values) => {
      // Validar que los campos de tipo select, radio y checkbox tengan opciones
      if (['select', 'radio', 'checkbox'].includes(values.type) && (!options || options.length === 0)) {
        alert('Debe agregar al menos una opción para este tipo de campo');
        return;
      }

      // Guardar el campo con sus opciones
      onSave({
        ...values,
        options: ['select', 'radio', 'checkbox'].includes(values.type) ? options : null
      });
    }
  });

  // Añadir una nueva opción
  const handleAddOption = () => {
    if (!newOption.label || !newOption.value) {
      alert('Debe proporcionar una etiqueta y un valor para la opción');
      return;
    }

    setOptions([...options, { ...newOption }]);
    setNewOption({ label: '', value: '' });
    setShowOptionForm(false);
  };

  // Eliminar una opción
  const handleRemoveOption = (index) => {
    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  // Manejar cambio en el tipo de campo
  const handleTypeChange = (e) => {
    const newType = e.target.value;
    formik.setFieldValue('type', newType);
    
    // Resetear opciones si el tipo no las requiere
    if (!['select', 'radio', 'checkbox'].includes(newType)) {
      setOptions([]);
    }
  };

  return (
    <Card>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">
          {isNew ? 'Añadir Nuevo Campo' : 'Editar Campo'}
        </h3>
        <button
          type="button"
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
        >
          <FiX size={24} />
        </button>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <FormInput
            label="Nombre"
            id="name"
            name="name"
            type="text"
            placeholder="nombre_campo"
            value={formik.values.name}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.name && formik.errors.name}
            touched={formik.touched.name}
            required
            helpText="Identificador único del campo (sin espacios ni caracteres especiales)"
          />
          
          <FormInput
            label="Etiqueta"
            id="label"
            name="label"
            type="text"
            placeholder="Etiqueta visible para el usuario"
            value={formik.values.label}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.label && formik.errors.label}
            touched={formik.touched.label}
            required
            helpText="Texto que verá el usuario junto al campo"
          />
          
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Tipo de Campo <span className="text-red-500">*</span>
            </label>
            <select
              id="type"
              name="type"
              value={formik.values.type}
              onChange={handleTypeChange}
              onBlur={formik.handleBlur}
              className={`appearance-none block w-full px-3 py-2 border ${
                formik.touched.type && formik.errors.type 
                  ? 'border-red-300' 
                  : 'border-gray-300'
              } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
            >
              <option value="text">Texto (una línea)</option>
              <option value="textarea">Texto (múltiples líneas)</option>
              <option value="number">Número</option>
              <option value="email">Correo electrónico</option>
              <option value="date">Fecha</option>
              <option value="select">Lista desplegable</option>
              <option value="radio">Opción única (radio)</option>
              <option value="checkbox">Opción múltiple (checkbox)</option>
              <option value="file">Archivo</option>
            </select>
            {formik.touched.type && formik.errors.type && (
              <p className="mt-2 text-sm text-red-600">{formik.errors.type}</p>
            )}
          </div>
          
          <FormInput
            label="Placeholder"
            id="placeholder"
            name="placeholder"
            type="text"
            placeholder="Texto de ejemplo"
            value={formik.values.placeholder}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.placeholder && formik.errors.placeholder}
            touched={formik.touched.placeholder}
            helpText="Texto de ejemplo que aparece cuando el campo está vacío"
          />
          
          <div className="md:col-span-2">
            <FormInput
              label="Texto de Ayuda"
              id="helpText"
              name="helpText"
              type="textarea"
              placeholder="Instrucciones o información adicional para el usuario"
              value={formik.values.helpText}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.helpText && formik.errors.helpText}
              touched={formik.touched.helpText}
              helpText="Texto explicativo que aparecerá debajo del campo"
            />
          </div>
          
          <div className="flex items-center">
            <input
              id="required"
              name="required"
              type="checkbox"
              checked={formik.values.required}
              onChange={formik.handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="required" className="ml-2 block text-sm text-gray-900">
              Campo requerido
            </label>
          </div>
        </div>
        
        {/* Sección de opciones para campos select, radio y checkbox */}
        {['select', 'radio', 'checkbox'].includes(formik.values.type) && (
          <div className="mt-6 border-t border-gray-200 pt-4">
            <h4 className="text-lg font-medium mb-3">Opciones</h4>
            
            {options.length === 0 ? (
              <div className="text-center py-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 mb-4">
                <p className="text-gray-500">No hay opciones definidas</p>
              </div>
            ) : (
              <div className="space-y-2 mb-4">
                {options.map((option, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                    <div>
                      <span className="font-medium">{option.label}</span>
                      <span className="text-gray-500 text-sm ml-2">({option.value})</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveOption(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <FiTrash2 />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            {showOptionForm ? (
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                  <FormInput
                    label="Etiqueta de la opción"
                    id="optionLabel"
                    name="optionLabel"
                    type="text"
                    placeholder="Texto visible"
                    value={newOption.label}
                    onChange={(e) => setNewOption({ ...newOption, label: e.target.value })}
                  />
                  
                  <FormInput
                    label="Valor de la opción"
                    id="optionValue"
                    name="optionValue"
                    type="text"
                    placeholder="Valor interno"
                    value={newOption.value}
                    onChange={(e) => setNewOption({ ...newOption, value: e.target.value })}
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowOptionForm(false)}
                  >
                    Cancelar
                  </Button>
                  
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={handleAddOption}
                  >
                    Añadir
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowOptionForm(true)}
                className="flex items-center"
              >
                <FiPlus className="mr-2" /> Añadir Opción
              </Button>
            )}
          </div>
        )}
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancelar
          </Button>
          
          <Button
            type="submit"
            variant="primary"
            className="flex items-center"
          >
            <FiSave className="mr-2" /> Guardar Campo
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default FieldEditor;
