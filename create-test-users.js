#!/usr/bin/env node

/**
 * Script para crear usuarios de prueba para el Sprint 6
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Credenciales del administrador
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = '';

// Función para hacer peticiones autenticadas
async function apiRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

async function createTestUsers() {
  try {
    console.log('🔐 Autenticando como administrador...');
    
    // Login como administrador
    const authResponse = await apiRequest('POST', '/auth/login', ADMIN_CREDENTIALS);
    if (!authResponse.success || !authResponse.token) {
      throw new Error('No se pudo autenticar');
    }
    
    authToken = authResponse.token;
    console.log('✅ Autenticación exitosa');
    
    // Obtener roles
    console.log('📋 Obteniendo roles...');
    const rolesResponse = await apiRequest('GET', '/users/roles');
    const roles = rolesResponse.roles;
    
    const mentorRole = roles.find(r => r.name === 'MENTOR');
    const entrepreneurRole = roles.find(r => r.name === 'ENTREPRENEUR');
    
    if (!mentorRole || !entrepreneurRole) {
      throw new Error('No se encontraron los roles necesarios');
    }
    
    console.log(`✅ Roles encontrados - MENTOR: ${mentorRole.id}, ENTREPRENEUR: ${entrepreneurRole.id}`);
    
    // Crear usuarios de prueba
    const testUsers = [
      {
        firstName: 'Carlos',
        lastName: 'Mentor',
        email: '<EMAIL>',
        password: 'test123',
        roleId: mentorRole.id
      },
      {
        firstName: 'Ana',
        lastName: 'Mentora',
        email: '<EMAIL>',
        password: 'test123',
        roleId: mentorRole.id
      },
      {
        firstName: 'Luis',
        lastName: 'Emprendedor',
        email: '<EMAIL>',
        password: 'test123',
        roleId: entrepreneurRole.id
      },
      {
        firstName: 'María',
        lastName: 'Startup',
        email: '<EMAIL>',
        password: 'test123',
        roleId: entrepreneurRole.id
      }
    ];
    
    console.log('👥 Creando usuarios de prueba...');
    
    for (const userData of testUsers) {
      try {
        // Verificar si el usuario ya existe
        const existingUsers = await apiRequest('GET', `/users?search=${userData.email}`);
        const userExists = existingUsers.users && existingUsers.users.some(u => u.email === userData.email);
        
        if (userExists) {
          console.log(`⚠️ Usuario ${userData.email} ya existe, saltando...`);
          continue;
        }
        
        const response = await apiRequest('POST', '/users', userData);
        if (response.success) {
          console.log(`✅ Usuario creado: ${userData.firstName} ${userData.lastName} (${userData.email})`);
        } else {
          console.log(`❌ Error al crear usuario ${userData.email}: ${response.message}`);
        }
      } catch (error) {
        console.log(`❌ Error al crear usuario ${userData.email}: ${error.message}`);
      }
    }
    
    console.log('\n🎉 Proceso completado!');
    console.log('\n📋 Usuarios de prueba disponibles:');
    console.log('MENTORES:');
    console.log('  - <EMAIL> / test123');
    console.log('  - <EMAIL> / test123');
    console.log('EMPRENDEDORES:');
    console.log('  - <EMAIL> / test123');
    console.log('  - <EMAIL> / test123');
    console.log('\nADMINISTRADOR:');
    console.log('  - <EMAIL> / admin123');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createTestUsers();
}

module.exports = { createTestUsers };
