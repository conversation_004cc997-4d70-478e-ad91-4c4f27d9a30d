import api from './api';

class TaskService {
  // Obtener todas las tareas con filtros y paginación
  async getAllTasks(filters = {}) {
    try {
      const params = new URLSearchParams({
        page: filters.page || 1,
        limit: filters.limit || 10,
        search: filters.search || '',
        status: filters.status || '',
        assignedToId: filters.assignedToId || '',
        assignedById: filters.assignedById || '',
        acceleratorId: filters.acceleratorId || '',
        sessionId: filters.sessionId || '',
        sortBy: filters.sortBy || 'createdAt',
        sortOrder: filters.sortOrder || 'DESC'
      });

      const response = await api.get(`/tasks?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener tareas:', error);
      throw error;
    }
  }

  // Obtener una tarea por ID
  async getTaskById(taskId) {
    try {
      const response = await api.get(`/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener tarea:', error);
      throw error;
    }
  }

  // Crear una nueva tarea
  async createTask(taskData) {
    try {
      const response = await api.post('/tasks', taskData);
      return response.data;
    } catch (error) {
      console.error('Error al crear tarea:', error);
      throw error;
    }
  }

  // Actualizar una tarea
  async updateTask(taskId, taskData) {
    try {
      const response = await api.put(`/tasks/${taskId}`, taskData);
      return response.data;
    } catch (error) {
      console.error('Error al actualizar tarea:', error);
      throw error;
    }
  }

  // Eliminar una tarea
  async deleteTask(taskId) {
    try {
      const response = await api.delete(`/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error al eliminar tarea:', error);
      throw error;
    }
  }

  // Completar una tarea
  async completeTask(taskId) {
    try {
      const response = await api.patch(`/tasks/${taskId}/complete`);
      return response.data;
    } catch (error) {
      console.error('Error al completar tarea:', error);
      throw error;
    }
  }

  // Obtener tareas de un usuario
  async getUserTasks(userId, filters = {}) {
    try {
      const params = new URLSearchParams({
        page: filters.page || 1,
        limit: filters.limit || 10,
        status: filters.status || ''
      });

      const response = await api.get(`/tasks/user/${userId}?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener tareas del usuario:', error);
      throw error;
    }
  }

  // Obtener estadísticas de tareas
  async getTaskStats(filters = {}) {
    try {
      const params = new URLSearchParams({
        userId: filters.userId || '',
        acceleratorId: filters.acceleratorId || ''
      });

      const response = await api.get(`/tasks/stats?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener estadísticas de tareas:', error);
      throw error;
    }
  }

  // Obtener tareas pendientes
  async getPendingTasks(userId, limit = 10) {
    try {
      const response = await this.getUserTasks(userId, {
        status: 'pending',
        limit
      });
      return response;
    } catch (error) {
      console.error('Error al obtener tareas pendientes:', error);
      throw error;
    }
  }

  // Obtener tareas vencidas
  async getOverdueTasks(userId, limit = 10) {
    try {
      const allTasks = await this.getUserTasks(userId, { limit: 1000 });
      const tasks = allTasks.tasks || [];

      const now = new Date();
      const overdueTasks = tasks.filter(task => {
        if (!task.dueDate || ['completed', 'archived'].includes(task.status)) {
          return false;
        }
        return new Date(task.dueDate) < now;
      });

      return {
        success: true,
        tasks: overdueTasks.slice(0, limit),
        totalTasks: overdueTasks.length
      };
    } catch (error) {
      console.error('Error al obtener tareas vencidas:', error);
      throw error;
    }
  }

  // Obtener tareas próximas a vencer
  async getUpcomingTasks(userId, days = 7, limit = 10) {
    try {
      const allTasks = await this.getUserTasks(userId, { limit: 1000 });
      const tasks = allTasks.tasks || [];

      const now = new Date();
      const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

      const upcomingTasks = tasks.filter(task => {
        if (!task.dueDate || ['completed', 'archived'].includes(task.status)) {
          return false;
        }
        const dueDate = new Date(task.dueDate);
        return dueDate >= now && dueDate <= futureDate;
      });

      return {
        success: true,
        tasks: upcomingTasks.slice(0, limit),
        totalTasks: upcomingTasks.length
      };
    } catch (error) {
      console.error('Error al obtener tareas próximas:', error);
      throw error;
    }
  }

  // Cambiar estado de una tarea
  async changeTaskStatus(taskId, newStatus) {
    try {
      const response = await this.updateTask(taskId, { status: newStatus });
      return response;
    } catch (error) {
      console.error('Error al cambiar estado de tarea:', error);
      throw error;
    }
  }

  // Asignar tarea a otro usuario
  async reassignTask(taskId, newAssigneeId) {
    try {
      const response = await this.updateTask(taskId, { assignedToId: newAssigneeId });
      return response;
    } catch (error) {
      console.error('Error al reasignar tarea:', error);
      throw error;
    }
  }

  // Obtener tareas por sesión
  async getTasksBySession(sessionId) {
    try {
      const response = await this.getAllTasks({ sessionId, limit: 1000 });
      return response;
    } catch (error) {
      console.error('Error al obtener tareas de la sesión:', error);
      throw error;
    }
  }

  // Obtener resumen de productividad
  async getProductivitySummary(userId, period = 'month') {
    try {
      const stats = await this.getTaskStats({ userId });
      const allTasks = await this.getUserTasks(userId, { limit: 1000 });
      const tasks = allTasks.tasks || [];

      const now = new Date();
      let startDate;

      switch (period) {
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - now.getDay()));
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      const periodTasks = tasks.filter(task => {
        const taskDate = new Date(task.createdAt);
        return taskDate >= startDate;
      });

      const completedTasks = periodTasks.filter(task => task.status === 'completed');
      const completionRate = periodTasks.length > 0 ? (completedTasks.length / periodTasks.length) * 100 : 0;

      return {
        success: true,
        summary: {
          period,
          totalTasks: periodTasks.length,
          completedTasks: completedTasks.length,
          completionRate: Math.round(completionRate),
          overdue: stats.stats?.overdue || 0,
          upcoming: stats.stats?.upcoming || 0
        }
      };
    } catch (error) {
      console.error('Error al obtener resumen de productividad:', error);
      throw error;
    }
  }
}

export default new TaskService();
