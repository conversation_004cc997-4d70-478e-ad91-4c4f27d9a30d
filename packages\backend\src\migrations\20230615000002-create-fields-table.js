'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Fields', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      formId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Forms',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      label: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      placeholder: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      helpText: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      required: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      options: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      validations: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      config: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Añadir índices para mejorar el rendimiento
    await queryInterface.addIndex('Fields', ['formId']);
    await queryInterface.addIndex('Fields', ['type']);
    await queryInterface.addIndex('Fields', ['order']);
    await queryInterface.addIndex('Fields', ['isActive']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Fields');
  }
};
