const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo FunnelStage - Representa una etapa del embudo de selección
 * 
 * Este modelo define las diferentes etapas por las que puede pasar una solicitud
 * en el proceso de selección de una aceleradora.
 */
const FunnelStage = sequelize.define('FunnelStage', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  // Nombre de la etapa
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  // Descripción de la etapa
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  // ID de la aceleradora a la que pertenece esta etapa
  acceleratorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Accelerators',
      key: 'id',
    },
  },
  // Orden de la etapa en el embudo
  order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  },
  // Color para representar visualmente la etapa
  color: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: '#3B82F6', // Color azul por defecto
  },
  // Configuración adicional de la etapa
  config: {
    type: DataTypes.JSON,
    allowNull: true,
    // Ejemplo: { requireApproval: true, notifyApplicant: true, autoAdvance: false }
  },
  // Indica si la etapa está activa
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
}, {
  timestamps: true,
});

// Método para inicializar etapas predeterminadas para una aceleradora
FunnelStage.initDefaultStages = async (acceleratorId) => {
  const defaultStages = [
    { 
      name: 'Recibida', 
      description: 'Solicitud recibida y pendiente de revisión inicial', 
      order: 1,
      color: '#3B82F6', // Azul
      acceleratorId 
    },
    { 
      name: 'Revisión', 
      description: 'En proceso de evaluación por el equipo', 
      order: 2,
      color: '#8B5CF6', // Púrpura
      acceleratorId 
    },
    { 
      name: 'Entrevista', 
      description: 'Seleccionado para entrevista', 
      order: 3,
      color: '#EC4899', // Rosa
      acceleratorId 
    },
    { 
      name: 'Seleccionado', 
      description: 'Aprobado para participar en el programa', 
      order: 4,
      color: '#10B981', // Verde
      acceleratorId 
    },
    { 
      name: 'Rechazado', 
      description: 'No seleccionado para el programa actual', 
      order: 5,
      color: '#EF4444', // Rojo
      acceleratorId 
    }
  ];

  try {
    for (const stage of defaultStages) {
      await FunnelStage.findOrCreate({
        where: { 
          name: stage.name,
          acceleratorId: stage.acceleratorId
        },
        defaults: stage,
      });
    }
    console.log(`Etapas de embudo inicializadas para aceleradora ID: ${acceleratorId}`);
    return true;
  } catch (error) {
    console.error('Error al inicializar etapas de embudo:', error);
    return false;
  }
};

module.exports = FunnelStage;
