/**
 * Script para verificar y corregir el rol del administrador global
 */

// Configurar variables de entorno
require('dotenv').config();

// Importar dependencias
const { sequelize } = require('../src/models');
const { verifyGlobalAdmin } = require('../src/utils/verifyAdmin');

const main = async () => {
  try {
    console.log('Conectando a la base de datos...');
    await sequelize.authenticate();
    console.log('Conexión establecida correctamente.');

    try {
      console.log('Sincronizando esquema de base de datos (alter: true) desde verify-admin.js...');
      await sequelize.sync({ alter: true });
      console.log('Esquema sincronizado correctamente desde verify-admin.js.');
    } catch (syncError) {
      console.error('Error al sincronizar esquema desde verify-admin.js:', syncError);
      // Decide if you want to exit or allow continuation
      // For now, let's log and continue, as the main app also tries to sync.
    }

    // Verificar y corregir el rol del administrador global
    const success = await verifyGlobalAdmin();

    if (success) {
      console.log('Verificación completada con éxito.');
    } else {
      console.error('Error en la verificación del administrador global.');
      // No salimos con código de error para permitir que el servidor inicie
      console.log('Continuando con el inicio del servidor...');
    }

    // Cerrar la conexión a la base de datos
    await sequelize.close();
    console.log('Conexión a la base de datos cerrada.');
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    // No salimos con código de error para permitir que el servidor inicie
    console.log('Continuando con el inicio del servidor a pesar del error...');
    process.exit(0);
  }
};

// Ejecutar el script
main();

