const { sequelize } = require('../config/database');
const User = require('./user.model');
const Role = require('./role.model');
const Accelerator = require('./accelerator.model');
const RegistrationCode = require('./registrationCode.model');
const Form = require('./form.model');
const Field = require('./field.model');
const Application = require('./application.model');
const FunnelStage = require('./funnelStage.model');
const Session = require('./session.model');
const SessionNote = require('./sessionNote.model');
const Task = require('./task.model');

// Definir relaciones entre modelos
User.belongsTo(Role, { foreignKey: 'roleId' });
Role.hasMany(User, { foreignKey: 'roleId' });

// Relaciones para aceleradoras
// Un usuario puede ser administrador de una aceleradora
User.belongsToMany(Accelerator, { through: 'AcceleratorAdmins', as: 'managedAccelerators' });
Accelerator.belongsToMany(User, { through: 'AcceleratorAdmins', as: 'administrators' });

// Relaciones para códigos de registro
RegistrationCode.belongsTo(Role, { foreignKey: 'roleId' });
RegistrationCode.belongsTo(Accelerator, { foreignKey: 'acceleratorId', as: 'accelerator' });
RegistrationCode.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });

// Relaciones para formularios
Form.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Form.belongsTo(User, { foreignKey: 'createdById', as: 'creator' });
Accelerator.hasMany(Form, { foreignKey: 'acceleratorId' });

// Relaciones para campos de formulario
Field.belongsTo(Form, { foreignKey: 'formId' });
Form.hasMany(Field, { foreignKey: 'formId' });

// Relaciones para solicitudes
Application.belongsTo(Form, { foreignKey: 'formId' });
Application.belongsTo(User, { foreignKey: 'applicantId', as: 'applicant' });
Application.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Application.belongsTo(FunnelStage, { foreignKey: 'funnelStageId', as: 'stage' });
Form.hasMany(Application, { foreignKey: 'formId' });
User.hasMany(Application, { foreignKey: 'applicantId', as: 'applications' });
Accelerator.hasMany(Application, { foreignKey: 'acceleratorId' });

// Relaciones para etapas del embudo
FunnelStage.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });
Accelerator.hasMany(FunnelStage, { foreignKey: 'acceleratorId', as: 'funnelStages' });
FunnelStage.hasMany(Application, { foreignKey: 'funnelStageId' });

// Coaching & Mentoría Session associations
User.hasMany(Session, { foreignKey: 'mentorId', as: 'MentoringSessions' });
User.hasMany(Session, { foreignKey: 'entrepreneurId', as: 'CoachingSessions' });
Session.belongsTo(User, { as: 'Mentor', foreignKey: 'mentorId' });
Session.belongsTo(User, { as: 'Entrepreneur', foreignKey: 'entrepreneurId' });
Accelerator.hasMany(Session, { foreignKey: 'acceleratorId' });
Session.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });

// SessionNote associations
Session.hasMany(SessionNote, { foreignKey: 'sessionId', as: 'Notes' });
SessionNote.belongsTo(Session, { foreignKey: 'sessionId' });
User.hasMany(SessionNote, { foreignKey: 'authorId', as: 'AuthoredNotes' });
SessionNote.belongsTo(User, { as: 'Author', foreignKey: 'authorId' });

// Task associations
Session.hasMany(Task, { foreignKey: 'sessionId', as: 'Tasks' }); // Tasks originating from a session
Task.belongsTo(Session, { foreignKey: 'sessionId' });
User.hasMany(Task, { foreignKey: 'assignedToId', as: 'AssignedTasks' });
User.hasMany(Task, { foreignKey: 'assignedById', as: 'CreatedTasks' });
Task.belongsTo(User, { as: 'AssignedTo', foreignKey: 'assignedToId' });
Task.belongsTo(User, { as: 'AssignedBy', foreignKey: 'assignedById' });
Accelerator.hasMany(Task, { foreignKey: 'acceleratorId' });
Task.belongsTo(Accelerator, { foreignKey: 'acceleratorId' });

module.exports = {
  sequelize,
  User,
  Role,
  Accelerator,
  RegistrationCode,
  Form,
  Field,
  Application,
  FunnelStage,
  Session,
  SessionNote,
  Task,
};
