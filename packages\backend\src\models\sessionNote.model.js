const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SessionNote = sequelize.define('SessionNote', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  sessionId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Sessions', key: 'id' }
  },
  authorId: { // User ID of the note's author (typically mentor)
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  // Consider adding 'type' if there are different kinds of notes (e.g., private, shared)
}, { timestamps: true });

module.exports = SessionNote;
