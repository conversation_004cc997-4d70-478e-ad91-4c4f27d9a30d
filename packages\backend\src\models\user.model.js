const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true,
    },
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  profilePicture: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  refreshToken: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  refreshTokenExpires: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  // Campo para almacenar datos específicos según el rol
  profileData: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  // Campos para restablecimiento de contraseña
  resetPasswordToken: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  resetPasswordExpires: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  timestamps: true,
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
  },
});

// Método para comparar contraseñas
User.prototype.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Método para crear o actualizar el administrador global predeterminado
User.createGlobalAdmin = async (roleId) => {
  try {
    if (!roleId) {
      throw new Error('Se requiere un ID de rol válido para el administrador global');
    }

    const [admin, created] = await User.findOrCreate({
      where: { email: '<EMAIL>' },
      defaults: {
        firstName: 'Admin',
        lastName: 'Global',
        email: '<EMAIL>',
        password: 'admin123', // Se encriptará automáticamente por el hook
        roleId: roleId,
        isActive: true,
      },
    });

    if (created) {
      console.log('Administrador global creado correctamente con roleId:', roleId);
    } else {
      // Verificar si el rol es correcto, y actualizarlo si es necesario
      if (admin.roleId !== roleId) {
        console.log(`Actualizando rol del administrador global de ${admin.roleId || 'null'} a ${roleId}`);
        await admin.update({ roleId: roleId });
        console.log('Rol del administrador global actualizado correctamente');
      } else {
        console.log('El administrador global ya existe con el rol correcto');
      }
    }

    // Verificar que el administrador tenga el rol correcto
    const updatedAdmin = await User.findOne({ where: { email: '<EMAIL>' } });
    if (updatedAdmin.roleId !== roleId) {
      console.error('Error: El administrador global no tiene el rol correcto después de la actualización');
    } else {
      console.log('Verificación exitosa: El administrador global tiene el rol correcto (ID:', roleId, ')');
    }

    return updatedAdmin;
  } catch (error) {
    console.error('Error al crear/actualizar administrador global:', error);
    throw error; // Propagar el error para que pueda ser manejado por el llamador
  }
};

module.exports = User;
