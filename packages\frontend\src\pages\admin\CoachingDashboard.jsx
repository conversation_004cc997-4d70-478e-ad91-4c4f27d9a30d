import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiClock,
  FiCheckCircle,
  FiAlertTriangle,
  FiUsers,
  FiFileText,
  FiBarChart2,
  FiPlus
} from 'react-icons/fi';
import { Link } from 'react-router-dom';
import sessionService from '../../services/sessionService';
import taskService from '../../services/taskService';
import sessionNoteService from '../../services/sessionNoteService';
import useAuth from '../../hooks/useAuth';

const CoachingDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    upcomingSessions: [],
    recentSessions: [],
    pendingTasks: [],
    overdueTasks: [],
    recentNotes: [],
    stats: {
      sessions: { total: 0, thisMonth: 0, thisWeek: 0 },
      tasks: { total: 0, completed: 0, pending: 0, overdue: 0 },
      notes: { total: 0, thisMonth: 0, thisWeek: 0 }
    }
  });

  useEffect(() => {
    loadDashboardData();
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Cargar datos en paralelo
      const [
        upcomingSessionsRes,
        recentSessionsRes,
        pendingTasksRes,
        overdueTasksRes,
        recentNotesRes,
        sessionStatsRes,
        taskStatsRes,
        noteStatsRes
      ] = await Promise.all([
        sessionService.getUpcomingSessions(user.id, user.role),
        sessionService.getAllSessions({
          limit: 5,
          sortBy: 'startTime',
          sortOrder: 'DESC',
          ...(user.role === 'MENTOR' && { mentorId: user.id }),
          ...(user.role === 'ENTREPRENEUR' && { entrepreneurId: user.id })
        }),
        taskService.getPendingTasks(user.id, 5),
        taskService.getOverdueTasks(user.id, 5),
        sessionNoteService.getRecentNotes(5),
        sessionService.getSessionStats({
          ...(user.role === 'MENTOR' && { mentorId: user.id }),
          ...(user.role === 'ENTREPRENEUR' && { entrepreneurId: user.id })
        }),
        taskService.getTaskStats({ userId: user.id }),
        sessionNoteService.getNotesStats(user.id)
      ]);

      setDashboardData({
        upcomingSessions: upcomingSessionsRes.sessions || [],
        recentSessions: recentSessionsRes.sessions || [],
        pendingTasks: pendingTasksRes.tasks || [],
        overdueTasks: overdueTasksRes.tasks || [],
        recentNotes: recentNotesRes.notes || [],
        stats: {
          sessions: sessionStatsRes,
          tasks: taskStatsRes.stats || {},
          notes: noteStatsRes
        }
      });
    } catch (error) {
      console.error('Error al cargar datos del dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      pending_confirmation: 'bg-yellow-100 text-yellow-800',
      scheduled: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      cancelled_mentor: 'bg-red-100 text-red-800',
      cancelled_entrepreneur: 'bg-red-100 text-red-800',
      pending: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusText = (status) => {
    const texts = {
      pending_confirmation: 'Pendiente confirmación',
      scheduled: 'Programada',
      completed: 'Completada',
      cancelled_mentor: 'Cancelada por mentor',
      cancelled_entrepreneur: 'Cancelada por emprendedor',
      pending: 'Pendiente',
      in_progress: 'En progreso'
    };
    return texts[status] || status;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard de Coaching
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Resumen de tus actividades de coaching y mentoría
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            to="/admin/sessions/new"
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <FiPlus className="-ml-1 mr-2 h-5 w-5" />
            Nueva Sesión
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCalendar className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Sesiones este mes
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {dashboardData.stats.sessions.thisMonth || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiCheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Tareas completadas
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {dashboardData.stats.tasks.completed || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiAlertTriangle className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Tareas vencidas
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {dashboardData.stats.tasks.overdue || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FiFileText className="h-6 w-6 text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Notas este mes
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {dashboardData.stats.notes.thisMonth || 0}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Próximas Sesiones */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Próximas Sesiones
              </h3>
              <Link
                to="/admin/sessions"
                className="text-sm text-indigo-600 hover:text-indigo-500"
              >
                Ver todas
              </Link>
            </div>
            <div className="space-y-3">
              {dashboardData.upcomingSessions.length > 0 ? (
                dashboardData.upcomingSessions.map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{session.title}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(session.startTime)}
                      </p>
                      <p className="text-xs text-gray-400">
                        {session.Mentor?.firstName} {session.Mentor?.lastName} - {session.Entrepreneur?.firstName} {session.Entrepreneur?.lastName}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                      {getStatusText(session.status)}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No hay sesiones próximas programadas
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Tareas Pendientes */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Tareas Pendientes
              </h3>
              <Link
                to="/admin/tasks"
                className="text-sm text-indigo-600 hover:text-indigo-500"
              >
                Ver todas
              </Link>
            </div>
            <div className="space-y-3">
              {dashboardData.pendingTasks.length > 0 ? (
                dashboardData.pendingTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{task.title}</p>
                      {task.dueDate && (
                        <p className="text-sm text-gray-500">
                          Vence: {formatDate(task.dueDate)}
                        </p>
                      )}
                      <p className="text-xs text-gray-400">
                        Asignada por: {task.AssignedBy?.firstName} {task.AssignedBy?.lastName}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {getStatusText(task.status)}
                    </span>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No hay tareas pendientes
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Tareas Vencidas y Notas Recientes */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Tareas Vencidas */}
        {dashboardData.overdueTasks.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                ⚠️ Tareas Vencidas
              </h3>
              <div className="space-y-3">
                {dashboardData.overdueTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{task.title}</p>
                      <p className="text-sm text-red-600">
                        Venció: {formatDate(task.dueDate)}
                      </p>
                    </div>
                    <FiAlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Notas Recientes */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Notas Recientes
              </h3>
              <Link
                to="/admin/session-notes"
                className="text-sm text-indigo-600 hover:text-indigo-500"
              >
                Ver todas
              </Link>
            </div>
            <div className="space-y-3">
              {dashboardData.recentNotes.length > 0 ? (
                dashboardData.recentNotes.map((note) => (
                  <div key={note.id} className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-900 line-clamp-2">
                      {note.content}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(note.createdAt)} - {note.Session?.title}
                    </p>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No hay notas recientes
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoachingDashboard;
