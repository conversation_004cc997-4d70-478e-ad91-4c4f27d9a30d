/**
 * Script para debuggear el problema de listado de aceleradoras
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`${title}`, 'bold');
  console.log('='.repeat(60));
}

async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    return response.data.token;
  } catch (error) {
    log(`Error al obtener token: ${error.message}`, 'red');
    return null;
  }
}

async function debugAccelerators() {
  logSection('🔍 DEBUG: PROBLEMA DE LISTADO DE ACELERADORAS');
  
  // Obtener token
  const token = await getAuthToken();
  if (!token) {
    log('No se pudo obtener token de autenticación', 'red');
    return;
  }
  
  log('✅ Token obtenido correctamente', 'green');
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // 1. Probar endpoint básico de aceleradoras
    log('\n1. Probando GET /api/accelerators (sin parámetros):', 'blue');
    const basicResponse = await axios.get(`${API_BASE_URL}/accelerators`, { headers });
    log(`Status: ${basicResponse.status}`, 'green');
    log(`Estructura de respuesta:`, 'cyan');
    console.log(JSON.stringify(basicResponse.data, null, 2));
    
    // 2. Probar con parámetros de paginación
    log('\n2. Probando GET /api/accelerators con parámetros:', 'blue');
    const paginatedResponse = await axios.get(`${API_BASE_URL}/accelerators?page=1&limit=10&search=&status=&sortBy=name&sortOrder=ASC`, { headers });
    log(`Status: ${paginatedResponse.status}`, 'green');
    log(`Número de aceleradoras: ${paginatedResponse.data.accelerators?.length || 0}`, 'cyan');
    
    if (paginatedResponse.data.accelerators && paginatedResponse.data.accelerators.length > 0) {
      log('\n📋 Aceleradoras encontradas:', 'green');
      paginatedResponse.data.accelerators.forEach((acc, index) => {
        log(`   ${index + 1}. ID: ${acc.id}, Nombre: ${acc.name}, Activa: ${acc.isActive}`, 'yellow');
      });
    } else {
      log('\n❌ No se encontraron aceleradoras en la respuesta', 'red');
    }
    
    // 3. Crear una aceleradora de prueba
    log('\n3. Creando aceleradora de prueba:', 'blue');
    const newAccelerator = {
      name: `Debug Accelerator ${Date.now()}`,
      description: 'Aceleradora creada para debug',
      website: 'https://debug.com',
      location: 'Ciudad de México',
      isActive: true
    };
    
    const createResponse = await axios.post(`${API_BASE_URL}/accelerators`, newAccelerator, { headers });
    log(`Status de creación: ${createResponse.status}`, 'green');
    log(`Aceleradora creada:`, 'cyan');
    console.log(JSON.stringify(createResponse.data, null, 2));
    
    // 4. Verificar listado después de crear
    log('\n4. Verificando listado después de crear:', 'blue');
    const afterCreateResponse = await axios.get(`${API_BASE_URL}/accelerators`, { headers });
    log(`Número de aceleradoras después de crear: ${afterCreateResponse.data.accelerators?.length || 0}`, 'cyan');
    
    // 5. Verificar estructura de datos
    log('\n5. Analizando estructura de datos:', 'blue');
    if (afterCreateResponse.data.accelerators && afterCreateResponse.data.accelerators.length > 0) {
      const firstAccelerator = afterCreateResponse.data.accelerators[0];
      log('Campos disponibles en la primera aceleradora:', 'cyan');
      Object.keys(firstAccelerator).forEach(key => {
        log(`   - ${key}: ${typeof firstAccelerator[key]} = ${firstAccelerator[key]}`, 'yellow');
      });
    }
    
  } catch (error) {
    log(`Error en las pruebas: ${error.message}`, 'red');
    if (error.response) {
      log(`Status: ${error.response.status}`, 'red');
      log(`Datos del error:`, 'red');
      console.log(JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function debugFrontendService() {
  logSection('🎨 DEBUG: SERVICIO DEL FRONTEND');
  
  // Simular la llamada que hace el frontend
  const token = await getAuthToken();
  if (!token) return;
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // Simular exactamente lo que hace AcceleratorService.getAllAccelerators
    const page = 1;
    const limit = 10;
    const filters = {
      search: '',
      status: '',
      sortBy: 'name',
      sortOrder: 'ASC'
    };
    
    const queryParams = new URLSearchParams({
      page,
      limit,
      ...filters
    });
    
    log(`URL que usa el frontend: ${API_BASE_URL}/accelerators?${queryParams}`, 'blue');
    
    const response = await axios.get(`${API_BASE_URL}/accelerators?${queryParams}`, { headers });
    
    log(`Status: ${response.status}`, 'green');
    log(`Respuesta completa del servicio:`, 'cyan');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Verificar si la estructura es la esperada por el frontend
    if (response.data.accelerators) {
      log(`✅ Campo 'accelerators' encontrado con ${response.data.accelerators.length} elementos`, 'green');
    } else {
      log(`❌ Campo 'accelerators' NO encontrado en la respuesta`, 'red');
      log(`Campos disponibles:`, 'yellow');
      Object.keys(response.data).forEach(key => {
        log(`   - ${key}`, 'yellow');
      });
    }
    
  } catch (error) {
    log(`Error en simulación del frontend: ${error.message}`, 'red');
    if (error.response) {
      console.log(JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runDebug() {
  console.clear();
  log('🐛 DEBUGGING: PROBLEMA DE LISTADO DE ACELERADORAS', 'bold');
  log('Investigando por qué el frontend no muestra las aceleradoras...', 'blue');
  
  await debugAccelerators();
  await debugFrontendService();
  
  logSection('💡 RECOMENDACIONES');
  log('1. Verificar que el backend esté devolviendo el campo "accelerators"', 'yellow');
  log('2. Comprobar que el frontend esté procesando correctamente la respuesta', 'yellow');
  log('3. Revisar la consola del navegador para errores de JavaScript', 'yellow');
  log('4. Verificar que no haya problemas de CORS', 'yellow');
}

// Ejecutar el debug
if (require.main === module) {
  runDebug().catch(error => {
    log(`Error fatal en debug: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runDebug };
