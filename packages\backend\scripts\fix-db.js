#!/usr/bin/env node

/**
 * Script de emergencia para arreglar problemas de base de datos
 * Este script elimina el archivo de base de datos y crea uno nuevo
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Ruta al archivo de base de datos
const dbPath = path.join(__dirname, '../database.sqlite');
const backupPath = path.join(__dirname, `../database.sqlite.backup.${Date.now()}`);

console.log('Script de emergencia para arreglar la base de datos');
console.log('ADVERTENCIA: Este script eliminará todos los datos de la base de datos');

// Verificar si existe el archivo
if (fs.existsSync(dbPath)) {
  try {
    // Crear copia de seguridad
    fs.copyFileSync(dbPath, backupPath);
    console.log(`Copia de seguridad creada en: ${backupPath}`);
  } catch (error) {
    console.warn(`No se pudo crear copia de seguridad: ${error.message}`);
  }

  try {
    // Eliminar archivo de base de datos
    fs.unlinkSync(dbPath);
    console.log('Archivo de base de datos eliminado correctamente');
  } catch (error) {
    console.error(`Error al eliminar la base de datos: ${error.message}`);
    console.error('El archivo podría estar en uso por otro proceso.');
    console.error('Cierre todas las aplicaciones que puedan estar usando la base de datos e intente de nuevo.');
    process.exit(1);
  }
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('¿Está seguro de que desea reparar la base de datos? Esto eliminará todos los datos existentes. (s/N): ', (answer) => {
  if (answer.toLowerCase() === 's') {
    console.log('Creando nueva base de datos...');

    // Iniciar el servidor para crear la base de datos
    try {
      // Ejecutar el servidor en modo de inicialización y luego detenerlo
      console.log('Iniciando servidor para crear la base de datos...');

      // Corregir la ruta al archivo index.js
      execSync('node src/index.js --init-only', {
        stdio: 'inherit',
        timeout: 10000, // 10 segundos máximo
        cwd: path.join(__dirname, '..') // Establecer el directorio de trabajo correcto
      });

      console.log('Base de datos creada correctamente');
    } catch (error) {
      console.error('Error al crear la base de datos:', error.message);
      process.exit(1);
    }

    console.log('¡Listo! La base de datos ha sido reparada.');
    console.log('Ahora puede iniciar el servidor normalmente.');
  } else {
    console.log('Operación cancelada.');
    process.exit(0);
  }
  rl.close();
});


