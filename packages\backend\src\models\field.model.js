const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Field - Representa un campo de formulario
 * 
 * Este modelo define la estructura de los campos que pueden ser utilizados
 * en los formularios dinámicos de la plataforma.
 */
const Field = sequelize.define('Field', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  formId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Forms',
      key: 'id',
    },
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  label: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false,
    // Tipos de campo soportados
    validate: {
      isIn: [['text', 'textarea', 'number', 'email', 'date', 'select', 'radio', 'checkbox', 'file']]
    }
  },
  placeholder: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  helpText: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  required: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
  },
  order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
  },
  // Para campos de tipo select, radio, checkbox
  options: {
    type: DataTypes.JSON,
    allowNull: true,
    // Ejemplo: [{ label: 'Opción 1', value: 'opcion1' }, { label: 'Opción 2', value: 'opcion2' }]
  },
  // Validaciones adicionales
  validations: {
    type: DataTypes.JSON,
    allowNull: true,
    // Ejemplo: { min: 5, max: 100, pattern: '^[a-zA-Z0-9]+$' }
  },
  // Configuración adicional específica del tipo de campo
  config: {
    type: DataTypes.JSON,
    allowNull: true,
  },
  // Indica si el campo está activo
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
}, {
  timestamps: true,
});

module.exports = Field;
