# 1. Asegúrate de estar en la rama correcta
git checkout refactor/remove-duplicate-frontend-src

# 2. Obtén los cambios del repositorio remoto
git fetch origin

# 3. Integra los cambios remotos a tu rama local
git pull origin refactor/remove-duplicate-frontend-src

# 4. Reinicia el servidor para aplicar los cambios
npm run dev


Para hacer merge de la rama refactor/remove-duplicate-frontend-src con la rama main, sigue estos pasos:

# 1. Cambia a la rama main
git checkout main

# 2. Asegúrate de tener la última versión de main
git pull origin main

# 3. Realiza el merge de tu rama de refactorización a main
git merge refactor/remove-duplicate-frontend-src

# 4. Si no hay conflictos, puedes subir los cambios al repositorio remoto
git push origin main

