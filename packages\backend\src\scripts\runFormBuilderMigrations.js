const { sequelize } = require('../config/database');
const { Umzug, SequelizeStorage } = require('umzug');
const path = require('path');

// Configurar Umzug para ejecutar migraciones
const umzug = new Umzug({
  migrations: {
    glob: [
      '20230615000001-create-forms-table.js',
      '20230615000002-create-fields-table.js',
      '20230615000003-create-funnel-stages-table.js',
      '20230615000004-create-applications-table.js',
    ].map(file => path.join(__dirname, '../migrations', file)),
    resolve: ({ name, path, context }) => {
      const migration = require(path);
      return {
        name,
        up: async () => migration.up(context.queryInterface, context.Sequelize),
        down: async () => migration.down(context.queryInterface, context.Sequelize),
      };
    },
  },
  context: { sequelize, queryInterface: sequelize.getQueryInterface(), Sequelize: require('sequelize') },
  storage: new SequelizeStorage({ sequelize }),
  logger: console,
});

// Función para ejecutar migraciones
async function runMigrations() {
  try {
    console.log('Ejecutando migraciones del Form Builder...');
    await umzug.up();
    console.log('Migraciones completadas exitosamente.');
    process.exit(0);
  } catch (error) {
    console.error('Error al ejecutar migraciones:', error);
    process.exit(1);
  }
}

// Ejecutar migraciones
runMigrations();
