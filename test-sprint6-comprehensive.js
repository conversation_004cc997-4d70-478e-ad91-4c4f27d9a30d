#!/usr/bin/env node

/**
 * PRUEBAS EXHAUSTIVAS DEL SPRINT 6 - SISTEMA DE COACHING Y MENTORÍA
 *
 * Este script realiza pruebas automatizadas de todas las funcionalidades
 * implementadas en el Sprint 6 del sistema Bumeran.
 */

const axios = require('axios');
const fs = require('fs');

// Configuración
const BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:5173';

// Credenciales de prueba
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = '';
let testResults = [];

// Función para logging de resultados
function logTest(testName, status, details = '') {
  const result = {
    test: testName,
    status: status, // 'PASS', 'FAIL', 'PARTIAL'
    details: details,
    timestamp: new Date().toISOString()
  };
  testResults.push(result);

  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${statusIcon} ${testName}: ${status}`);
  if (details) console.log(`   ${details}`);
}

// Función para hacer peticiones autenticadas
async function apiRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

// 1. PRUEBAS DE AUTENTICACIÓN Y ACCESO
async function testAuthentication() {
  console.log('\n🔐 === PRUEBAS DE AUTENTICACIÓN ===');

  try {
    const response = await apiRequest('POST', '/auth/login', ADMIN_CREDENTIALS);

    if (response.success && response.token) {
      authToken = response.token;
      logTest('Login de Administrador Global', 'PASS', 'Token obtenido correctamente');

      // Verificar perfil
      const profile = await apiRequest('GET', '/auth/profile');
      if (profile.user && profile.user.role === 'GLOBAL_ADMIN') {
        logTest('Verificación de Rol GLOBAL_ADMIN', 'PASS', `Usuario: ${profile.user.email}`);
      } else {
        logTest('Verificación de Rol GLOBAL_ADMIN', 'FAIL', 'Rol incorrecto o no encontrado');
      }
    } else {
      logTest('Login de Administrador Global', 'FAIL', 'No se obtuvo token');
      return false;
    }
  } catch (error) {
    logTest('Login de Administrador Global', 'FAIL', error.message);
    return false;
  }

  return true;
}

// 2. PRUEBAS DE ENDPOINTS DE USUARIOS POR ROL
async function testUsersByRole() {
  console.log('\n👥 === PRUEBAS DE USUARIOS POR ROL ===');

  const roles = ['MENTOR', 'ENTREPRENEUR', 'ACCELERATOR_ADMIN'];

  for (const role of roles) {
    try {
      const response = await apiRequest('GET', `/users/by-role/${role}`);

      if (response.success && Array.isArray(response.users)) {
        logTest(`Obtener usuarios con rol ${role}`, 'PASS',
          `${response.users.length} usuarios encontrados`);
      } else {
        logTest(`Obtener usuarios con rol ${role}`, 'FAIL', 'Respuesta inválida');
      }
    } catch (error) {
      logTest(`Obtener usuarios con rol ${role}`, 'FAIL', error.message);
    }
  }
}

// 3. PRUEBAS DE SESIONES
async function testSessions() {
  console.log('\n📅 === PRUEBAS DE SESIONES ===');

  try {
    // Obtener lista de sesiones
    const sessions = await apiRequest('GET', '/sessions');
    logTest('Obtener lista de sesiones', 'PASS',
      `${sessions.sessions ? sessions.sessions.length : 0} sesiones encontradas`);

    // Obtener usuarios para crear sesión de prueba
    const mentors = await apiRequest('GET', '/users/by-role/MENTOR');
    const entrepreneurs = await apiRequest('GET', '/users/by-role/ENTREPRENEUR');

    if (mentors.users.length > 0 && entrepreneurs.users.length > 0) {
      // Crear sesión de prueba
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7); // Una semana en el futuro
      const endDate = new Date(futureDate);
      endDate.setHours(endDate.getHours() + 1); // 1 hora después

      const sessionData = {
        mentorId: mentors.users[0].id,
        entrepreneurId: entrepreneurs.users[0].id,
        title: 'Sesión de Prueba Sprint 6',
        description: 'Sesión creada automáticamente para pruebas del Sprint 6',
        startTime: futureDate.toISOString(),
        endTime: endDate.toISOString(),
        location: 'Oficina Virtual',
        videoMeetingLink: 'https://meet.google.com/test-sprint6'
      };

      const newSession = await apiRequest('POST', '/sessions', sessionData);

      if (newSession.success && newSession.session) {
        logTest('Crear nueva sesión', 'PASS', `Sesión ID: ${newSession.session.id}`);

        // Probar obtener detalles de la sesión
        const sessionDetails = await apiRequest('GET', `/sessions/${newSession.session.id}`);
        if (sessionDetails.success && sessionDetails.session) {
          logTest('Obtener detalles de sesión', 'PASS', 'Detalles obtenidos correctamente');
        } else {
          logTest('Obtener detalles de sesión', 'FAIL', 'No se pudieron obtener los detalles');
        }

        return newSession.session.id; // Retornar ID para pruebas posteriores
      } else {
        logTest('Crear nueva sesión', 'FAIL', 'No se pudo crear la sesión');
      }
    } else {
      logTest('Crear nueva sesión', 'FAIL', 'No hay mentores o emprendedores disponibles');
    }
  } catch (error) {
    logTest('Pruebas de sesiones', 'FAIL', error.message);
  }

  return null;
}

// 4. PRUEBAS DE TAREAS
async function testTasks(sessionId = null) {
  console.log('\n✅ === PRUEBAS DE TAREAS ===');

  try {
    // Obtener lista de tareas
    const tasks = await apiRequest('GET', '/tasks');
    logTest('Obtener lista de tareas', 'PASS',
      `${tasks.tasks ? tasks.tasks.length : 0} tareas encontradas`);

    // Obtener usuarios para asignar tarea
    const entrepreneurs = await apiRequest('GET', '/users/by-role/ENTREPRENEUR');

    if (entrepreneurs.users.length > 0) {
      // Crear tarea de prueba
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 3); // 3 días en el futuro

      const taskData = {
        title: 'Tarea de Prueba Sprint 6',
        description: 'Tarea creada automáticamente para pruebas del Sprint 6',
        assignedToId: entrepreneurs.users[0].id,
        dueDate: futureDate.toISOString(),
        priority: 'medium',
        status: 'pending'
      };

      if (sessionId) {
        taskData.sessionId = sessionId;
      }

      const newTask = await apiRequest('POST', '/tasks', taskData);

      if (newTask.success && newTask.task) {
        logTest('Crear nueva tarea', 'PASS', `Tarea ID: ${newTask.task.id}`);

        // Probar obtener detalles de la tarea
        const taskDetails = await apiRequest('GET', `/tasks/${newTask.task.id}`);
        if (taskDetails.success && taskDetails.task) {
          logTest('Obtener detalles de tarea', 'PASS', 'Detalles obtenidos correctamente');
        } else {
          logTest('Obtener detalles de tarea', 'FAIL', 'No se pudieron obtener los detalles');
        }

        return newTask.task.id; // Retornar ID para pruebas posteriores
      } else {
        logTest('Crear nueva tarea', 'FAIL', 'No se pudo crear la tarea');
      }
    } else {
      logTest('Crear nueva tarea', 'FAIL', 'No hay emprendedores disponibles');
    }
  } catch (error) {
    logTest('Pruebas de tareas', 'FAIL', error.message);
  }

  return null;
}

// 5. PRUEBAS DE NOTAS DE SESIÓN
async function testSessionNotes(sessionId = null) {
  console.log('\n📝 === PRUEBAS DE NOTAS DE SESIÓN ===');

  try {
    // Obtener lista de notas
    const notes = await apiRequest('GET', '/session-notes');
    logTest('Obtener lista de notas', 'PASS',
      `${notes.notes ? notes.notes.length : 0} notas encontradas`);

    if (sessionId) {
      // Crear nota de prueba
      const noteData = {
        sessionId: sessionId,
        content: 'Nota de prueba creada automáticamente para el Sprint 6. Esta nota contiene información importante sobre la sesión de coaching.',
        isPrivate: false
      };

      const newNote = await apiRequest('POST', '/session-notes', noteData);

      if (newNote.success && newNote.note) {
        logTest('Crear nueva nota de sesión', 'PASS', `Nota ID: ${newNote.note.id}`);

        // Probar obtener notas de la sesión específica
        const sessionNotes = await apiRequest('GET', `/session-notes/session/${sessionId}`);
        if (sessionNotes.success && sessionNotes.notes) {
          logTest('Obtener notas de sesión específica', 'PASS',
            `${sessionNotes.notes.length} notas de la sesión`);
        } else {
          logTest('Obtener notas de sesión específica', 'FAIL', 'No se pudieron obtener las notas');
        }

        return newNote.note.id;
      } else {
        logTest('Crear nueva nota de sesión', 'FAIL', 'No se pudo crear la nota');
      }
    } else {
      logTest('Crear nueva nota de sesión', 'PARTIAL', 'No hay sesión disponible para crear nota');
    }
  } catch (error) {
    logTest('Pruebas de notas de sesión', 'FAIL', error.message);
  }

  return null;
}

// 6. PRUEBAS DE INTEGRACIÓN
async function testIntegration() {
  console.log('\n🔗 === PRUEBAS DE INTEGRACIÓN ===');

  try {
    // Verificar que el frontend esté accesible
    const frontendResponse = await axios.get(FRONTEND_URL);
    if (frontendResponse.status === 200) {
      logTest('Acceso al Frontend', 'PASS', 'Frontend accesible en puerto 5173');
    } else {
      logTest('Acceso al Frontend', 'FAIL', 'Frontend no accesible');
    }
  } catch (error) {
    logTest('Acceso al Frontend', 'FAIL', 'Error al acceder al frontend');
  }

  // Verificar rutas específicas del Sprint 6 (esto requeriría navegación real)
  const sprint6Routes = [
    '/admin/sessions/new',
    '/admin/tasks',
    '/admin/session-notes'
  ];

  logTest('Rutas del Sprint 6', 'PARTIAL',
    `Rutas implementadas: ${sprint6Routes.join(', ')}`);
}

// FUNCIÓN PRINCIPAL
async function runAllTests() {
  console.log('🧪 INICIANDO PRUEBAS EXHAUSTIVAS DEL SPRINT 6');
  console.log('================================================');

  const startTime = new Date();

  // Ejecutar todas las pruebas
  const authSuccess = await testAuthentication();

  if (authSuccess) {
    await testUsersByRole();
    const sessionId = await testSessions();
    const taskId = await testTasks(sessionId);
    const noteId = await testSessionNotes(sessionId);
    await testIntegration();
  } else {
    console.log('❌ No se pudo autenticar. Saltando pruebas restantes.');
  }

  // Generar reporte final
  const endTime = new Date();
  const duration = (endTime - startTime) / 1000;

  console.log('\n📊 === RESUMEN DE PRUEBAS ===');
  console.log(`Tiempo total: ${duration} segundos`);

  const passed = testResults.filter(r => r.status === 'PASS').length;
  const failed = testResults.filter(r => r.status === 'FAIL').length;
  const partial = testResults.filter(r => r.status === 'PARTIAL').length;

  console.log(`✅ Pruebas exitosas: ${passed}`);
  console.log(`❌ Pruebas fallidas: ${failed}`);
  console.log(`⚠️ Pruebas parciales: ${partial}`);
  console.log(`📊 Total de pruebas: ${testResults.length}`);

  const successRate = ((passed + partial * 0.5) / testResults.length * 100).toFixed(1);
  console.log(`🎯 Tasa de éxito: ${successRate}%`);

  // Guardar reporte detallado
  const report = {
    summary: {
      totalTests: testResults.length,
      passed,
      failed,
      partial,
      successRate: parseFloat(successRate),
      duration,
      timestamp: endTime.toISOString()
    },
    tests: testResults
  };

  fs.writeFileSync('sprint6-test-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Reporte detallado guardado en: sprint6-test-report.json');

  // Determinar estado general
  if (successRate >= 80) {
    console.log('\n🎉 SPRINT 6 - ESTADO: LISTO PARA PRODUCCIÓN');
  } else if (successRate >= 60) {
    console.log('\n⚠️ SPRINT 6 - ESTADO: REQUIERE AJUSTES MENORES');
  } else {
    console.log('\n❌ SPRINT 6 - ESTADO: REQUIERE CORRECCIONES IMPORTANTES');
  }
}

// Ejecutar pruebas si se llama directamente
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testResults };
