const express = require('express');
const router = express.Router();
const applicationController = require('../controllers/application.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateApplication, validateUpdateApplicationStatus } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para solicitudes
router.post('/', 
  checkRole(['ENTREPRENEUR']), 
  validateCreateApplication, 
  applicationController.createApplication
);

router.get('/', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), 
  applicationController.getAllApplications
);

router.get('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTRE<PERSON><PERSON>E<PERSON>']), 
  applicationController.getApplicationById
);

router.put('/:id/status', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), 
  validateUpdateApplicationStatus, 
  applicationController.updateApplicationStatus
);

module.exports = router;
