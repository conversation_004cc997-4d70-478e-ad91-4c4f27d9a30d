const jwt = require('jsonwebtoken');
const { User, Role } = require('../models');

// Configuración de JWT
const JWT_SECRET = process.env.JWT_SECRET || 'bumeran-secret-key';

// Middleware para verificar token JWT
exports.verifyToken = async (req, res, next) => {
  try {
    // Obtener token del header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No se proporcionó token de autenticación',
      });
    }

    const token = authHeader.split(' ')[1];

    // Verificar token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Buscar usuario en la base de datos
    const user = await User.findByPk(decoded.id, {
      include: [{ model: Role }],
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Usuario no encontrado',
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'Usuario desactivado',
      });
    }

    // Añadir información del usuario al request
    req.user = {
      id: user.id,
      email: user.email,
      roleId: user.roleId,
      role: user.Role ? user.Role.name : null,
    };

    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expirado',
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token inválido',
      });
    }

    console.error('Error en verificación de token:', error);
    res.status(500).json({
      success: false,
      message: 'Error al verificar token',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Middleware para verificar roles
exports.checkRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'No autenticado',
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'No tiene permisos para acceder a este recurso',
      });
    }

    next();
  };
};

// Middleware para verificar si es el propio usuario o un administrador
exports.isSelfOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'No autenticado',
    });
  }

  const userId = req.params.id;
  
  // Permitir si es el propio usuario o un administrador global
  if (req.user.id.toString() === userId || req.user.role === 'GLOBAL_ADMIN') {
    next();
  } else {
    return res.status(403).json({
      success: false,
      message: 'No tiene permisos para acceder a este recurso',
    });
  }
};
