{"name": "backend", "version": "1.0.0", "description": "Backend para la plataforma Bumeran", "main": "src/index.js", "scripts": {"start": "nodemon src/index.js", "prestart": "node scripts/verify-admin.js", "test": "echo \"Error: no test specified\" && exit 1", "db:test": "node scripts/db-tools.js test", "db:reset": "node scripts/db-tools.js reset", "db:init": "node scripts/db-tools.js init", "db:fix": "node scripts/fix-db.js", "db:force-fix": "node scripts/force-fix-db.js", "db:setup": "node scripts/setup-db.js", "db:setup-simple": "node scripts/setup-db-simple.js", "db:migrate:forms": "node src/scripts/runFormBuilderMigrations.js", "admin:verify": "node scripts/verify-admin.js", "admin:fix": "node scripts/fix-admin-role.js", "debug:toggle": "node scripts/toggle-debug.js", "debug:on": "cross-env DEBUG_MODE=true nodemon src/index.js", "debug:off": "cross-env DEBUG_MODE=false nodemon src/index.js"}, "keywords": ["express", "api", "backend"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-random-string": "^5.0.0", "csv-parser": "^3.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.1", "sequelize": "^6.35.1", "sqlite3": "^5.1.6", "streamifier": "^0.1.1", "umzug": "^3.8.2"}, "devDependencies": {"@faker-js/faker": "^9.7.0", "axios": "^1.9.0", "cross-env": "^7.0.3", "nodemon": "^3.0.1"}}