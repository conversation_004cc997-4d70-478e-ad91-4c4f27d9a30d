const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * Modelo Application - Representa una solicitud o postulación
 *
 * Este modelo almacena las solicitudes enviadas por los emprendedores
 * a través de los formularios creados por las aceleradoras.
 */
const Application = sequelize.define('Application', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  // ID del formulario al que corresponde esta solicitud
  formId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Forms',
      key: 'id',
    },
  },
  // ID del usuario que envió la solicitud (emprendedor)
  applicantId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id',
    },
  },
  // ID de la aceleradora a la que se envía la solicitud
  acceleratorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Accelerators',
      key: 'id',
    },
  },
  // ID de la etapa del embudo en la que se encuentra la solicitud
  funnelStageId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'FunnelStages',
      key: 'id',
    },
  },
  // Respuestas del formulario
  responses: {
    type: DataTypes.JSON,
    allowNull: false,
  },
  // Estado de la solicitud
  status: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'pending', // pending, reviewing, approved, rejected
  },
  // Puntuación de la solicitud (puede ser calculada a partir de evaluaciones)
  score: {
    type: DataTypes.FLOAT,
    allowNull: true,
  },
  // Notas internas para el equipo de la aceleradora
  internalNotes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  // Retroalimentación para el solicitante
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  // Fecha de envío de la solicitud
  submittedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
  },
  // Fecha de última actualización del estado
  statusUpdatedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  // Indica si la solicitud está activa
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
}, {
  timestamps: true,
});

module.exports = Application;
