const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Task = sequelize.define('Task', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  sessionId: { // Optional: if task originates from a session
    type: DataTypes.INTEGER,
    allowNull: true,
    references: { model: 'Sessions', key: 'id' }
  },
  assignedToId: { // User ID of the person responsible for the task
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  assignedById: { // User ID of the person who assigned the task
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  acceleratorId: { // To scope tasks
    type: DataTypes.INTEGER,
    allowNull: true, // Or false if always required
    references: { model: 'Accelerators', key: 'id' }
  },
  dueDate: {
    type: DataTypes.DATEONLY, // Or DataTypes.DATE if time is relevant
    allowNull: true,
  },
  status: { // e.g., 'pending', 'in_progress', 'completed', 'archived'
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'pending',
  },
  // Consider 'priority', 'tags', etc.
}, { timestamps: true });

module.exports = Task;
