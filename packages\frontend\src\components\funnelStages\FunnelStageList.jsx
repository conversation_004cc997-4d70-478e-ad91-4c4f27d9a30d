import React, { useState, useEffect } from 'react';
import { FiPlus, FiEdit, FiTrash2, FiMove } from 'react-icons/fi';
import Card from '../common/Card';
import Button from '../common/Button';
import LoadingSpinner from '../common/LoadingSpinner';
import api from '../../services/api';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

/**
 * Componente para gestionar las etapas del embudo de una aceleradora
 */
const FunnelStageList = ({ acceleratorId, showNotification }) => {
  const [stages, setStages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [currentStage, setCurrentStage] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    order: 0
  });

  // Cargar etapas del embudo
  useEffect(() => {
    const fetchStages = async () => {
      if (!acceleratorId) return;
      
      try {
        setLoading(true);
        const response = await api.get(`/funnel-stages/accelerator/${acceleratorId}`);
        
        if (response.data.success) {
          // Ordenar etapas por el campo 'order'
          const sortedStages = [...response.data.stages].sort((a, b) => a.order - b.order);
          setStages(sortedStages);
        } else {
          showNotification('error', 'Error al cargar etapas del embudo');
        }
      } catch (error) {
        console.error('Error al cargar etapas del embudo:', error);
        showNotification('error', 'Error al cargar etapas del embudo. Por favor, intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchStages();
  }, [acceleratorId, showNotification]);

  // Manejar cambio en los campos del formulario
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCurrentStage(prev => ({ ...prev, [name]: value }));
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!currentStage.name) {
      showNotification('error', 'El nombre de la etapa es requerido');
      return;
    }
    
    try {
      let response;
      
      if (currentStage.id) {
        // Actualizar etapa existente
        response = await api.put(`/funnel-stages/${currentStage.id}`, {
          ...currentStage,
          acceleratorId
        });
      } else {
        // Crear nueva etapa
        response = await api.post('/funnel-stages', {
          ...currentStage,
          acceleratorId,
          order: stages.length
        });
      }
      
      if (response.data.success) {
        showNotification('success', `Etapa ${currentStage.id ? 'actualizada' : 'creada'} correctamente`);
        
        if (currentStage.id) {
          // Actualizar etapa en la lista
          setStages(prev => prev.map(stage => 
            stage.id === currentStage.id ? response.data.stage : stage
          ));
        } else {
          // Añadir nueva etapa a la lista
          setStages(prev => [...prev, response.data.stage]);
        }
        
        // Resetear formulario
        setCurrentStage({
          name: '',
          description: '',
          color: '#3B82F6',
          order: 0
        });
        setEditMode(false);
      } else {
        showNotification('error', `Error al ${currentStage.id ? 'actualizar' : 'crear'} etapa`);
      }
    } catch (error) {
      console.error(`Error al ${currentStage.id ? 'actualizar' : 'crear'} etapa:`, error);
      showNotification('error', `Error al ${currentStage.id ? 'actualizar' : 'crear'} etapa. Por favor, intente nuevamente.`);
    }
  };

  // Editar una etapa existente
  const handleEdit = (stage) => {
    setCurrentStage(stage);
    setEditMode(true);
  };

  // Eliminar una etapa
  const handleDelete = async (id) => {
    if (window.confirm('¿Está seguro de que desea eliminar esta etapa? Esta acción no se puede deshacer.')) {
      try {
        const response = await api.delete(`/funnel-stages/${id}`);
        
        if (response.data.success) {
          showNotification('success', 'Etapa eliminada correctamente');
          setStages(prev => prev.filter(stage => stage.id !== id));
          
          // Actualizar el orden de las etapas restantes
          const updatedStages = stages
            .filter(stage => stage.id !== id)
            .map((stage, index) => ({
              ...stage,
              order: index
            }));
          
          setStages(updatedStages);
          
          // Actualizar el orden en el servidor
          if (updatedStages.length > 0) {
            await api.put(`/funnel-stages/reorder`, {
              stageOrders: updatedStages.map((stage, idx) => ({
                id: stage.id,
                order: idx
              }))
            });
          }
        } else {
          showNotification('error', 'Error al eliminar etapa');
        }
      } catch (error) {
        console.error('Error al eliminar etapa:', error);
        showNotification('error', 'Error al eliminar etapa. Por favor, intente nuevamente.');
      }
    }
  };

  // Manejar reordenamiento por arrastrar y soltar
  const handleDragEnd = async (result) => {
    if (!result.destination) return;
    
    const items = Array.from(stages);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    
    // Actualizar el orden
    const updatedStages = items.map((stage, index) => ({
      ...stage,
      order: index
    }));
    
    setStages(updatedStages);
    
    // Actualizar el orden en el servidor
    try {
      await api.put(`/funnel-stages/reorder`, {
        stageOrders: updatedStages.map((stage, idx) => ({
          id: stage.id,
          order: idx
        }))
      });
    } catch (error) {
      console.error('Error al reordenar etapas:', error);
      showNotification('error', 'Error al reordenar etapas. Por favor, intente nuevamente.');
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Etapas del Embudo</h2>
        {!editMode && (
          <Button
            variant="primary"
            className="flex items-center"
            onClick={() => {
              setCurrentStage({
                name: '',
                description: '',
                color: '#3B82F6',
                order: stages.length
              });
              setEditMode(true);
            }}
          >
            <FiPlus className="mr-2" /> Añadir Etapa
          </Button>
        )}
      </div>

      {editMode ? (
        <form onSubmit={handleSubmit} className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h3 className="text-lg font-medium mb-4">
            {currentStage.id ? 'Editar Etapa' : 'Nueva Etapa'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nombre <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="name"
                value={currentStage.name}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Color
              </label>
              <div className="flex items-center">
                <input
                  type="color"
                  name="color"
                  value={currentStage.color}
                  onChange={handleChange}
                  className="h-10 w-10 border border-gray-300 rounded mr-2"
                />
                <input
                  type="text"
                  name="color"
                  value={currentStage.color}
                  onChange={handleChange}
                  className="flex-1 border border-gray-300 rounded-md shadow-sm p-2"
                />
              </div>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descripción
              </label>
              <textarea
                name="description"
                value={currentStage.description}
                onChange={handleChange}
                rows="3"
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              ></textarea>
            </div>
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setEditMode(false);
                setCurrentStage({
                  name: '',
                  description: '',
                  color: '#3B82F6',
                  order: 0
                });
              }}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              variant="primary"
            >
              {currentStage.id ? 'Actualizar' : 'Crear'} Etapa
            </Button>
          </div>
        </form>
      ) : null}

      {stages.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <p className="text-gray-500 mb-4">No hay etapas definidas para esta aceleradora</p>
          <Button
            variant="primary"
            className="flex items-center mx-auto"
            onClick={() => {
              setCurrentStage({
                name: '',
                description: '',
                color: '#3B82F6',
                order: 0
              });
              setEditMode(true);
            }}
          >
            <FiPlus className="mr-2" /> Añadir Primera Etapa
          </Button>
        </div>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="stages">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-3"
              >
                {stages.map((stage, index) => (
                  <Draggable key={stage.id} draggableId={String(stage.id)} index={index}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm flex justify-between items-center"
                      >
                        <div className="flex items-center">
                          <div
                            {...provided.dragHandleProps}
                            className="mr-3 text-gray-400 cursor-move"
                          >
                            <FiMove />
                          </div>
                          <div
                            className="w-4 h-4 rounded-full mr-3"
                            style={{ backgroundColor: stage.color }}
                          ></div>
                          <div>
                            <h4 className="font-medium">{stage.name}</h4>
                            {stage.description && (
                              <p className="text-sm text-gray-500">{stage.description}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center"
                            onClick={() => handleEdit(stage)}
                          >
                            <FiEdit className="mr-1" /> Editar
                          </Button>
                          <Button
                            variant="danger"
                            size="sm"
                            className="flex items-center"
                            onClick={() => handleDelete(stage.id)}
                          >
                            <FiTrash2 className="mr-1" /> Eliminar
                          </Button>
                        </div>
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}
    </Card>
  );
};

export default FunnelStageList;
