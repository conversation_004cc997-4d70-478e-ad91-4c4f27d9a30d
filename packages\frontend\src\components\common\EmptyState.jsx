import React from 'react';
import { Link } from 'react-router-dom';
import { FiPlus } from 'react-icons/fi';
import Button from './Button';

/**
 * Componente para mostrar un estado vacío
 * 
 * @param {Object} props - Propiedades del componente
 * @param {string} props.title - Título del estado vacío
 * @param {string} props.description - Descripción del estado vacío
 * @param {string} props.icon - Icono a mostrar (componente)
 * @param {string} props.actionText - Texto del botón de acción
 * @param {string} props.actionLink - Enlace del botón de acción
 * @param {Function} props.onAction - Función a ejecutar al hacer clic en el botón
 * @param {string} props.className - Clases adicionales para personalizar el estilo
 */
const EmptyState = ({
  title,
  description,
  icon: Icon,
  actionText,
  actionLink,
  onAction,
  className = '',
}) => {
  return (
    <div className={`text-center py-12 px-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 ${className}`}>
      {Icon && (
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 text-gray-400 mb-4">
          <Icon size={24} />
        </div>
      )}
      
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      
      {description && (
        <p className="text-sm text-gray-500 max-w-md mx-auto mb-6">{description}</p>
      )}
      
      {actionText && (
        actionLink ? (
          <Link to={actionLink}>
            <Button variant="primary" className="flex items-center mx-auto">
              <FiPlus className="mr-2" /> {actionText}
            </Button>
          </Link>
        ) : (
          <Button 
            variant="primary" 
            onClick={onAction} 
            className="flex items-center mx-auto"
          >
            <FiPlus className="mr-2" /> {actionText}
          </Button>
        )
      )}
    </div>
  );
};

export default EmptyState;
