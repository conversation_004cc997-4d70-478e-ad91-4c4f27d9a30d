import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import AdminLayout from '../../../components/admin/AdminLayout';
import ApplicationList from '../../../components/applications/ApplicationList';
import ApplicationDetails from '../../../components/applications/ApplicationDetails';

/**
 * Página principal de gestión de aplicaciones
 *
 * Esta página contiene las rutas anidadas para:
 * - Listar aplicaciones
 * - Ver detalles de una aplicación
 */
const ApplicationsPage = () => {
  const [notification, setNotification] = useState(null);

  // Mostrar notificación y limpiarla después de 5 segundos
  const showNotification = (type, message) => {
    setNotification({ type, message });
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Encabezado */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Gestión de Aplicaciones</h1>
          <p className="text-gray-600">Administra y evalúa las aplicaciones recibidas</p>
        </div>

        {/* Notificación */}
        {notification && (
          <div className={`mb-4 p-4 rounded-md ${
            notification.type === 'error'
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}>
            {notification.message}
            <button
              onClick={() => setNotification(null)}
              className="float-right text-gray-500 hover:text-gray-700"
            >
              &times;
            </button>
          </div>
        )}

        {/* Rutas anidadas */}
        <Routes>
          <Route
            index
            element={
              <ApplicationList
                showNotification={showNotification}
              />
            }
          />
          <Route
            path=":id"
            element={
              <ApplicationDetails
                showNotification={showNotification}
              />
            }
          />
        </Routes>
      </div>
    </AdminLayout>
  );
};

export default ApplicationsPage;
