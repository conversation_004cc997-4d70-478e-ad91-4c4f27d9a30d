import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FiSearch, FiBell, FiUser } from 'react-icons/fi';
import useAuth from '../../hooks/useAuth';
import BumeranLogo from '../common/BumeranLogo';
import { useDebug } from '../../contexts/DebugContext';

const AdminNavbar = () => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [userStatus, setUserStatus] = useState('online'); // online, busy, offline
  const { debugMode } = useDebug();
  const profileMenuRef = useRef(null);
  const profileButtonRef = useRef(null);
  const notificationsRef = useRef(null);
  const notificationsButtonRef = useRef(null);

  // Notificaciones de ejemplo
  const [notifications] = useState([
    {
      id: 1,
      title: 'Nueva aplicación recibida',
      message: 'Se ha recibido una nueva aplicación para la aceleradora XYZ',
      time: '10 min',
      read: false
    },
    {
      id: 2,
      title: 'Actualización del sistema',
      message: 'Se ha completado la actualización del sistema',
      time: '1 hora',
      read: true
    },
    {
      id: 3,
      title: 'Nuevo usuario registrado',
      message: 'Un nuevo mentor se ha registrado en la plataforma',
      time: '3 horas',
      read: false
    }
  ]);

  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Obtener el rol desde localStorage como respaldo
  const userFromStorage = JSON.parse(localStorage.getItem('user') || '{}');
  const roleFromStorage = userFromStorage?.role;

  // Verificar si el usuario es administrador global
  const isGlobalAdmin = currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN';

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
    if (isNotificationsOpen) setIsNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen);
    if (isProfileMenuOpen) setIsProfileMenuOpen(false);
  };

  const changeUserStatus = (status) => {
    setUserStatus(status);
    setIsProfileMenuOpen(false);
  };

  // Cerrar los menús cuando se hace clic fuera de ellos
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Cerrar menú de perfil
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target) &&
        profileButtonRef.current &&
        !profileButtonRef.current.contains(event.target)
      ) {
        setIsProfileMenuOpen(false);
      }

      // Cerrar menú de notificaciones
      if (
        notificationsRef.current &&
        !notificationsRef.current.contains(event.target) &&
        notificationsButtonRef.current &&
        !notificationsButtonRef.current.contains(event.target)
      ) {
        setIsNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Obtener color según estado del usuario
  const getUserStatusColor = () => {
    switch (userStatus) {
      case 'online':
        return 'bg-green-500';
      case 'busy':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-green-500';
    }
  };

  return (
    <nav className="bg-blue-700 text-white shadow-md fixed top-0 left-0 right-0 z-30">
      <div className="w-full px-4 py-2">
        <div className="flex justify-between items-center">
          {/* Logo y título */}
          <div className="flex items-center">
            <div className="text-xl font-bold ml-16 md:ml-16 lg:ml-64 transition-all duration-300 flex items-center">
              <BumeranLogo className="mr-2" size="sm" />
              <span>Admin Dashboard</span>
            </div>
          </div>

          {/* Buscador y acciones */}
          <div className="flex items-center space-x-4">
            {/* Buscador */}
            <div className="relative hidden md:block">
              <input
                type="text"
                placeholder="Buscar..."
                className="bg-blue-600 text-white placeholder-blue-300 rounded-md py-1 px-3 pl-8 focus:outline-none focus:ring-2 focus:ring-blue-400 text-sm w-48"
              />
              <FiSearch className="absolute left-2 top-2 text-blue-300" />
            </div>

            {/* Notificaciones */}
            <div className="relative">
              <button
                ref={notificationsButtonRef}
                onClick={toggleNotifications}
                className="hover:text-blue-200 p-1 relative"
              >
                <FiBell size={20} />
                {unreadNotifications > 0 && (
                  <span className="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {unreadNotifications}
                  </span>
                )}
              </button>

              {isNotificationsOpen && (
                <div
                  ref={notificationsRef}
                  className="absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg py-1 z-10 text-gray-800"
                >
                  <div className="px-4 py-2 border-b border-gray-100">
                    <h3 className="font-medium">Notificaciones</h3>
                  </div>
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.map(notification => (
                        <div
                          key={notification.id}
                          className={`px-4 py-2 hover:bg-gray-50 border-l-4 ${notification.read ? 'border-transparent' : 'border-blue-500'}`}
                        >
                          <h4 className="text-sm font-medium">{notification.title}</h4>
                          <p className="text-xs text-gray-500">{notification.message}</p>
                          <p className="text-xs text-gray-400 mt-1">{notification.time}</p>
                        </div>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-center text-gray-500 text-sm">
                        No hay notificaciones
                      </div>
                    )}
                  </div>
                  <div className="px-4 py-2 border-t border-gray-100 text-center">
                    <button className="text-sm text-blue-600 hover:text-blue-800">
                      Ver todas
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Menú de Perfil */}
            <div className="relative">
              <button
                ref={profileButtonRef}
                onClick={toggleProfileMenu}
                className="hover:text-blue-200 flex items-center focus:outline-none"
              >
                <div className="relative">
                  <div className="w-8 h-8 rounded-full bg-blue-800 flex items-center justify-center">
                    <FiUser className="text-white" />
                  </div>
                  <span className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-blue-700 ${getUserStatusColor()}`}></span>
                </div>
                <span className="ml-2 text-sm hidden md:block">{currentUser?.firstName}</span>
              </button>

              {isProfileMenuOpen && (
                <div
                  ref={profileMenuRef}
                  className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10"
                >
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-800">{currentUser?.firstName} {currentUser?.lastName}</p>
                    <p className="text-xs text-gray-500">{currentUser?.email}</p>
                  </div>

                  {/* Estados de usuario */}
                  <div className="px-4 py-2 border-b border-gray-100">
                    <p className="text-xs text-gray-500 mb-1">Estado</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => changeUserStatus('online')}
                        className={`w-2 h-2 rounded-full bg-green-500 p-2 flex items-center justify-center ${userStatus === 'online' ? 'ring-2 ring-green-200' : ''}`}
                      >
                        {userStatus === 'online' && <span className="text-xs text-white">✓</span>}
                      </button>
                      <button
                        onClick={() => changeUserStatus('busy')}
                        className={`w-2 h-2 rounded-full bg-yellow-500 p-2 flex items-center justify-center ${userStatus === 'busy' ? 'ring-2 ring-yellow-200' : ''}`}
                      >
                        {userStatus === 'busy' && <span className="text-xs text-white">✓</span>}
                      </button>
                      <button
                        onClick={() => changeUserStatus('offline')}
                        className={`w-2 h-2 rounded-full bg-gray-400 p-2 flex items-center justify-center ${userStatus === 'offline' ? 'ring-2 ring-gray-200' : ''}`}
                      >
                        {userStatus === 'offline' && <span className="text-xs text-white">✓</span>}
                      </button>
                    </div>
                  </div>

                  {/* Dashboard link removed as per request */}
                  <Link
                    to="/profile"
                    className="block px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                    onClick={() => setIsProfileMenuOpen(false)}
                  >
                    Mi Perfil y Configuración
                  </Link>
                  {debugMode && (
                    <Link
                      to="/debug/auth"
                      className="block px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                      onClick={() => setIsProfileMenuOpen(false)}
                    >
                      Herramientas de Debug
                    </Link>
                  )}
                  <button
                    onClick={() => {
                      setIsProfileMenuOpen(false);
                      handleLogout();
                    }}
                    className="block w-full text-left px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                  >
                    Cerrar Sesión
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default AdminNavbar;

