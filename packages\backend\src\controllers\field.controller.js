const { Field, Form, Accelerator } = require('../models');
const { validationResult } = require('express-validator');

/**
 * Controlador para la gestión de campos de formulario
 */

// Crear un nuevo campo
exports.createField = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { 
      formId, 
      name, 
      label, 
      type, 
      placeholder, 
      helpText, 
      required, 
      order,
      options,
      validations,
      config
    } = req.body;

    // Verificar si el formulario existe
    const form = await Form.findByPk(formId, {
      include: [{ model: Accelerator }]
    });
    
    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'El formulario especificado no existe'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para añadir campos a este formulario'
        });
      }
    }

    // Crear el campo
    const newField = await Field.create({
      formId,
      name,
      label,
      type,
      placeholder: placeholder || '',
      helpText: helpText || '',
      required: required || false,
      order: order || 0,
      options: options || null,
      validations: validations || null,
      config: config || null,
      isActive: true
    });

    res.status(201).json({
      success: true,
      message: 'Campo creado correctamente',
      field: newField
    });
  } catch (error) {
    console.error('Error al crear campo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear campo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener todos los campos de un formulario
exports.getFieldsByFormId = async (req, res) => {
  try {
    const { formId } = req.params;
    
    // Verificar si el formulario existe
    const form = await Form.findByPk(formId);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    // Verificar permisos si no es GLOBAL_ADMIN y el formulario no está publicado
    if (req.user.role !== 'GLOBAL_ADMIN' && !form.isPublished) {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver los campos de este formulario'
        });
      }
    }

    // Obtener los campos ordenados
    const fields = await Field.findAll({
      where: { 
        formId,
        isActive: true 
      },
      order: [['order', 'ASC']]
    });

    res.status(200).json({
      success: true,
      fields
    });
  } catch (error) {
    console.error('Error al obtener campos:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener campos',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener un campo por ID
exports.getFieldById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Buscar el campo
    const field = await Field.findByPk(id, {
      include: [{ model: Form }]
    });

    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Campo no encontrado'
      });
    }

    // Verificar permisos si no es GLOBAL_ADMIN y el formulario no está publicado
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const form = await Form.findByPk(field.formId);
      if (!form.isPublished) {
        const accelerator = await Accelerator.findByPk(form.acceleratorId);
        const isAdmin = await accelerator.hasAdministrator(req.user.id);
        
        if (!isAdmin) {
          return res.status(403).json({
            success: false,
            message: 'No tienes permisos para ver este campo'
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      field
    });
  } catch (error) {
    console.error('Error al obtener campo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener campo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Actualizar un campo
exports.updateField = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { id } = req.params;
    const { 
      name, 
      label, 
      type, 
      placeholder, 
      helpText, 
      required, 
      order,
      options,
      validations,
      config,
      isActive
    } = req.body;

    // Buscar el campo
    const field = await Field.findByPk(id, {
      include: [{ model: Form }]
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Campo no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const form = await Form.findByPk(field.formId);
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para actualizar este campo'
        });
      }
    }

    // Actualizar el campo
    await field.update({
      name: name || field.name,
      label: label || field.label,
      type: type || field.type,
      placeholder: placeholder !== undefined ? placeholder : field.placeholder,
      helpText: helpText !== undefined ? helpText : field.helpText,
      required: required !== undefined ? required : field.required,
      order: order !== undefined ? order : field.order,
      options: options !== undefined ? options : field.options,
      validations: validations !== undefined ? validations : field.validations,
      config: config !== undefined ? config : field.config,
      isActive: isActive !== undefined ? isActive : field.isActive
    });

    res.status(200).json({
      success: true,
      message: 'Campo actualizado correctamente',
      field
    });
  } catch (error) {
    console.error('Error al actualizar campo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar campo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Eliminar un campo (desactivar)
exports.deleteField = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Buscar el campo
    const field = await Field.findByPk(id, {
      include: [{ model: Form }]
    });
    
    if (!field) {
      return res.status(404).json({
        success: false,
        message: 'Campo no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const form = await Form.findByPk(field.formId);
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para eliminar este campo'
        });
      }
    }

    // Desactivar el campo en lugar de eliminarlo
    await field.update({ isActive: false });

    res.status(200).json({
      success: true,
      message: 'Campo eliminado correctamente'
    });
  } catch (error) {
    console.error('Error al eliminar campo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar campo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Reordenar campos
exports.reorderFields = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { formId } = req.params;
    const { fieldOrders } = req.body;

    // Verificar si el formulario existe
    const form = await Form.findByPk(formId);
    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(form.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para reordenar los campos de este formulario'
        });
      }
    }

    // Actualizar el orden de los campos
    for (const item of fieldOrders) {
      await Field.update(
        { order: item.order },
        { where: { id: item.id, formId } }
      );
    }

    // Obtener los campos actualizados
    const updatedFields = await Field.findAll({
      where: { formId, isActive: true },
      order: [['order', 'ASC']]
    });

    res.status(200).json({
      success: true,
      message: 'Campos reordenados correctamente',
      fields: updatedFields
    });
  } catch (error) {
    console.error('Error al reordenar campos:', error);
    res.status(500).json({
      success: false,
      message: 'Error al reordenar campos',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
