import React, { useState, useEffect } from 'react';
import {
  FiCalendar,
  FiUsers,
  FiCheckCircle,
  FiClock,
  FiTrendingUp,
  FiTarget,
  FiBookOpen,
  FiActivity
} from 'react-icons/fi';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import { useNavigate } from 'react-router-dom';
import sessionService from '../../services/sessionService';
import taskService from '../../services/taskService';
import sessionNoteService from '../../services/sessionNoteService';
import userService from '../../services/user.service';
import useAuth from '../../hooks/useAuth';

const CoachingDashboardEnhanced = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalSessions: 0,
    completedSessions: 0,
    upcomingSessions: 0,
    totalTasks: 0,
    completedTasks: 0,
    overdueTasks: 0,
    totalNotes: 0,
    activeMentors: 0,
    activeEntrepreneurs: 0
  });
  const [recentSessions, setRecentSessions] = useState([]);
  const [upcomingTasks, setUpcomingTasks] = useState([]);
  const [recentNotes, setRecentNotes] = useState([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Cargar datos en paralelo
      const [
        sessionsResponse,
        tasksResponse,
        notesResponse,
        mentorsResponse,
        entrepreneursResponse
      ] = await Promise.all([
        sessionService.getAllSessions({ limit: 100 }),
        taskService.getAllTasks({ limit: 100 }),
        sessionNoteService.getAllNotes({ limit: 100 }),
        userService.getUsersByRole('MENTOR'),
        userService.getUsersByRole('ENTREPRENEUR')
      ]);

      // Procesar estadísticas de sesiones
      const sessions = sessionsResponse.sessions || [];
      const now = new Date();
      const completedSessions = sessions.filter(s => s.status === 'COMPLETED').length;
      const upcomingSessions = sessions.filter(s =>
        s.status === 'CONFIRMED' && new Date(s.startTime) > now
      ).length;

      // Procesar estadísticas de tareas
      const tasks = tasksResponse.tasks || [];
      const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
      const overdueTasks = tasks.filter(t =>
        t.status !== 'COMPLETED' && new Date(t.dueDate) < now
      ).length;

      // Procesar estadísticas de notas
      const notes = notesResponse.notes || [];

      // Actualizar estadísticas
      setStats({
        totalSessions: sessions.length,
        completedSessions,
        upcomingSessions,
        totalTasks: tasks.length,
        completedTasks,
        overdueTasks,
        totalNotes: notes.length,
        activeMentors: mentorsResponse.users?.length || 0,
        activeEntrepreneurs: entrepreneursResponse.users?.length || 0
      });

      // Obtener sesiones recientes (últimas 5 completadas)
      const recentCompletedSessions = sessions
        .filter(s => s.status === 'COMPLETED')
        .sort((a, b) => new Date(b.endTime) - new Date(a.endTime))
        .slice(0, 5);
      setRecentSessions(recentCompletedSessions);

      // Obtener tareas próximas a vencer (próximos 7 días)
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const upcomingTasksList = tasks
        .filter(t =>
          t.status !== 'COMPLETED' &&
          new Date(t.dueDate) <= nextWeek &&
          new Date(t.dueDate) >= now
        )
        .sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate))
        .slice(0, 5);
      setUpcomingTasks(upcomingTasksList);

      // Obtener notas recientes (últimas 5)
      const recentNotesList = notes
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
      setRecentNotes(recentNotesList);

    } catch (error) {
      console.error('Error al cargar datos del dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color = 'blue', onClick }) => (
    <Card className={`p-6 cursor-pointer hover:shadow-lg transition-shadow ${onClick ? 'hover:bg-gray-50' : ''}`} onClick={onClick}>
      <div className="flex items-center">
        <div className={`p-3 rounded-full bg-${color}-100 text-${color}-600 mr-4`}>
          <Icon size={24} />
        </div>
        <div className="flex-1">
          <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
        </div>
      </div>
    </Card>
  );

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTaskPriorityColor = (dueDate) => {
    const now = new Date();
    const due = new Date(dueDate);
    const daysUntilDue = Math.ceil((due - now) / (1000 * 60 * 60 * 24));

    if (daysUntilDue < 0) return 'text-red-600';
    if (daysUntilDue <= 1) return 'text-orange-600';
    if (daysUntilDue <= 3) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard de Coaching</h1>
          <p className="text-gray-600 mt-1">Resumen completo del sistema de mentoría</p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => navigate('/admin/sessions/new')}
            className="flex items-center space-x-2"
          >
            <FiCalendar size={16} />
            <span>Nueva Sesión</span>
          </Button>
          <Button
            onClick={() => navigate('/admin/tasks')}
            className="flex items-center space-x-2"
          >
            <FiTarget size={16} />
            <span>Ver Tareas</span>
          </Button>
        </div>
      </div>

      {/* Estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={FiCalendar}
          title="Total de Sesiones"
          value={stats.totalSessions}
          subtitle={`${stats.completedSessions} completadas`}
          color="blue"
          onClick={() => navigate('/admin/sessions')}
        />
        <StatCard
          icon={FiClock}
          title="Próximas Sesiones"
          value={stats.upcomingSessions}
          subtitle="Confirmadas"
          color="green"
          onClick={() => navigate('/admin/sessions')}
        />
        <StatCard
          icon={FiTarget}
          title="Tareas Activas"
          value={stats.totalTasks - stats.completedTasks}
          subtitle={`${stats.overdueTasks} vencidas`}
          color="orange"
          onClick={() => navigate('/admin/tasks')}
        />
        <StatCard
          icon={FiCheckCircle}
          title="Tareas Completadas"
          value={stats.completedTasks}
          subtitle={`${Math.round((stats.completedTasks / stats.totalTasks) * 100) || 0}% del total`}
          color="purple"
          onClick={() => navigate('/admin/tasks')}
        />
      </div>

      {/* Segunda fila de estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={FiBookOpen}
          title="Notas de Sesión"
          value={stats.totalNotes}
          subtitle="Total registradas"
          color="indigo"
          onClick={() => navigate('/admin/session-notes')}
        />
        <StatCard
          icon={FiUsers}
          title="Mentores Activos"
          value={stats.activeMentors}
          subtitle="En el sistema"
          color="teal"
        />
        <StatCard
          icon={FiTrendingUp}
          title="Emprendedores"
          value={stats.activeEntrepreneurs}
          subtitle="Registrados"
          color="cyan"
        />
        <StatCard
          icon={FiActivity}
          title="Tasa de Éxito"
          value={`${Math.round((stats.completedSessions / stats.totalSessions) * 100) || 0}%`}
          subtitle="Sesiones completadas"
          color="emerald"
        />
      </div>

      {/* Actividad reciente */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sesiones recientes */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Sesiones Recientes</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/sessions')}
            >
              Ver todas
            </Button>
          </div>
          <div className="space-y-3">
            {recentSessions.length > 0 ? (
              recentSessions.map((session) => (
                <div
                  key={session.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100"
                  onClick={() => navigate(`/admin/sessions/${session.id}`)}
                >
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 truncate">{session.title}</p>
                    <p className="text-sm text-gray-600">
                      {session.Mentor?.firstName} {session.Mentor?.lastName} → {session.Entrepreneur?.firstName} {session.Entrepreneur?.lastName}
                    </p>
                    <p className="text-xs text-gray-500">{formatDate(session.endTime)}</p>
                  </div>
                  <div className="ml-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Completada
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No hay sesiones recientes</p>
            )}
          </div>
        </Card>

        {/* Tareas próximas */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Tareas Próximas</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/tasks')}
            >
              Ver todas
            </Button>
          </div>
          <div className="space-y-3">
            {upcomingTasks.length > 0 ? (
              upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 truncate">{task.title}</p>
                    <p className="text-sm text-gray-600">
                      Asignada a: {task.AssignedTo?.firstName} {task.AssignedTo?.lastName}
                    </p>
                    <p className={`text-xs font-medium ${getTaskPriorityColor(task.dueDate)}`}>
                      Vence: {formatDate(task.dueDate)}
                    </p>
                  </div>
                  <div className="ml-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      task.priority === 'HIGH' ? 'bg-red-100 text-red-800' :
                      task.priority === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {task.priority === 'HIGH' ? 'Alta' : task.priority === 'MEDIUM' ? 'Media' : 'Baja'}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No hay tareas próximas</p>
            )}
          </div>
        </Card>

        {/* Notas recientes */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Notas Recientes</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin/session-notes')}
            >
              Ver todas
            </Button>
          </div>
          <div className="space-y-3">
            {recentNotes.length > 0 ? (
              recentNotes.map((note) => (
                <div
                  key={note.id}
                  className="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100"
                  onClick={() => navigate(`/admin/sessions/${note.sessionId}`)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 line-clamp-2">
                        {note.content.substring(0, 100)}
                        {note.content.length > 100 ? '...' : ''}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        Por: {note.Author?.firstName} {note.Author?.lastName}
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(note.createdAt)}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-4">No hay notas recientes</p>
            )}
          </div>
        </Card>
      </div>

      {/* Acciones rápidas */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Acciones Rápidas</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2 p-4"
            onClick={() => navigate('/admin/sessions/new')}
          >
            <FiCalendar size={20} />
            <span>Programar Sesión</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2 p-4"
            onClick={() => navigate('/admin/tasks')}
          >
            <FiTarget size={20} />
            <span>Crear Tarea</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2 p-4"
            onClick={() => navigate('/admin/session-notes')}
          >
            <FiBookOpen size={20} />
            <span>Agregar Nota</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center justify-center space-x-2 p-4"
            onClick={() => window.location.reload()}
          >
            <FiActivity size={20} />
            <span>Actualizar Datos</span>
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default CoachingDashboardEnhanced;