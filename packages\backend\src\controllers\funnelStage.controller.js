const { FunnelStage, Accelerator, Application } = require('../models');
const { validationResult } = require('express-validator');

/**
 * Controlador para la gestión de etapas del embudo (funnel stages)
 */

// Crear una nueva etapa del embudo
exports.createFunnelStage = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const {
      name,
      description,
      acceleratorId,
      order,
      color,
      config
    } = req.body;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'La aceleradora especificada no existe'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para crear etapas en esta aceleradora'
        });
      }
    }

    // Si no se especifica un orden, obtener el máximo orden actual y sumar 1
    let stageOrder = order;
    if (!stageOrder) {
      const maxOrderStage = await FunnelStage.findOne({
        where: { acceleratorId },
        order: [['order', 'DESC']]
      });
      stageOrder = maxOrderStage ? maxOrderStage.order + 1 : 1;
    }

    // Crear la etapa del embudo
    const newStage = await FunnelStage.create({
      name,
      description,
      acceleratorId,
      order: stageOrder,
      color: color || '#3B82F6', // Color azul por defecto
      config: config || {},
      isActive: true
    });

    res.status(201).json({
      success: true,
      message: 'Etapa del embudo creada correctamente',
      stage: newStage
    });
  } catch (error) {
    console.error('Error al crear etapa del embudo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear etapa del embudo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener todas las etapas del embudo de una aceleradora
exports.getFunnelStagesByAccelerator = async (req, res) => {
  try {
    const { acceleratorId } = req.params;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada'
      });
    }

    // Verificar permisos si no es GLOBAL_ADMIN
    if (req.user.role !== 'GLOBAL_ADMIN' && req.user.role !== 'ACCELERATOR_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para ver las etapas de esta aceleradora'
      });
    } else if (req.user.role === 'ACCELERATOR_ADMIN') {
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver las etapas de esta aceleradora'
        });
      }
    }

    // Obtener las etapas ordenadas
    const stages = await FunnelStage.findAll({
      where: {
        acceleratorId,
        isActive: true
      },
      order: [['order', 'ASC']]
    });

    // Para cada etapa, devolver 0 como número de solicitudes ya que no podemos consultar por funnelStageId
    const stagesWithCounts = stages.map(stage => {
      return {
        ...stage.toJSON(),
        applicationCount: 0 // No podemos contar aplicaciones por etapa ya que no existe la columna funnelStageId
      };
    });

    res.status(200).json({
      success: true,
      stages: stagesWithCounts
    });
  } catch (error) {
    console.error('Error al obtener etapas del embudo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener etapas del embudo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener una etapa del embudo por ID
exports.getFunnelStageById = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar la etapa
    const stage = await FunnelStage.findByPk(id, {
      include: [{ model: Accelerator }]
    });

    if (!stage) {
      return res.status(404).json({
        success: false,
        message: 'Etapa del embudo no encontrada'
      });
    }

    // Verificar permisos si no es GLOBAL_ADMIN
    if (req.user.role !== 'GLOBAL_ADMIN' && req.user.role !== 'ACCELERATOR_ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para ver esta etapa'
      });
    } else if (req.user.role === 'ACCELERATOR_ADMIN') {
      const accelerator = await Accelerator.findByPk(stage.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver esta etapa'
        });
      }
    }

    // No podemos contar aplicaciones por etapa ya que no existe la columna funnelStageId
    const applicationCount = 0;

    res.status(200).json({
      success: true,
      stage: {
        ...stage.toJSON(),
        applicationCount
      }
    });
  } catch (error) {
    console.error('Error al obtener etapa del embudo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener etapa del embudo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Actualizar una etapa del embudo
exports.updateFunnelStage = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { id } = req.params;
    const {
      name,
      description,
      order,
      color,
      config,
      isActive
    } = req.body;

    // Buscar la etapa
    const stage = await FunnelStage.findByPk(id);
    if (!stage) {
      return res.status(404).json({
        success: false,
        message: 'Etapa del embudo no encontrada'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(stage.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para actualizar esta etapa'
        });
      }
    }

    // Actualizar la etapa
    await stage.update({
      name: name || stage.name,
      description: description !== undefined ? description : stage.description,
      order: order !== undefined ? order : stage.order,
      color: color || stage.color,
      config: config !== undefined ? config : stage.config,
      isActive: isActive !== undefined ? isActive : stage.isActive
    });

    res.status(200).json({
      success: true,
      message: 'Etapa del embudo actualizada correctamente',
      stage
    });
  } catch (error) {
    console.error('Error al actualizar etapa del embudo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar etapa del embudo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Eliminar una etapa del embudo (desactivar)
exports.deleteFunnelStage = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar la etapa
    const stage = await FunnelStage.findByPk(id);
    if (!stage) {
      return res.status(404).json({
        success: false,
        message: 'Etapa del embudo no encontrada'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const accelerator = await Accelerator.findByPk(stage.acceleratorId);
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para eliminar esta etapa'
        });
      }
    }

    // No podemos verificar si hay solicitudes en esta etapa ya que no existe la columna funnelStageId
    // const applicationsCount = await Application.count({
    //   where: { funnelStageId: id }
    // });
    //
    // if (applicationsCount > 0) {
    //   return res.status(400).json({
    //     success: false,
    //     message: 'No se puede eliminar esta etapa porque hay solicitudes asociadas a ella'
    //   });
    // }

    // Desactivar la etapa en lugar de eliminarla
    await stage.update({ isActive: false });

    res.status(200).json({
      success: true,
      message: 'Etapa del embudo eliminada correctamente'
    });
  } catch (error) {
    console.error('Error al eliminar etapa del embudo:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar etapa del embudo',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Reordenar etapas del embudo
exports.reorderFunnelStages = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { acceleratorId } = req.params;
    const { stageOrders } = req.body;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para reordenar las etapas de esta aceleradora'
        });
      }
    }

    // Actualizar el orden de las etapas
    for (const item of stageOrders) {
      await FunnelStage.update(
        { order: item.order },
        { where: { id: item.id, acceleratorId } }
      );
    }

    // Obtener las etapas actualizadas
    const updatedStages = await FunnelStage.findAll({
      where: { acceleratorId, isActive: true },
      order: [['order', 'ASC']]
    });

    res.status(200).json({
      success: true,
      message: 'Etapas reordenadas correctamente',
      stages: updatedStages
    });
  } catch (error) {
    console.error('Error al reordenar etapas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al reordenar etapas',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Inicializar etapas predeterminadas para una aceleradora
exports.initDefaultStages = async (req, res) => {
  try {
    const { acceleratorId } = req.params;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada'
      });
    }

    // Verificar permisos
    if (req.user.role !== 'GLOBAL_ADMIN') {
      const isAdmin = await accelerator.hasAdministrator(req.user.id);
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para inicializar etapas en esta aceleradora'
        });
      }
    }

    // Verificar si ya existen etapas para esta aceleradora
    const existingStages = await FunnelStage.findAll({
      where: { acceleratorId }
    });

    if (existingStages.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Esta aceleradora ya tiene etapas definidas'
      });
    }

    // Inicializar etapas predeterminadas
    const success = await FunnelStage.initDefaultStages(acceleratorId);

    if (!success) {
      return res.status(500).json({
        success: false,
        message: 'Error al inicializar etapas predeterminadas'
      });
    }

    // Obtener las etapas creadas
    const stages = await FunnelStage.findAll({
      where: { acceleratorId },
      order: [['order', 'ASC']]
    });

    res.status(201).json({
      success: true,
      message: 'Etapas predeterminadas inicializadas correctamente',
      stages
    });
  } catch (error) {
    console.error('Error al inicializar etapas predeterminadas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al inicializar etapas predeterminadas',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
