const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Session = sequelize.define('Session', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  mentorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  entrepreneurId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: { model: 'Users', key: 'id' }
  },
  acceleratorId: { // To scope sessions to an accelerator program
    type: DataTypes.INTEGER,
    allowNull: true, // Or false if always required
    references: { model: 'Accelerators', key: 'id' }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  status: { // e.g., 'scheduled', 'completed', 'cancelled_mentor', 'cancelled_entrepreneur', 'pending_confirmation'
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'pending_confirmation',
  },
  location: { // Could be 'online' or a physical address
    type: DataTypes.STRING,
    allowNull: true,
  },
  videoMeetingLink: { // For Zoom or other meeting links
    type: DataTypes.STRING,
    allowNull: true,
  },
  googleCalendarEventId: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  microsoftCalendarEventId: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  // Add other relevant fields like recurrence rules if needed later
}, { timestamps: true });

module.exports = Session;
