import React, { useState, useEffect } from 'react';
import { Routes, Route, Link, useNavigate } from 'react-router-dom';
import { FiPlus, FiList } from 'react-icons/fi';
import AdminLayout from '../../../components/admin/AdminLayout';
import Button from '../../../components/common/Button';
import FormList from '../../../components/forms/FormList';
import CreateForm from '../../../components/forms/CreateForm';
import FormDetails from '../../../components/forms/FormDetails';
import EditForm from '../../../components/forms/EditForm';
import useAuth from '../../../hooks/useAuth';
import api from '../../../services/api';

/**
 * Página principal del Form Builder
 *
 * Esta página contiene las rutas anidadas para:
 * - Listar formularios
 * - Crear un nuevo formulario
 * - Ver detalles de un formulario
 * - Editar un formulario
 */
const FormBuilderPage = () => {
  const [notification, setNotification] = useState(null);
  const [accelerators, setAccelerators] = useState([]);
  const [loading, setLoading] = useState(false);
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Cargar aceleradoras al montar el componente
  useEffect(() => {
    const fetchAccelerators = async () => {
      try {
        setLoading(true);
        const response = await api.get('/accelerators');
        if (response.data.success) {
          setAccelerators(response.data.accelerators);
        }
      } catch (error) {
        console.error('Error al cargar aceleradoras:', error);
        setNotification({
          type: 'error',
          message: 'Error al cargar aceleradoras. Por favor, intente nuevamente.'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAccelerators();
  }, []);

  // Mostrar notificación y limpiarla después de 5 segundos
  const showNotification = (type, message) => {
    setNotification({ type, message });
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-6">
        {/* Encabezado */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Gestor de Formularios</h1>
          <div className="flex space-x-2">
            <Link to="/admin/forms">
              <Button
                variant="outline"
                className="flex items-center"
              >
                <FiList className="mr-2" /> Ver Formularios
              </Button>
            </Link>
            <Link to="/admin/forms/new">
              <Button
                variant="primary"
                className="flex items-center"
              >
                <FiPlus className="mr-2" /> Nuevo Formulario
              </Button>
            </Link>
          </div>
        </div>

        {/* Notificación */}
        {notification && (
          <div className={`mb-4 p-4 rounded-md ${
            notification.type === 'error'
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}>
            {notification.message}
            <button
              onClick={() => setNotification(null)}
              className="float-right text-gray-500 hover:text-gray-700"
            >
              &times;
            </button>
          </div>
        )}

        {/* Rutas anidadas */}
        <Routes>
          <Route
            index
            element={
              <FormList
                showNotification={showNotification}
              />
            }
          />
          <Route
            path="new"
            element={
              <CreateForm
                accelerators={accelerators}
                showNotification={showNotification}
                navigate={navigate}
              />
            }
          />
          <Route
            path=":id"
            element={
              <FormDetails
                showNotification={showNotification}
              />
            }
          />
          <Route
            path=":id/edit"
            element={
              <EditForm
                accelerators={accelerators}
                showNotification={showNotification}
                navigate={navigate}
              />
            }
          />
        </Routes>
      </div>
    </AdminLayout>
  );
};

export default FormBuilderPage;
