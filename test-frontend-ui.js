/**
 * Script de Pruebas de UI del Frontend
 * Proyecto Bumeran - Verificación de Interfaz de Usuario
 */

const axios = require('axios');

// Configuración
const FRONTEND_URL = 'http://localhost:5173';
const API_BASE_URL = 'http://localhost:5000/api';

// Credenciales de prueba
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`${title}`, 'bold');
  console.log('='.repeat(60));
}

// Función para obtener token de autenticación
async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    return response.data.token;
  } catch (error) {
    log(`Error al obtener token: ${error.message}`, 'red');
    return null;
  }
}

// Función para verificar datos existentes en el sistema
async function checkExistingData() {
  logSection('📊 VERIFICACIÓN DE DATOS EXISTENTES');
  
  const token = await getAuthToken();
  if (!token) {
    log('No se pudo obtener token de autenticación', 'red');
    return;
  }

  const headers = { Authorization: `Bearer ${token}` };

  try {
    // Verificar aceleradoras
    const acceleratorsResponse = await axios.get(`${API_BASE_URL}/accelerators`, { headers });
    log(`✅ Aceleradoras: ${acceleratorsResponse.data.accelerators?.length || 0} encontradas`, 'green');
    
    if (acceleratorsResponse.data.accelerators?.length > 0) {
      acceleratorsResponse.data.accelerators.slice(0, 3).forEach((acc, index) => {
        log(`   ${index + 1}. ${acc.name} (${acc.status})`, 'yellow');
      });
    }

    // Verificar formularios
    const formsResponse = await axios.get(`${API_BASE_URL}/forms`, { headers });
    log(`✅ Formularios: ${formsResponse.data.forms?.length || 0} encontrados`, 'green');
    
    if (formsResponse.data.forms?.length > 0) {
      formsResponse.data.forms.slice(0, 3).forEach((form, index) => {
        log(`   ${index + 1}. ${form.title} (${form.status})`, 'yellow');
      });
    }

    // Verificar aplicaciones
    const applicationsResponse = await axios.get(`${API_BASE_URL}/applications`, { headers });
    log(`✅ Aplicaciones: ${applicationsResponse.data.applications?.length || 0} encontradas`, 'green');

    // Verificar usuarios
    const usersResponse = await axios.get(`${API_BASE_URL}/users`, { headers });
    log(`✅ Usuarios: ${usersResponse.data.users?.length || 0} encontrados`, 'green');
    
    if (usersResponse.data.users?.length > 0) {
      usersResponse.data.users.slice(0, 3).forEach((user, index) => {
        log(`   ${index + 1}. ${user.firstName} ${user.lastName} (${user.Role?.name || 'Sin rol'})`, 'yellow');
      });
    }

    // Verificar códigos de registro
    try {
      const codesResponse = await axios.get(`${API_BASE_URL}/registration-codes`, { headers });
      log(`✅ Códigos de registro: ${codesResponse.data.codes?.length || 0} encontrados`, 'green');
    } catch (error) {
      log(`⚠️  Códigos de registro: Error al obtener (${error.response?.status || 'Error'})`, 'yellow');
    }

  } catch (error) {
    log(`Error al verificar datos: ${error.message}`, 'red');
  }
}

// Función para generar instrucciones de pruebas manuales
function generateManualTestInstructions() {
  logSection('🧪 INSTRUCCIONES PARA PRUEBAS MANUALES DEL FRONTEND');
  
  log('Por favor, realiza las siguientes pruebas en el navegador:', 'blue');
  log(`Frontend URL: ${FRONTEND_URL}`, 'yellow');
  
  console.log('\n📋 LISTA DE VERIFICACIÓN:');
  
  const testCases = [
    {
      section: '🔐 AUTENTICACIÓN',
      tests: [
        'Acceder a la página principal (landing page)',
        'Hacer clic en "Iniciar Sesión"',
        `Ingresar credenciales: ${ADMIN_CREDENTIALS.email} / ${ADMIN_CREDENTIALS.password}`,
        'Verificar redirección al dashboard de administrador',
        'Verificar que aparezca el menú de navegación lateral',
        'Verificar que el nombre del usuario aparezca en la interfaz'
      ]
    },
    {
      section: '🚀 GESTIÓN DE ACELERADORAS',
      tests: [
        'Navegar a "Aceleradoras" desde el menú lateral',
        'Verificar que se muestren las aceleradoras existentes',
        'Hacer clic en "Nueva Aceleradora"',
        'Llenar el formulario de creación',
        'Guardar y verificar que aparezca en la lista',
        'Hacer clic en una aceleradora para ver detalles',
        'Probar la función de edición'
      ]
    },
    {
      section: '📝 FORM BUILDER',
      tests: [
        'Navegar a "Formularios" desde el menú lateral',
        'Verificar que se muestren los formularios existentes',
        'Hacer clic en "Nuevo Formulario"',
        'Usar el constructor de formularios para agregar campos',
        'Agregar diferentes tipos de campos (texto, email, select)',
        'Guardar el formulario como borrador',
        'Publicar el formulario',
        'Verificar el preview del formulario'
      ]
    },
    {
      section: '📋 GESTIÓN DE APLICACIONES',
      tests: [
        'Navegar a "Aplicaciones" desde el menú lateral',
        'Verificar que se muestren las aplicaciones existentes',
        'Probar los filtros por estado',
        'Verificar que las etapas del embudo se muestren correctamente',
        'Hacer clic en una aplicación para ver detalles'
      ]
    },
    {
      section: '👥 GESTIÓN DE USUARIOS',
      tests: [
        'Navegar a "Usuarios" desde el menú lateral',
        'Verificar que se muestren los usuarios existentes',
        'Probar la función de búsqueda',
        'Navegar a "Códigos de Registro"',
        'Crear un nuevo código de registro',
        'Verificar que aparezca en la lista'
      ]
    },
    {
      section: '🎨 INTERFAZ Y NAVEGACIÓN',
      tests: [
        'Verificar que el diseño sea responsive',
        'Probar la navegación entre diferentes secciones',
        'Verificar que no haya errores en la consola del navegador',
        'Comprobar que los botones y enlaces funcionen correctamente',
        'Verificar que los formularios validen correctamente',
        'Probar el logout y verificar redirección'
      ]
    }
  ];

  testCases.forEach((section, sectionIndex) => {
    console.log(`\n${section.section}`);
    section.tests.forEach((test, testIndex) => {
      console.log(`   ${sectionIndex + 1}.${testIndex + 1} ${test}`);
    });
  });

  logSection('⚠️  PROBLEMAS CONOCIDOS A VERIFICAR');
  log('Basado en las pruebas automatizadas, presta especial atención a:', 'yellow');
  log('• Form Builder: Puede haber problemas al crear formularios nuevos', 'red');
  log('• Códigos de registro: Posible error en la obtención de códigos', 'red');
  log('• Etapas del embudo: Verificar que se muestren correctamente', 'yellow');

  logSection('✅ CRITERIOS DE ÉXITO');
  log('El frontend se considera completamente funcional si:', 'blue');
  log('• Login y navegación funcionan sin errores', 'green');
  log('• Todas las operaciones CRUD desde UI funcionan', 'green');
  log('• No hay errores críticos en consola', 'green');
  log('• Las respuestas del backend se muestran correctamente en UI', 'green');
  log('• El sistema es usable para un administrador real', 'green');
}

// Función principal
async function runUITests() {
  console.clear();
  logSection('🎨 PRUEBAS DE INTERFAZ DE USUARIO - BUMERAN');
  log('Verificando datos del sistema y generando instrucciones de prueba...', 'blue');

  await checkExistingData();
  generateManualTestInstructions();

  logSection('🎯 PRÓXIMOS PASOS');
  log('1. Abre el navegador en: http://localhost:5173', 'blue');
  log('2. Sigue las instrucciones de prueba manual arriba', 'blue');
  log('3. Reporta cualquier problema encontrado', 'blue');
  log('4. Verifica que no haya errores en la consola del navegador', 'blue');
}

// Ejecutar las pruebas
if (require.main === module) {
  runUITests().catch(error => {
    log(`Error en las pruebas de UI: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runUITests };
