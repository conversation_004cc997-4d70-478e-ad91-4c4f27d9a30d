{"timestamp": "2025-05-24T20:16:56.880Z", "duration": "0.23s", "summary": {"total": 15, "passed": 12, "failed": 3, "successRate": "80.0%"}, "tests": [{"name": "Backend disponible", "passed": true, "details": "Puerto 5000 - Status: 200"}, {"name": "Frontend disponible", "passed": true, "details": "Puerto 5173 - Status: 200"}, {"name": "<PERSON>gin exitoso", "passed": true, "details": "Token recibido: eyJhbGciOiJIUzI1NiIs..."}, {"name": "Datos de usuario recibidos", "passed": true, "details": "Usuario: <EMAIL>"}, {"name": "Rol de administrador", "passed": true, "details": "Rol: GLOBAL_ADMIN"}, {"name": "Acceso a perfil autenticado", "passed": true, "details": "Email: undefined"}, {"name": "<PERSON><PERSON>zo de credenciales incorrectas", "passed": true, "details": "Status: 401"}, {"name": "Listar aceler<PERSON>", "passed": true, "details": "Encontradas: 9"}, {"name": "<PERSON><PERSON><PERSON> ace<PERSON>", "passed": true, "details": "ID: undefined"}, {"name": "Listar formularios", "passed": true, "details": "Encontrados: 3"}, {"name": "Form Builder", "passed": false, "details": "Error: Request failed with status code 400"}, {"name": "Listar aplicaciones", "passed": true, "details": "Encontradas: 2"}, {"name": "Gestión de aplicaciones", "passed": false, "details": "Error: Request failed with status code 404"}, {"name": "Listar usuarios", "passed": true, "details": "Encontrados: 4"}, {"name": "Gestión de usuarios", "passed": false, "details": "Error: <PERSON><PERSON><PERSON> al obtener códigos de registro"}]}