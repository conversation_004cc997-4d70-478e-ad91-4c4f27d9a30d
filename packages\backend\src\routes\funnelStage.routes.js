const express = require('express');
const router = express.Router();
const funnelStageController = require('../controllers/funnelStage.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateFunnelStage, validateUpdateFunnelStage, validateReorderFunnelStages } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para etapas del embudo
router.post('/', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateCreateFunnelStage, 
  funnelStageController.createFunnelStage
);

router.get('/accelerator/:acceleratorId', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), 
  funnelStageController.getFunnelStagesByAccelerator
);

router.get('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR']), 
  funnelStageController.getFunnelStageById
);

router.put('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateUpdateFunnelStage, 
  funnelStageController.updateFunnelStage
);

router.delete('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  funnelStageController.deleteFunnelStage
);

router.put('/accelerator/:acceleratorId/reorder', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateReorderFunnelStages, 
  funnelStageController.reorderFunnelStages
);

router.post('/accelerator/:acceleratorId/init-default', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  funnelStageController.initDefaultStages
);

module.exports = router;
