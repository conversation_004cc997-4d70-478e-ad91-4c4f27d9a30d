/**
 * Script para debuggear parámetros específicos del endpoint de aceleradoras
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(`${title}`, 'bold');
  console.log('='.repeat(60));
}

async function getAuthToken() {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    return response.data.token;
  } catch (error) {
    log(`Error al obtener token: ${error.message}`, 'red');
    return null;
  }
}

async function testParameterCombinations() {
  logSection('🔍 TESTING PARAMETER COMBINATIONS');
  
  const token = await getAuthToken();
  if (!token) return;
  
  const headers = { Authorization: `Bearer ${token}` };
  
  // Diferentes combinaciones de parámetros para probar
  const testCases = [
    {
      name: 'Sin parámetros',
      params: {}
    },
    {
      name: 'Solo page y limit',
      params: { page: 1, limit: 10 }
    },
    {
      name: 'Con search vacío',
      params: { page: 1, limit: 10, search: '' }
    },
    {
      name: 'Con status vacío',
      params: { page: 1, limit: 10, search: '', status: '' }
    },
    {
      name: 'Con sortBy solamente',
      params: { page: 1, limit: 10, search: '', status: '', sortBy: 'name' }
    },
    {
      name: 'Con sortOrder solamente',
      params: { page: 1, limit: 10, search: '', status: '', sortOrder: 'ASC' }
    },
    {
      name: 'Con sortBy y sortOrder (problema original)',
      params: { page: 1, limit: 10, search: '', status: '', sortBy: 'name', sortOrder: 'ASC' }
    },
    {
      name: 'Con sortBy diferente',
      params: { page: 1, limit: 10, search: '', status: '', sortBy: 'createdAt', sortOrder: 'ASC' }
    },
    {
      name: 'Con sortOrder DESC',
      params: { page: 1, limit: 10, search: '', status: '', sortBy: 'name', sortOrder: 'DESC' }
    },
    {
      name: 'Con status active',
      params: { page: 1, limit: 10, search: '', status: 'active', sortBy: 'name', sortOrder: 'ASC' }
    },
    {
      name: 'Con search no vacío',
      params: { page: 1, limit: 10, search: 'Daniel', status: '', sortBy: 'name', sortOrder: 'ASC' }
    }
  ];
  
  for (const testCase of testCases) {
    try {
      log(`\n🧪 Probando: ${testCase.name}`, 'blue');
      
      const queryParams = new URLSearchParams(testCase.params);
      const url = `${API_BASE_URL}/accelerators?${queryParams}`;
      log(`URL: ${url}`, 'cyan');
      
      const response = await axios.get(url, { headers });
      
      const count = response.data.accelerators?.length || 0;
      const status = count > 0 ? '✅' : '❌';
      const color = count > 0 ? 'green' : 'red';
      
      log(`${status} Resultado: ${count} aceleradoras encontradas`, color);
      
      if (count === 0 && response.data.accelerators !== undefined) {
        log(`   Estructura de respuesta:`, 'yellow');
        console.log(`   ${JSON.stringify(response.data, null, 4)}`);
      }
      
    } catch (error) {
      log(`❌ Error: ${error.message}`, 'red');
      if (error.response) {
        log(`   Status: ${error.response.status}`, 'red');
        log(`   Data: ${JSON.stringify(error.response.data, null, 4)}`, 'red');
      }
    }
  }
}

async function testSpecificIssue() {
  logSection('🎯 TESTING SPECIFIC ISSUE');
  
  const token = await getAuthToken();
  if (!token) return;
  
  const headers = { Authorization: `Bearer ${token}` };
  
  try {
    // Probar la consulta exacta que está fallando
    log('Probando la consulta exacta que usa el frontend:', 'blue');
    
    const problematicUrl = `${API_BASE_URL}/accelerators?page=1&limit=10&search=&status=&sortBy=name&sortOrder=ASC`;
    log(`URL problemática: ${problematicUrl}`, 'cyan');
    
    const response = await axios.get(problematicUrl, { headers });
    
    log('Respuesta completa:', 'yellow');
    console.log(JSON.stringify(response.data, null, 2));
    
    // Verificar si el problema está en el ordenamiento
    log('\nProbando sin ordenamiento:', 'blue');
    const withoutSortUrl = `${API_BASE_URL}/accelerators?page=1&limit=10&search=&status=`;
    const responseWithoutSort = await axios.get(withoutSortUrl, { headers });
    
    log(`Sin ordenamiento: ${responseWithoutSort.data.accelerators?.length || 0} aceleradoras`, 'green');
    
  } catch (error) {
    log(`Error en prueba específica: ${error.message}`, 'red');
    if (error.response) {
      console.log(JSON.stringify(error.response.data, null, 2));
    }
  }
}

async function runParameterDebug() {
  console.clear();
  log('🐛 DEBUG: PARÁMETROS DEL ENDPOINT DE ACELERADORAS', 'bold');
  log('Investigando qué parámetro específico está causando el problema...', 'blue');
  
  await testParameterCombinations();
  await testSpecificIssue();
  
  logSection('💡 ANÁLISIS');
  log('Basado en las pruebas, el problema parece estar en:', 'yellow');
  log('1. Los parámetros de ordenamiento (sortBy/sortOrder)', 'yellow');
  log('2. La combinación de parámetros vacíos con ordenamiento', 'yellow');
  log('3. Posible problema en el controlador con parámetros de query', 'yellow');
}

// Ejecutar el debug
if (require.main === module) {
  runParameterDebug().catch(error => {
    log(`Error fatal en debug: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runParameterDebug };
