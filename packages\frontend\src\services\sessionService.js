import api from './api';

class SessionService {
  // Obtener todas las sesiones con filtros y paginación
  async getAllSessions(filters = {}) {
    try {
      const params = new URLSearchParams({
        page: filters.page || 1,
        limit: filters.limit || 10,
        search: filters.search || '',
        status: filters.status || '',
        mentorId: filters.mentorId || '',
        entrepreneurId: filters.entrepreneurId || '',
        acceleratorId: filters.acceleratorId || '',
        sortBy: filters.sortBy || 'startTime',
        sortOrder: filters.sortOrder || 'DESC'
      });

      const response = await api.get(`/sessions?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener sesiones:', error);
      throw error;
    }
  }

  // Obtener una sesión por ID
  async getSessionById(sessionId) {
    try {
      const response = await api.get(`/sessions/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener sesión:', error);
      throw error;
    }
  }

  // Crear una nueva sesión
  async createSession(sessionData) {
    try {
      const response = await api.post('/sessions', sessionData);
      return response.data;
    } catch (error) {
      console.error('Error al crear sesión:', error);
      throw error;
    }
  }

  // Actualizar una sesión
  async updateSession(sessionId, sessionData) {
    try {
      const response = await api.put(`/sessions/${sessionId}`, sessionData);
      return response.data;
    } catch (error) {
      console.error('Error al actualizar sesión:', error);
      throw error;
    }
  }

  // Confirmar una sesión
  async confirmSession(sessionId) {
    try {
      const response = await api.patch(`/sessions/${sessionId}/confirm`);
      return response.data;
    } catch (error) {
      console.error('Error al confirmar sesión:', error);
      throw error;
    }
  }

  // Cancelar una sesión
  async cancelSession(sessionId, cancelData) {
    try {
      const response = await api.patch(`/sessions/${sessionId}/cancel`, cancelData);
      return response.data;
    } catch (error) {
      console.error('Error al cancelar sesión:', error);
      throw error;
    }
  }

  // Completar una sesión
  async completeSession(sessionId) {
    try {
      const response = await api.patch(`/sessions/${sessionId}/complete`);
      return response.data;
    } catch (error) {
      console.error('Error al completar sesión:', error);
      throw error;
    }
  }

  // Obtener sesiones por mentor
  async getSessionsByMentor(mentorId, filters = {}) {
    try {
      const params = new URLSearchParams({
        ...filters,
        mentorId
      });

      const response = await api.get(`/sessions?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener sesiones del mentor:', error);
      throw error;
    }
  }

  // Obtener sesiones por emprendedor
  async getSessionsByEntrepreneur(entrepreneurId, filters = {}) {
    try {
      const params = new URLSearchParams({
        ...filters,
        entrepreneurId
      });

      const response = await api.get(`/sessions?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error al obtener sesiones del emprendedor:', error);
      throw error;
    }
  }

  // Obtener sesiones próximas
  async getUpcomingSessions(userId, userRole) {
    try {
      const filters = {
        status: 'scheduled',
        sortBy: 'startTime',
        sortOrder: 'ASC',
        limit: 5
      };

      if (userRole === 'MENTOR') {
        filters.mentorId = userId;
      } else if (userRole === 'ENTREPRENEUR') {
        filters.entrepreneurId = userId;
      }

      const response = await this.getAllSessions(filters);
      return response;
    } catch (error) {
      console.error('Error al obtener sesiones próximas:', error);
      throw error;
    }
  }

  // Obtener estadísticas de sesiones
  async getSessionStats(filters = {}) {
    try {
      const allSessions = await this.getAllSessions({ ...filters, limit: 1000 });
      const sessions = allSessions.sessions || [];

      const stats = {
        total: sessions.length,
        byStatus: {},
        thisMonth: 0,
        thisWeek: 0
      };

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));

      sessions.forEach(session => {
        // Contar por estado
        stats.byStatus[session.status] = (stats.byStatus[session.status] || 0) + 1;

        // Contar este mes
        const sessionDate = new Date(session.startTime);
        if (sessionDate >= startOfMonth) {
          stats.thisMonth++;
        }

        // Contar esta semana
        if (sessionDate >= startOfWeek) {
          stats.thisWeek++;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error al obtener estadísticas de sesiones:', error);
      throw error;
    }
  }

  // Verificar disponibilidad de horario
  async checkAvailability(mentorId, startTime, endTime, excludeSessionId = null) {
    try {
      const filters = {
        mentorId,
        limit: 1000
      };

      const response = await this.getAllSessions(filters);
      const sessions = response.sessions || [];

      const start = new Date(startTime);
      const end = new Date(endTime);

      const conflicts = sessions.filter(session => {
        if (excludeSessionId && session.id === excludeSessionId) {
          return false;
        }

        if (['cancelled_mentor', 'cancelled_entrepreneur'].includes(session.status)) {
          return false;
        }

        const sessionStart = new Date(session.startTime);
        const sessionEnd = new Date(session.endTime);

        return (
          (start >= sessionStart && start < sessionEnd) ||
          (end > sessionStart && end <= sessionEnd) ||
          (start <= sessionStart && end >= sessionEnd)
        );
      });

      return {
        available: conflicts.length === 0,
        conflicts
      };
    } catch (error) {
      console.error('Error al verificar disponibilidad:', error);
      throw error;
    }
  }
}

export default new SessionService();
