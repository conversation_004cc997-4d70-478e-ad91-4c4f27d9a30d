const { Application, Form, User, Accelerator, FunnelStage, Field } = require('../models');
const { validationResult } = require('express-validator');

/**
 * Controlador para la gestión de solicitudes (applications)
 */

// Crear una nueva solicitud
exports.createApplication = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { formId, responses } = req.body;

    // Verificar si el formulario existe y está publicado
    const form = await Form.findByPk(formId, {
      include: [
        { model: Field, where: { isActive: true } },
        { model: Accelerator }
      ]
    });

    if (!form) {
      return res.status(404).json({
        success: false,
        message: 'Formulario no encontrado'
      });
    }

    if (form.status !== 'published') {
      return res.status(400).json({
        success: false,
        message: 'Este formulario no está disponible para envío de solicitudes'
      });
    }

    // Verificar si el formulario está dentro del período válido
    const now = new Date();
    const settings = form.settings || {};
    if (settings.startDate && now < new Date(settings.startDate)) {
      return res.status(400).json({
        success: false,
        message: 'Este formulario aún no está disponible para envío de solicitudes'
      });
    }

    if (settings.endDate && now > new Date(settings.endDate)) {
      return res.status(400).json({
        success: false,
        message: 'El período para enviar solicitudes a este formulario ha finalizado'
      });
    }

    // Validar que se hayan proporcionado respuestas para todos los campos requeridos
    const requiredFields = form.Fields.filter(field => field.required);
    for (const field of requiredFields) {
      if (responses[field.name] === undefined || responses[field.name] === null || responses[field.name] === '') {
        return res.status(400).json({
          success: false,
          message: `El campo "${field.label}" es obligatorio`
        });
      }
    }

    // Obtener la primera etapa del embudo para esta aceleradora
    const firstStage = await FunnelStage.findOne({
      where: {
        acceleratorId: form.acceleratorId,
        order: 1,
        isActive: true
      }
    });

    // Crear la solicitud
    const newApplication = await Application.create({
      formId,
      applicantId: req.user.id,
      acceleratorId: form.acceleratorId,
      funnelStageId: firstStage ? firstStage.id : null,
      responses: responses,
      status: 'pending',
      submittedAt: new Date(),
      statusUpdatedAt: new Date(),
      isActive: true
    });

    // Obtener la solicitud creada con sus relaciones
    const createdApplication = await Application.findByPk(newApplication.id, {
      include: [
        { model: Form, attributes: ['id', 'title', 'description'] },
        {
          model: User,
          as: 'applicant',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        },
        {
          model: FunnelStage,
          as: 'stage',
          attributes: ['id', 'name', 'description', 'order', 'color']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Solicitud enviada correctamente',
      application: createdApplication
    });
  } catch (error) {
    console.error('Error al crear solicitud:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear solicitud',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener todas las solicitudes
exports.getAllApplications = async (req, res) => {
  try {
    const {
      acceleratorId,
      formId,
      status,
      funnelStageId,
      startDate,
      endDate
    } = req.query;

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Construir condiciones de búsqueda
    const where = {};

    if (acceleratorId) {
      where.acceleratorId = acceleratorId;
    }

    if (formId) {
      where.formId = formId;
    }

    if (status) {
      where.status = status;
    }

    // No usar funnelStageId ya que no existe en la tabla
    // if (funnelStageId) {
    //   where.funnelStageId = funnelStageId;
    // }

    // Filtrar por fecha de envío
    if (startDate || endDate) {
      where.submittedAt = {};

      if (startDate) {
        where.submittedAt.$gte = new Date(startDate);
      }

      if (endDate) {
        where.submittedAt.$lte = new Date(endDate);
      }
    }

    // Verificar permisos según el rol
    if (req.user.role === 'ENTREPRENEUR') {
      // Los emprendedores solo pueden ver sus propias solicitudes
      where.applicantId = req.user.id;
    } else if (req.user.role === 'ACCELERATOR_ADMIN') {
      // Los administradores de aceleradora solo pueden ver solicitudes de sus aceleradoras
      const userAccelerators = await req.user.getManagedAccelerators();
      const acceleratorIds = userAccelerators.map(acc => acc.id);

      if (acceleratorId) {
        // Si se especificó una aceleradora, verificar que el usuario tenga acceso
        if (!acceleratorIds.includes(parseInt(acceleratorId))) {
          return res.status(403).json({
            success: false,
            message: 'No tienes permisos para ver solicitudes de esta aceleradora'
          });
        }
      } else {
        // Si no se especificó, mostrar solo de las aceleradoras que administra
        where.acceleratorId = acceleratorIds;
      }
    }
    // Los GLOBAL_ADMIN pueden ver todas las solicitudes

    // Obtener solicitudes con paginación
    const { count, rows: applications } = await Application.findAndCountAll({
      where,
      include: [
        { model: Form, attributes: ['id', 'title', 'description'] },
        {
          model: User,
          as: 'applicant',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
        // No incluir FunnelStage ya que no existe la relación en la tabla
        // {
        //   model: FunnelStage,
        //   as: 'stage',
        //   attributes: ['id', 'name', 'description', 'order', 'color']
        // }
      ],
      order: [['submittedAt', 'DESC']],
      limit,
      offset
    });

    res.status(200).json({
      success: true,
      applications,
      totalApplications: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page
    });
  } catch (error) {
    console.error('Error al obtener solicitudes:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener solicitudes',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Obtener una solicitud por ID
exports.getApplicationById = async (req, res) => {
  try {
    const { id } = req.params;

    // Buscar la solicitud con sus relaciones
    const application = await Application.findByPk(id, {
      include: [
        {
          model: Form,
          include: [
            { model: Field, where: { isActive: true }, required: false }
          ]
        },
        {
          model: User,
          as: 'applicant',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profileData']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        }
        // No incluir FunnelStage ya que no existe la relación en la tabla
        // {
        //   model: FunnelStage,
        //   as: 'stage',
        //   attributes: ['id', 'name', 'description', 'order', 'color']
        // }
      ]
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Solicitud no encontrada'
      });
    }

    // Verificar permisos según el rol
    if (req.user.role === 'ENTREPRENEUR') {
      // Los emprendedores solo pueden ver sus propias solicitudes
      if (application.applicantId !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver esta solicitud'
        });
      }
    } else if (req.user.role === 'ACCELERATOR_ADMIN') {
      // Los administradores de aceleradora solo pueden ver solicitudes de sus aceleradoras
      const userAccelerators = await req.user.getManagedAccelerators();
      const acceleratorIds = userAccelerators.map(acc => acc.id);

      if (!acceleratorIds.includes(application.acceleratorId)) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para ver esta solicitud'
        });
      }
    }
    // Los GLOBAL_ADMIN pueden ver todas las solicitudes

    res.status(200).json({
      success: true,
      application
    });
  } catch (error) {
    console.error('Error al obtener solicitud:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener solicitud',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Actualizar el estado de una solicitud
exports.updateApplicationStatus = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { id } = req.params;
    const { status, funnelStageId, internalNotes, feedback, score } = req.body;

    // Buscar la solicitud
    const application = await Application.findByPk(id, {
      include: [
        { model: Form },
        { model: Accelerator }
      ]
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Solicitud no encontrada'
      });
    }

    // Verificar permisos (solo administradores pueden actualizar el estado)
    if (req.user.role === 'ENTREPRENEUR') {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para actualizar el estado de esta solicitud'
      });
    } else if (req.user.role === 'ACCELERATOR_ADMIN') {
      // Verificar que sea administrador de esta aceleradora
      const userAccelerators = await req.user.getManagedAccelerators();
      const acceleratorIds = userAccelerators.map(acc => acc.id);

      if (!acceleratorIds.includes(application.acceleratorId)) {
        return res.status(403).json({
          success: false,
          message: 'No tienes permisos para actualizar esta solicitud'
        });
      }
    }
    // Los GLOBAL_ADMIN pueden actualizar todas las solicitudes

    // Si se proporciona una etapa del embudo, verificar que exista y pertenezca a la aceleradora
    if (funnelStageId) {
      const stage = await FunnelStage.findByPk(funnelStageId);
      if (!stage) {
        return res.status(404).json({
          success: false,
          message: 'Etapa del embudo no encontrada'
        });
      }

      if (stage.acceleratorId !== application.acceleratorId) {
        return res.status(400).json({
          success: false,
          message: 'La etapa del embudo no pertenece a la aceleradora de esta solicitud'
        });
      }
    }

    // Actualizar la solicitud

    await application.update({
      status: status || application.status,
      funnelStageId: funnelStageId || application.funnelStageId,
      internalNotes: internalNotes !== undefined ? internalNotes : application.internalNotes,
      feedback: feedback !== undefined ? feedback : application.feedback,
      score: score !== undefined ? score : application.score,
      statusUpdatedAt: new Date()
    });

    // Obtener la solicitud actualizada con sus relaciones
    const updatedApplication = await Application.findByPk(id, {
      include: [
        { model: Form, attributes: ['id', 'title', 'description'] },
        {
          model: User,
          as: 'applicant',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description']
        },
        {
          model: FunnelStage,
          as: 'stage',
          attributes: ['id', 'name', 'description', 'order', 'color']
        }
      ]
    });

    res.status(200).json({
      success: true,
      message: 'Estado de solicitud actualizado correctamente',
      application: updatedApplication
    });
  } catch (error) {
    console.error('Error al actualizar estado de solicitud:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar estado de solicitud',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
