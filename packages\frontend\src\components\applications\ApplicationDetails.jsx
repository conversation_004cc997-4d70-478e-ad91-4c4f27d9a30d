import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiEdit, FiCheck, FiX, FiMessageSquare, FiStar } from 'react-icons/fi';
import Button from '../common/Button';
import Card from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';
import api from '../../services/api';
import useAuth from '../../hooks/useAuth';

/**
 * Componente para mostrar los detalles de una aplicación
 */
const ApplicationDetails = ({ showNotification }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [application, setApplication] = useState(null);
  const [loading, setLoading] = useState(true);
  const [stages, setStages] = useState([]);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({
    status: '',
    funnelStageId: '',
    internalNotes: '',
    feedback: '',
    score: ''
  });
  const { currentUser } = useAuth();

  // Cargar datos de la aplicación
  useEffect(() => {
    const fetchApplication = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/applications/${id}`);

        if (response.data.success) {
          setApplication(response.data.application);
          setFormData({
            status: response.data.application.status,
            funnelStageId: response.data.application.funnelStageId || '',
            internalNotes: response.data.application.internalNotes || '',
            feedback: response.data.application.feedback || '',
            score: response.data.application.score || ''
          });

          // Cargar etapas del embudo para esta aceleradora
          if (response.data.application.acceleratorId) {
            const stagesResponse = await api.get(`/funnel-stages/accelerator/${response.data.application.acceleratorId}`);
            if (stagesResponse.data.success) {
              setStages(stagesResponse.data.stages);
            }
          }
        } else {
          showNotification('error', 'Error al cargar la aplicación');
          navigate('/admin/applications');
        }
      } catch (error) {
        console.error('Error al cargar aplicación:', error);
        showNotification('error', 'Error al cargar la aplicación. Por favor, intente nuevamente.');
        navigate('/admin/applications');
      } finally {
        setLoading(false);
      }
    };

    fetchApplication();
  }, [id, navigate, showNotification]);

  // Manejar cambio en los campos del formulario
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Manejar envío del formulario
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const response = await api.put(`/applications/${id}/status`, formData);

      if (response.data.success) {
        setApplication(response.data.application);
        setEditMode(false);
        showNotification('success', 'Aplicación actualizada correctamente');
      } else {
        showNotification('error', 'Error al actualizar la aplicación');
      }
    } catch (error) {
      console.error('Error al actualizar aplicación:', error);
      showNotification('error', 'Error al actualizar la aplicación. Por favor, intente nuevamente.');
    }
  };

  // Formatear fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Obtener color de estado
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewing':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Obtener texto de estado
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'reviewing':
        return 'En revisión';
      case 'approved':
        return 'Aprobado';
      case 'rejected':
        return 'Rechazado';
      default:
        return 'Desconocido';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!application) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold text-gray-800">Aplicación no encontrada</h2>
        <Button
          variant="primary"
          className="mt-4"
          onClick={() => navigate('/admin/applications')}
        >
          Volver a la lista
        </Button>
      </div>
    );
  }

  return (
    <div>
      {/* Encabezado */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            variant="text"
            onClick={() => navigate('/admin/applications')}
            className="mr-4"
          >
            <FiArrowLeft className="mr-2" /> Volver
          </Button>
          <h1 className="text-2xl font-bold text-gray-800">
            Aplicación de {application.applicant.firstName} {application.applicant.lastName}
          </h1>
        </div>

        {(currentUser.role === 'GLOBAL_ADMIN' || currentUser.role === 'ACCELERATOR_ADMIN') && (
          <Button
            variant={editMode ? "secondary" : "primary"}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? (
              <>
                <FiX className="mr-2" /> Cancelar edición
              </>
            ) : (
              <>
                <FiEdit className="mr-2" /> Editar estado
              </>
            )}
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Columna izquierda: Información del aplicante */}
        <div className="lg:col-span-2">
          <Card className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Información del aplicante</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Nombre completo</p>
                <p className="text-gray-800">{application.applicant.firstName} {application.applicant.lastName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Correo electrónico</p>
                <p className="text-gray-800">{application.applicant.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Formulario</p>
                <p className="text-gray-800">{application.Form.title}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Aceleradora</p>
                <p className="text-gray-800">{application.accelerator.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Fecha de envío</p>
                <p className="text-gray-800">{formatDate(application.submittedAt)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Estado</p>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                  {getStatusText(application.status)}
                </span>
              </div>
              {application.stage && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Etapa actual</p>
                  <span
                    className="px-2 py-1 rounded text-xs font-medium"
                    style={{ backgroundColor: `${application.stage.color}20`, color: application.stage.color }}
                  >
                    {application.stage.name}
                  </span>
                </div>
              )}
              {application.score !== null && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Puntuación</p>
                  <div className="flex items-center">
                    <FiStar className="text-yellow-500 mr-1" />
                    <span>{application.score}/10</span>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Respuestas del formulario */}
          <Card>
            <h2 className="text-xl font-semibold mb-4">Respuestas del formulario</h2>
            <div className="space-y-4">
              {application.Form.Fields && application.Form.Fields.map(field => (
                <div key={field.id} className="border-b border-gray-200 pb-3 last:border-0 last:pb-0">
                  <p className="text-sm font-medium text-gray-500">{field.label}</p>
                  <p className="text-gray-800">
                    {application.responses[field.name] || 'No respondido'}
                  </p>
                </div>
              ))}

              {(!application.Form.Fields || application.Form.Fields.length === 0) && (
                <p className="text-gray-500 italic">No hay campos definidos en este formulario.</p>
              )}
            </div>
          </Card>
        </div>

        {/* Columna derecha: Estado y evaluación */}
        <div>
          {editMode ? (
            <Card>
              <h2 className="text-xl font-semibold mb-4">Actualizar estado</h2>
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Estado
                    </label>
                    <select
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    >
                      <option value="pending">Pendiente</option>
                      <option value="reviewing">En revisión</option>
                      <option value="approved">Aprobado</option>
                      <option value="rejected">Rechazado</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Etapa del embudo
                    </label>
                    <select
                      name="funnelStageId"
                      value={formData.funnelStageId}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    >
                      <option value="">Seleccionar etapa</option>
                      {stages.map(stage => (
                        <option key={stage.id} value={stage.id}>
                          {stage.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Puntuación (0-10)
                    </label>
                    <input
                      type="number"
                      name="score"
                      min="0"
                      max="10"
                      step="0.1"
                      value={formData.score}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notas internas
                    </label>
                    <textarea
                      name="internalNotes"
                      value={formData.internalNotes}
                      onChange={handleChange}
                      rows="3"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                      placeholder="Notas internas (solo visibles para administradores)"
                    ></textarea>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Feedback para el aplicante
                    </label>
                    <textarea
                      name="feedback"
                      value={formData.feedback}
                      onChange={handleChange}
                      rows="3"
                      className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                      placeholder="Feedback (visible para el aplicante)"
                    ></textarea>
                  </div>

                  <div className="flex justify-end pt-4">
                    <Button
                      type="button"
                      variant="secondary"
                      className="mr-2"
                      onClick={() => setEditMode(false)}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      variant="primary"
                    >
                      <FiCheck className="mr-2" /> Guardar cambios
                    </Button>
                  </div>
                </div>
              </form>
            </Card>
          ) : (
            <>
              <Card className="mb-6">
                <h2 className="text-xl font-semibold mb-4">Evaluación</h2>

                {application.internalNotes ? (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-gray-500 mb-1">Notas internas</p>
                    <div className="bg-gray-50 p-3 rounded border border-gray-200">
                      <p className="text-gray-800 whitespace-pre-line">{application.internalNotes}</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 italic mb-4">No hay notas internas.</p>
                )}

                {application.feedback ? (
                  <div>
                    <p className="text-sm font-medium text-gray-500 mb-1">Feedback para el aplicante</p>
                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                      <div className="flex items-start">
                        <FiMessageSquare className="text-blue-500 mt-1 mr-2 flex-shrink-0" />
                        <p className="text-gray-800 whitespace-pre-line">{application.feedback}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No se ha proporcionado feedback.</p>
                )}
              </Card>

              {application.applicant.profileData && (
                <Card>
                  <h2 className="text-xl font-semibold mb-4">Perfil del aplicante</h2>
                  <div className="space-y-3">
                    {Object.entries(application.applicant.profileData).map(([key, value]) => (
                      <div key={key}>
                        <p className="text-sm font-medium text-gray-500 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</p>
                        <p className="text-gray-800">{value}</p>
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationDetails;
