import React, { useState, useEffect } from 'react';
import { FiSettings } from 'react-icons/fi';
import AdminLayout from '../../../components/admin/AdminLayout';
import Card from '../../../components/common/Card';
import LoadingSpinner from '../../../components/common/LoadingSpinner';
import FunnelStageList from '../../../components/funnelStages/FunnelStageList';
import api from '../../../services/api';
import useAuth from '../../../hooks/useAuth';

/**
 * Página para gestionar las etapas del embudo de las aceleradoras
 */
const FunnelStagesPage = () => {
  const [accelerators, setAccelerators] = useState([]);
  const [selectedAccelerator, setSelectedAccelerator] = useState('');
  const [loading, setLoading] = useState(true);
  const { currentUser } = useAuth();
  const [notification, setNotification] = useState(null);

  // Mostrar notificación y limpiarla después de 5 segundos
  const showNotification = (type, message) => {
    setNotification({ type, message });
    setTimeout(() => {
      setNotification(null);
    }, 5000);
  };

  // Cargar aceleradoras disponibles
  useEffect(() => {
    const fetchAccelerators = async () => {
      try {
        setLoading(true);
        const response = await api.get('/accelerators');

        if (response.data.success) {
          const availableAccelerators = response.data.accelerators;
          setAccelerators(availableAccelerators);

          // Si el usuario es administrador de aceleradora, seleccionar su aceleradora por defecto
          if (currentUser.role === 'ACCELERATOR_ADMIN' && availableAccelerators.length > 0) {
            const userAccelerator = availableAccelerators.find(
              acc => acc.administrators.some(admin => admin.id === currentUser.id)
            );

            if (userAccelerator) {
              setSelectedAccelerator(userAccelerator.id);
            } else if (availableAccelerators.length > 0) {
              setSelectedAccelerator(availableAccelerators[0].id);
            }
          } else if (availableAccelerators.length > 0) {
            setSelectedAccelerator(availableAccelerators[0].id);
          }
        } else {
          showNotification('error', 'Error al cargar aceleradoras');
        }
      } catch (error) {
        console.error('Error al cargar aceleradoras:', error);
        showNotification('error', 'Error al cargar aceleradoras. Por favor, intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchAccelerators();
  }, [currentUser, showNotification]);

  // Manejar cambio de aceleradora seleccionada
  const handleAcceleratorChange = (e) => {
    setSelectedAccelerator(e.target.value);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Etapas del Embudo</h1>
          <FiSettings className="text-gray-600 text-xl" />
        </div>

        {/* Notificación */}
        {notification && (
          <div className={`mb-4 p-4 rounded-md ${
            notification.type === 'error'
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}>
            {notification.message}
            <button
              onClick={() => setNotification(null)}
              className="float-right text-gray-500 hover:text-gray-700"
            >
              &times;
            </button>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <>
            {/* Selector de aceleradora */}
            <Card className="mb-6">
              <div className="flex items-center">
                <label htmlFor="accelerator" className="block text-sm font-medium text-gray-700 mr-4">
                  Seleccionar Aceleradora:
                </label>
                <select
                  id="accelerator"
                  name="accelerator"
                  value={selectedAccelerator}
                  onChange={handleAcceleratorChange}
                  className="flex-1 max-w-md border border-gray-300 rounded-md shadow-sm p-2"
                  disabled={currentUser.role === 'ACCELERATOR_ADMIN' && accelerators.length === 1}
                >
                  <option value="">Seleccionar aceleradora</option>
                  {accelerators.map(accelerator => (
                    <option key={accelerator.id} value={accelerator.id}>
                      {accelerator.name}
                    </option>
                  ))}
                </select>
              </div>
            </Card>

            {/* Lista de etapas del embudo */}
            {selectedAccelerator ? (
              <FunnelStageList
                acceleratorId={selectedAccelerator}
                showNotification={showNotification}
              />
            ) : (
              <Card>
                <div className="text-center py-8">
                  <p className="text-gray-500">
                    Seleccione una aceleradora para gestionar sus etapas del embudo.
                  </p>
                </div>
              </Card>
            )}
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default FunnelStagesPage;
