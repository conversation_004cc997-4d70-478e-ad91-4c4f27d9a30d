const express = require('express');
const router = express.Router();
const formController = require('../controllers/form.controller');
const fieldController = require('../controllers/field.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateForm, validateUpdateForm, validate<PERSON>reateField, validateUpdateField, validateReorder<PERSON>ields } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para formularios
router.post('/', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateCreateForm, 
  formController.createForm
);

router.get('/', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), 
  formController.getAllForms
);

router.get('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), 
  formController.getFormById
);

router.put('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateUpdateForm, 
  formController.updateForm
);

router.delete('/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  formController.deleteForm
);

// Rutas para campos de formulario
router.post('/fields', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateCreateField, 
  fieldController.createField
);

router.get('/:formId/fields', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), 
  fieldController.getFieldsByFormId
);

router.get('/fields/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), 
  fieldController.getFieldById
);

router.put('/fields/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateUpdateField, 
  fieldController.updateField
);

router.delete('/fields/:id', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  fieldController.deleteField
);

router.put('/:formId/fields/reorder', 
  checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN']), 
  validateReorderFields, 
  fieldController.reorderFields
);

module.exports = router;
