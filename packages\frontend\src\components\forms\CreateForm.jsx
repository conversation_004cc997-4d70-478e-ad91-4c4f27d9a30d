import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { FiSave, FiPlus, FiArrowLeft } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import FormInput from '../common/FormInput';
import Button from '../common/Button';
import Card from '../common/Card';
import FieldEditor from './FieldEditor';
import api from '../../services/api';

/**
 * Componente para crear un nuevo formulario
 */
const CreateForm = ({ accelerators, showNotification, navigate }) => {
  const [fields, setFields] = useState([]);
  const [showFieldEditor, setShowFieldEditor] = useState(false);
  const [currentField, setCurrentField] = useState(null);
  const [editingFieldIndex, setEditingFieldIndex] = useState(-1);
  const [submitting, setSubmitting] = useState(false);

  // Esquema de validación para el formulario
  const validationSchema = Yup.object({
    title: Yup.string()
      .required('El título es requerido')
      .min(2, 'El título debe tener al menos 2 caracteres')
      .max(100, 'El título no debe exceder los 100 caracteres'),
    description: Yup.string()
      .max(1000, 'La descripción no debe exceder los 1000 caracteres'),
    acceleratorId: Yup.number()
      .required('La aceleradora es requerida'),
    formType: Yup.string()
      .required('El tipo de formulario es requerido')
      .oneOf(['application', 'evaluation', 'feedback'], 'Tipo de formulario no válido'),
    startDate: Yup.date()
      .nullable(),
    endDate: Yup.date()
      .nullable()
      .min(Yup.ref('startDate'), 'La fecha de fin debe ser posterior a la fecha de inicio'),
  });

  // Configuración de Formik
  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      acceleratorId: '',
      formType: 'application',
      config: {
        showProgressBar: true,
        allowSave: true,
        submitButtonText: 'Enviar'
      },
      startDate: '',
      endDate: '',
      isPublished: false
    },
    validationSchema,
    onSubmit: async (values) => {
      // Validar que haya al menos un campo
      if (fields.length === 0) {
        showNotification('error', 'Debe agregar al menos un campo al formulario');
        return;
      }

      try {
        setSubmitting(true);
        
        // Preparar datos para enviar
        const formData = {
          ...values,
          startDate: values.startDate || null,
          endDate: values.endDate || null,
          fields
        };
        
        const response = await api.post('/forms', formData);
        
        if (response.data.success) {
          showNotification('success', 'Formulario creado correctamente');
          navigate(`/admin/forms/${response.data.form.id}`);
        } else {
          showNotification('error', 'Error al crear formulario');
        }
      } catch (error) {
        console.error('Error al crear formulario:', error);
        showNotification('error', 'Error al crear formulario. Por favor, intente nuevamente.');
      } finally {
        setSubmitting(false);
      }
    }
  });

  // Abrir editor de campo para añadir un nuevo campo
  const handleAddField = () => {
    setCurrentField({
      name: '',
      label: '',
      type: 'text',
      placeholder: '',
      helpText: '',
      required: false,
      order: fields.length,
      options: [],
      validations: {},
      config: {}
    });
    setEditingFieldIndex(-1);
    setShowFieldEditor(true);
  };

  // Abrir editor de campo para editar un campo existente
  const handleEditField = (index) => {
    setCurrentField({ ...fields[index] });
    setEditingFieldIndex(index);
    setShowFieldEditor(true);
  };

  // Eliminar un campo
  const handleDeleteField = (index) => {
    const newFields = [...fields];
    newFields.splice(index, 1);
    
    // Actualizar el orden de los campos restantes
    const updatedFields = newFields.map((field, idx) => ({
      ...field,
      order: idx
    }));
    
    setFields(updatedFields);
  };

  // Guardar un campo (nuevo o editado)
  const handleSaveField = (field) => {
    if (editingFieldIndex >= 0) {
      // Editar campo existente
      const newFields = [...fields];
      newFields[editingFieldIndex] = field;
      setFields(newFields);
    } else {
      // Añadir nuevo campo
      setFields([...fields, field]);
    }
    
    setShowFieldEditor(false);
    setCurrentField(null);
    setEditingFieldIndex(-1);
  };

  // Cancelar edición de campo
  const handleCancelFieldEdit = () => {
    setShowFieldEditor(false);
    setCurrentField(null);
    setEditingFieldIndex(-1);
  };

  // Mover un campo hacia arriba en el orden
  const handleMoveFieldUp = (index) => {
    if (index === 0) return;
    
    const newFields = [...fields];
    const temp = newFields[index];
    newFields[index] = newFields[index - 1];
    newFields[index - 1] = temp;
    
    // Actualizar el orden
    const updatedFields = newFields.map((field, idx) => ({
      ...field,
      order: idx
    }));
    
    setFields(updatedFields);
  };

  // Mover un campo hacia abajo en el orden
  const handleMoveFieldDown = (index) => {
    if (index === fields.length - 1) return;
    
    const newFields = [...fields];
    const temp = newFields[index];
    newFields[index] = newFields[index + 1];
    newFields[index + 1] = temp;
    
    // Actualizar el orden
    const updatedFields = newFields.map((field, idx) => ({
      ...field,
      order: idx
    }));
    
    setFields(updatedFields);
  };

  return (
    <div>
      <div className="mb-6">
        <Link to="/admin/forms" className="text-blue-600 hover:text-blue-800 flex items-center">
          <FiArrowLeft className="mr-1" /> Volver a la lista de formularios
        </Link>
      </div>

      <h2 className="text-2xl font-bold mb-6">Crear Nuevo Formulario</h2>

      {showFieldEditor ? (
        <FieldEditor 
          field={currentField} 
          onSave={handleSaveField} 
          onCancel={handleCancelFieldEdit} 
          isNew={editingFieldIndex === -1}
        />
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Formulario principal */}
          <div className="lg:col-span-2">
            <Card>
              <h3 className="text-xl font-semibold mb-4">Información del Formulario</h3>
              
              <form onSubmit={formik.handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <FormInput
                    label="Título"
                    id="title"
                    name="title"
                    type="text"
                    placeholder="Título del formulario"
                    value={formik.values.title}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.title && formik.errors.title}
                    touched={formik.touched.title}
                    required
                  />
                  
                  <div className="md:col-span-2">
                    <FormInput
                      label="Descripción"
                      id="description"
                      name="description"
                      type="textarea"
                      placeholder="Descripción del formulario"
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.description && formik.errors.description}
                      touched={formik.touched.description}
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="acceleratorId" className="block text-sm font-medium text-gray-700 mb-1">
                      Aceleradora <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="acceleratorId"
                      name="acceleratorId"
                      value={formik.values.acceleratorId}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`appearance-none block w-full px-3 py-2 border ${
                        formik.touched.acceleratorId && formik.errors.acceleratorId 
                          ? 'border-red-300' 
                          : 'border-gray-300'
                      } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    >
                      <option value="">Seleccione una aceleradora</option>
                      {accelerators.map(accelerator => (
                        <option key={accelerator.id} value={accelerator.id}>
                          {accelerator.name}
                        </option>
                      ))}
                    </select>
                    {formik.touched.acceleratorId && formik.errors.acceleratorId && (
                      <p className="mt-2 text-sm text-red-600">{formik.errors.acceleratorId}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="formType" className="block text-sm font-medium text-gray-700 mb-1">
                      Tipo de Formulario <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="formType"
                      name="formType"
                      value={formik.values.formType}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      className={`appearance-none block w-full px-3 py-2 border ${
                        formik.touched.formType && formik.errors.formType 
                          ? 'border-red-300' 
                          : 'border-gray-300'
                      } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    >
                      <option value="application">Postulación</option>
                      <option value="evaluation">Evaluación</option>
                      <option value="feedback">Feedback</option>
                    </select>
                    {formik.touched.formType && formik.errors.formType && (
                      <p className="mt-2 text-sm text-red-600">{formik.errors.formType}</p>
                    )}
                  </div>
                  
                  <FormInput
                    label="Fecha de Inicio"
                    id="startDate"
                    name="startDate"
                    type="datetime-local"
                    value={formik.values.startDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.startDate && formik.errors.startDate}
                    touched={formik.touched.startDate}
                  />
                  
                  <FormInput
                    label="Fecha de Fin"
                    id="endDate"
                    name="endDate"
                    type="datetime-local"
                    value={formik.values.endDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    error={formik.touched.endDate && formik.errors.endDate}
                    touched={formik.touched.endDate}
                  />
                  
                  <div className="md:col-span-2">
                    <div className="flex items-center">
                      <input
                        id="isPublished"
                        name="isPublished"
                        type="checkbox"
                        checked={formik.values.isPublished}
                        onChange={formik.handleChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isPublished" className="ml-2 block text-sm text-gray-900">
                        Publicar formulario inmediatamente
                      </label>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-200 pt-4 mt-4">
                  <h3 className="text-lg font-semibold mb-4">Campos del Formulario</h3>
                  
                  {fields.length === 0 ? (
                    <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                      <p className="text-gray-500 mb-4">No hay campos en este formulario</p>
                      <Button
                        type="button"
                        variant="primary"
                        onClick={handleAddField}
                        className="flex items-center mx-auto"
                      >
                        <FiPlus className="mr-2" /> Añadir Campo
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4 mb-4">
                      {fields.map((field, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">{field.label}</h4>
                              <p className="text-sm text-gray-500">
                                Tipo: {field.type} | Nombre: {field.name} | 
                                {field.required ? ' Requerido' : ' Opcional'}
                              </p>
                              {field.helpText && (
                                <p className="text-sm text-gray-600 mt-1">{field.helpText}</p>
                              )}
                            </div>
                            <div className="flex space-x-2">
                              <button
                                type="button"
                                onClick={() => handleMoveFieldUp(index)}
                                disabled={index === 0}
                                className={`p-1 rounded ${
                                  index === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-200'
                                }`}
                              >
                                ↑
                              </button>
                              <button
                                type="button"
                                onClick={() => handleMoveFieldDown(index)}
                                disabled={index === fields.length - 1}
                                className={`p-1 rounded ${
                                  index === fields.length - 1 ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-200'
                                }`}
                              >
                                ↓
                              </button>
                              <button
                                type="button"
                                onClick={() => handleEditField(index)}
                                className="p-1 rounded text-blue-600 hover:bg-blue-100"
                              >
                                Editar
                              </button>
                              <button
                                type="button"
                                onClick={() => handleDeleteField(index)}
                                className="p-1 rounded text-red-600 hover:bg-red-100"
                              >
                                Eliminar
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleAddField}
                        className="flex items-center"
                      >
                        <FiPlus className="mr-2" /> Añadir Campo
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <Link to="/admin/forms">
                    <Button type="button" variant="outline">
                      Cancelar
                    </Button>
                  </Link>
                  <Button
                    type="submit"
                    variant="primary"
                    className="flex items-center"
                    disabled={submitting}
                  >
                    <FiSave className="mr-2" /> 
                    {submitting ? 'Guardando...' : 'Guardar Formulario'}
                  </Button>
                </div>
              </form>
            </Card>
          </div>
          
          {/* Panel lateral con ayuda */}
          <div className="lg:col-span-1">
            <Card>
              <h3 className="text-xl font-semibold mb-4">Ayuda</h3>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  <strong>Título y Descripción:</strong> Proporcione un título claro y una descripción que explique el propósito del formulario.
                </p>
                <p>
                  <strong>Aceleradora:</strong> Seleccione la aceleradora a la que pertenecerá este formulario.
                </p>
                <p>
                  <strong>Tipo de Formulario:</strong>
                  <ul className="list-disc ml-5 mt-1">
                    <li><strong>Postulación:</strong> Para recibir solicitudes de startups.</li>
                    <li><strong>Evaluación:</strong> Para evaluar startups o proyectos.</li>
                    <li><strong>Feedback:</strong> Para recopilar comentarios o retroalimentación.</li>
                  </ul>
                </p>
                <p>
                  <strong>Fechas:</strong> Opcionalmente, puede establecer un período durante el cual el formulario estará disponible.
                </p>
                <p>
                  <strong>Campos:</strong> Añada los campos necesarios para recopilar la información requerida. Cada campo debe tener un nombre único y un tipo apropiado.
                </p>
                <p>
                  <strong>Publicación:</strong> Marque la casilla "Publicar" si desea que el formulario esté disponible inmediatamente después de guardarlo.
                </p>
              </div>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreateForm;
