'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Forms', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      acceleratorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Accelerators',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'NO ACTION',
      },
      formType: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'application',
      },
      config: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      isPublished: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Añadir índices para mejorar el rendimiento
    await queryInterface.addIndex('Forms', ['acceleratorId']);
    await queryInterface.addIndex('Forms', ['createdBy']);
    await queryInterface.addIndex('Forms', ['formType']);
    await queryInterface.addIndex('Forms', ['isPublished', 'isActive']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Forms');
  }
};
