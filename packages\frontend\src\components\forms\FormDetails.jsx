import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { FiEdit, FiArrowLeft, FiEye, FiCopy, FiCheck, FiX } from 'react-icons/fi';
import Button from '../common/Button';
import Card from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';
import api from '../../services/api';

/**
 * Componente para ver los detalles de un formulario
 */
const FormDetails = ({ showNotification }) => {
  const { id } = useParams();
  const [form, setForm] = useState(null);
  const [loading, setLoading] = useState(true);
  const [previewUrl, setPreviewUrl] = useState('');

  // Cargar datos del formulario
  useEffect(() => {
    const fetchForm = async () => {
      try {
        setLoading(true);
        const response = await api.get(`/forms/${id}`);
        
        if (response.data.success) {
          setForm(response.data.form);
          
          // Generar URL de vista previa (esto dependerá de la implementación real)
          setPreviewUrl(`/forms/preview/${response.data.form.id}`);
        } else {
          showNotification('error', 'Error al cargar el formulario');
        }
      } catch (error) {
        console.error('Error al cargar formulario:', error);
        showNotification('error', 'Error al cargar formulario. Por favor, intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchForm();
  }, [id, showNotification]);

  // Manejar publicación/despublicación de formulario
  const handleTogglePublish = async () => {
    try {
      const response = await api.put(`/forms/${id}`, {
        isPublished: !form.isPublished
      });
      
      if (response.data.success) {
        showNotification('success', `Formulario ${!form.isPublished ? 'publicado' : 'despublicado'} correctamente`);
        setForm({ ...form, isPublished: !form.isPublished });
      } else {
        showNotification('error', `Error al ${!form.isPublished ? 'publicar' : 'despublicar'} formulario`);
      }
    } catch (error) {
      console.error(`Error al ${!form.isPublished ? 'publicar' : 'despublicar'} formulario:`, error);
      showNotification('error', `Error al ${!form.isPublished ? 'publicar' : 'despublicar'} formulario. Por favor, intente nuevamente.`);
    }
  };

  // Copiar enlace al portapapeles
  const copyLinkToClipboard = () => {
    const baseUrl = window.location.origin;
    const formUrl = `${baseUrl}/forms/${id}`;
    
    navigator.clipboard.writeText(formUrl)
      .then(() => {
        showNotification('success', 'Enlace copiado al portapapeles');
      })
      .catch(() => {
        showNotification('error', 'Error al copiar enlace');
      });
  };

  // Renderizar estado de carga
  if (loading) {
    return <LoadingSpinner />;
  }

  // Renderizar mensaje de error si no se encuentra el formulario
  if (!form) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Formulario no encontrado</h2>
        <p className="text-gray-600 mb-6">El formulario que estás buscando no existe o no tienes permisos para verlo.</p>
        <Link to="/admin/forms">
          <Button variant="primary">Volver a la lista de formularios</Button>
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <Link to="/admin/forms" className="text-blue-600 hover:text-blue-800 flex items-center">
          <FiArrowLeft className="mr-1" /> Volver a la lista de formularios
        </Link>
      </div>

      {/* Encabezado */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h2 className="text-2xl font-bold">{form.title}</h2>
          <p className="text-gray-600">{form.description || 'Sin descripción'}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="flex items-center"
            onClick={copyLinkToClipboard}
          >
            <FiCopy className="mr-2" /> Copiar Enlace
          </Button>
          
          <Button
            variant={form.isPublished ? 'success' : 'outline'}
            className="flex items-center"
            onClick={handleTogglePublish}
          >
            {form.isPublished ? (
              <>
                <FiCheck className="mr-2" /> Publicado
              </>
            ) : (
              <>
                <FiX className="mr-2" /> No Publicado
              </>
            )}
          </Button>
          
          <Link to={`/admin/forms/${id}/edit`}>
            <Button variant="primary" className="flex items-center">
              <FiEdit className="mr-2" /> Editar
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Información del formulario */}
        <div className="lg:col-span-2">
          <Card className="mb-6">
            <h3 className="text-xl font-semibold mb-4">Información General</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Aceleradora</h4>
                <p className="text-gray-800">{form.accelerator?.name || 'No especificada'}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Tipo de Formulario</h4>
                <p className="text-gray-800">
                  {form.formType === 'application' ? 'Postulación' :
                   form.formType === 'evaluation' ? 'Evaluación' : 'Feedback'}
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Creado por</h4>
                <p className="text-gray-800">
                  {form.creator?.firstName} {form.creator?.lastName} ({form.creator?.email})
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Fecha de Creación</h4>
                <p className="text-gray-800">
                  {new Date(form.createdAt).toLocaleDateString()}
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Disponible desde</h4>
                <p className="text-gray-800">
                  {form.startDate ? new Date(form.startDate).toLocaleString() : 'No especificado'}
                </p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium text-gray-500">Disponible hasta</h4>
                <p className="text-gray-800">
                  {form.endDate ? new Date(form.endDate).toLocaleString() : 'No especificado'}
                </p>
              </div>
            </div>
          </Card>

          {/* Campos del formulario */}
          <Card>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Campos del Formulario</h3>
              <Link to={previewUrl} target="_blank">
                <Button variant="outline" size="sm" className="flex items-center">
                  <FiEye className="mr-1" /> Vista Previa
                </Button>
              </Link>
            </div>
            
            {form.Fields && form.Fields.length > 0 ? (
              <div className="space-y-4">
                {form.Fields.sort((a, b) => a.order - b.order).map((field) => (
                  <div key={field.id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{field.label}</h4>
                        <p className="text-sm text-gray-500">
                          Tipo: {field.type} | Nombre: {field.name} | 
                          {field.required ? ' Requerido' : ' Opcional'}
                        </p>
                        {field.helpText && (
                          <p className="text-sm text-gray-600 mt-1">{field.helpText}</p>
                        )}
                        
                        {['select', 'radio', 'checkbox'].includes(field.type) && field.options && (
                          <div className="mt-2">
                            <p className="text-sm font-medium text-gray-500">Opciones:</p>
                            <ul className="text-sm text-gray-600 list-disc list-inside">
                              {field.options.map((option, index) => (
                                <li key={index}>{option.label} ({option.value})</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">Este formulario no tiene campos definidos.</p>
            )}
          </Card>
        </div>
        
        {/* Panel lateral */}
        <div className="lg:col-span-1">
          <Card className="mb-6">
            <h3 className="text-xl font-semibold mb-4">Estado</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Estado de Publicación:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  form.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {form.isPublished ? 'Publicado' : 'No Publicado'}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Estado:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  form.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {form.isActive ? 'Activo' : 'Inactivo'}
                </span>
              </div>
            </div>
          </Card>
          
          <Card>
            <h3 className="text-xl font-semibold mb-4">Acciones</h3>
            <div className="space-y-3">
              <Link to={`/admin/forms/${id}/edit`} className="block">
                <Button variant="outline" className="w-full flex items-center justify-center">
                  <FiEdit className="mr-2" /> Editar Formulario
                </Button>
              </Link>
              
              <Button
                variant={form.isPublished ? 'outline' : 'primary'}
                className="w-full flex items-center justify-center"
                onClick={handleTogglePublish}
              >
                {form.isPublished ? (
                  <>
                    <FiX className="mr-2" /> Despublicar
                  </>
                ) : (
                  <>
                    <FiCheck className="mr-2" /> Publicar
                  </>
                )}
              </Button>
              
              <Link to={previewUrl} target="_blank" className="block">
                <Button variant="outline" className="w-full flex items-center justify-center">
                  <FiEye className="mr-2" /> Vista Previa
                </Button>
              </Link>
              
              <Button
                variant="outline"
                className="w-full flex items-center justify-center"
                onClick={copyLinkToClipboard}
              >
                <FiCopy className="mr-2" /> Copiar Enlace
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default FormDetails;
