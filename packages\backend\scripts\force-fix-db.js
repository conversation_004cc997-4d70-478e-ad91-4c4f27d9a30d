#!/usr/bin/env node

/**
 * Script de emergencia para forzar la reparación de la base de datos
 * Este script utiliza un enfoque más agresivo para eliminar la base de datos
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Ruta al archivo de base de datos
const dbPath = path.join(__dirname, '../database.sqlite');
const backupPath = path.join(__dirname, `../database.sqlite.backup.${Date.now()}`);
const tempDbPath = path.join(__dirname, '../database.sqlite.new');

console.log('Script de emergencia para FORZAR la reparación de la base de datos');
console.log('ADVERTENCIA: Este script eliminará todos los datos de la base de datos');

// Verificar si existe el archivo
if (fs.existsSync(dbPath)) {
  try {
    // Crear copia de seguridad
    fs.copyFileSync(dbPath, backupPath);
    console.log(`Copia de seguridad creada en: ${backupPath}`);
  } catch (error) {
    console.warn(`No se pudo crear copia de seguridad: ${error.message}`);
  }
}

// Crear una nueva base de datos en una ubicación temporal
console.log('Creando nueva base de datos en ubicación temporal...');

// Iniciar el servidor para crear la base de datos temporal
try {
  // Modificar temporalmente la variable de entorno para la base de datos
  process.env.DB_PATH = tempDbPath;

  // Ejecutar el servidor en modo de inicialización y luego detenerlo
  console.log('Iniciando servidor para crear la base de datos temporal...');
  execSync('node src/index.js --init-only', {
    stdio: 'inherit',
    timeout: 10000, // 10 segundos máximo
    env: { ...process.env, DB_PATH: tempDbPath },
    cwd: path.join(__dirname, '..')  // Establecer el directorio de trabajo correcto
  });
  console.log('Base de datos temporal creada correctamente');

  // Ahora reemplazar la base de datos original con la temporal
  console.log('Reemplazando base de datos original...');

  try {
    // Intentar eliminar la base de datos original
    if (fs.existsSync(dbPath)) {
      fs.unlinkSync(dbPath);
    }
  } catch (error) {
    console.warn(`No se pudo eliminar la base de datos original: ${error.message}`);
    console.warn('Intentando reemplazar directamente...');
  }

  // Mover la base de datos temporal a la ubicación original
  fs.copyFileSync(tempDbPath, dbPath);
  console.log('Base de datos reemplazada correctamente');

  // Limpiar archivos temporales
  try {
    if (fs.existsSync(tempDbPath)) {
      fs.unlinkSync(tempDbPath);
    }
  } catch (error) {
    console.warn(`No se pudo eliminar la base de datos temporal: ${error.message}`);
  }

  console.log('¡Listo! La base de datos ha sido reparada.');
  console.log('Ahora puede iniciar el servidor normalmente.');
} catch (error) {
  console.error('Error al crear la base de datos:', error.message);
  process.exit(1);
}
