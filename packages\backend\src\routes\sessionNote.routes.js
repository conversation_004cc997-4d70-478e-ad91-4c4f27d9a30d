const express = require('express');
const router = express.Router();
const sessionNoteController = require('../controllers/sessionNote.controller');
const { verifyToken, checkRole } = require('../middlewares/auth.middleware');
const { validateCreateNote, validateUpdateNote } = require('../middlewares/validation.middleware');

// Todas las rutas requieren autenticación
router.use(verifyToken);

// Rutas para notas de sesión
router.get('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionNoteController.getAllNotes);
router.get('/session/:sessionId', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionNoteController.getSessionNotes);
router.get('/user/:userId?', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionNoteController.getUserNotes);
router.get('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionNoteController.getNoteById);

// Rutas para crear, actualizar y eliminar notas
router.post('/', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), validateCreateNote, sessionNoteController.createNote);
router.put('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), validateUpdateNote, sessionNoteController.updateNote);
router.delete('/:id', checkRole(['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR', 'ENTREPRENEUR']), sessionNoteController.deleteNote);

module.exports = router;
