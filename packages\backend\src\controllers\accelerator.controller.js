const { Accelerator, User } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Obtener todas las aceleradoras (con paginación, filtros y búsqueda)
exports.getAllAccelerators = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'DESC';

    // Construir condiciones de búsqueda
    const whereConditions = {};

    // Búsqueda por nombre o descripción
    if (search) {
      whereConditions[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ];
    }

    // Filtro por estado (activo/inactivo)
    if (status !== undefined && status !== '') {
      whereConditions.isActive = status === 'active';
    }

    // Validar campo de ordenamiento
    const validSortFields = ['name', 'createdAt', 'location'];
    const orderField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Validar dirección de ordenamiento
    const orderDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await Accelerator.findAndCountAll({
      where: whereConditions,
      limit,
      offset,
      order: [[orderField, orderDirection]],
      include: [
        {
          model: User,
          as: 'administrators',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          through: { attributes: [] }, // No incluir atributos de la tabla intermedia
        },
      ],
    });

    res.json({
      success: true,
      accelerators: rows,
      totalAccelerators: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener aceleradoras:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener aceleradoras',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener una aceleradora por ID
exports.getAcceleratorById = async (req, res) => {
  try {
    const acceleratorId = req.params.id;

    const accelerator = await Accelerator.findByPk(acceleratorId, {
      include: [
        {
          model: User,
          as: 'administrators',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          through: { attributes: [] },
        },
      ],
    });

    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada',
      });
    }

    res.json({
      success: true,
      accelerator,
    });
  } catch (error) {
    console.error('Error al obtener aceleradora:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener aceleradora',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Crear una nueva aceleradora
exports.createAccelerator = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const {
      name,
      description,
      logo,
      website,
      location,
      contactEmail,
      contactPhone,
      isActive,
      additionalData,
      administratorIds
    } = req.body;

    // Crear nueva aceleradora
    const newAccelerator = await Accelerator.create({
      name,
      description,
      logo,
      website,
      location,
      contactEmail,
      contactPhone,
      isActive: isActive !== undefined ? isActive : true,
      additionalData: additionalData || {},
    });

    // Asignar administradores si se proporcionaron
    if (administratorIds && Array.isArray(administratorIds) && administratorIds.length > 0) {
      const administrators = await User.findAll({
        where: {
          id: { [Op.in]: administratorIds },
          roleId: { [Op.in]: [1, 2] }, // Solo GLOBAL_ADMIN y ACCELERATOR_ADMIN pueden ser administradores
        },
      });

      if (administrators.length > 0) {
        await newAccelerator.setAdministrators(administrators);
      }
    }

    // Obtener aceleradora creada con sus administradores
    const createdAccelerator = await Accelerator.findByPk(newAccelerator.id, {
      include: [
        {
          model: User,
          as: 'administrators',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          through: { attributes: [] },
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: 'Aceleradora creada correctamente',
      accelerator: createdAccelerator,
    });
  } catch (error) {
    console.error('Error al crear aceleradora:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear aceleradora',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Actualizar una aceleradora
exports.updateAccelerator = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const acceleratorId = req.params.id;
    const {
      name,
      description,
      logo,
      website,
      location,
      contactEmail,
      contactPhone,
      isActive,
      additionalData,
      administratorIds
    } = req.body;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada',
      });
    }

    // Actualizar aceleradora
    await accelerator.update({
      name: name || accelerator.name,
      description: description !== undefined ? description : accelerator.description,
      logo: logo !== undefined ? logo : accelerator.logo,
      website: website !== undefined ? website : accelerator.website,
      location: location !== undefined ? location : accelerator.location,
      contactEmail: contactEmail !== undefined ? contactEmail : accelerator.contactEmail,
      contactPhone: contactPhone !== undefined ? contactPhone : accelerator.contactPhone,
      isActive: isActive !== undefined ? isActive : accelerator.isActive,
      additionalData: additionalData || accelerator.additionalData,
    });

    // Actualizar administradores si se proporcionaron
    if (administratorIds && Array.isArray(administratorIds)) {
      const administrators = await User.findAll({
        where: {
          id: { [Op.in]: administratorIds },
          roleId: { [Op.in]: [1, 2] }, // Solo GLOBAL_ADMIN y ACCELERATOR_ADMIN pueden ser administradores
        },
      });

      await accelerator.setAdministrators(administrators);
    }

    // Obtener información actualizada con los administradores
    const updatedAccelerator = await Accelerator.findByPk(acceleratorId, {
      include: [
        {
          model: User,
          as: 'administrators',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          through: { attributes: [] },
        },
      ],
    });

    res.json({
      success: true,
      message: 'Aceleradora actualizada correctamente',
      accelerator: updatedAccelerator,
    });
  } catch (error) {
    console.error('Error al actualizar aceleradora:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar aceleradora',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Activar una aceleradora
exports.activateAccelerator = async (req, res) => {
  try {
    const acceleratorId = req.params.id;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada',
      });
    }

    // Activar aceleradora
    await accelerator.update({ isActive: true });

    res.json({
      success: true,
      message: 'Aceleradora activada correctamente',
    });
  } catch (error) {
    console.error('Error al activar aceleradora:', error);
    res.status(500).json({
      success: false,
      message: 'Error al activar aceleradora',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Desactivar una aceleradora
exports.deactivateAccelerator = async (req, res) => {
  try {
    const acceleratorId = req.params.id;

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada',
      });
    }

    // Desactivar aceleradora
    await accelerator.update({ isActive: false });

    res.json({
      success: true,
      message: 'Aceleradora desactivada correctamente',
    });
  } catch (error) {
    console.error('Error al desactivar aceleradora:', error);
    res.status(500).json({
      success: false,
      message: 'Error al desactivar aceleradora',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Asignar administradores a una aceleradora
exports.assignAdministrators = async (req, res) => {
  try {
    const acceleratorId = req.params.id;
    const { administratorIds } = req.body;

    if (!administratorIds || !Array.isArray(administratorIds)) {
      return res.status(400).json({
        success: false,
        message: 'Se requiere un array de IDs de administradores',
      });
    }

    // Verificar si la aceleradora existe
    const accelerator = await Accelerator.findByPk(acceleratorId);
    if (!accelerator) {
      return res.status(404).json({
        success: false,
        message: 'Aceleradora no encontrada',
      });
    }

    // Buscar usuarios que sean administradores
    const administrators = await User.findAll({
      where: {
        id: { [Op.in]: administratorIds },
        roleId: { [Op.in]: [1, 2] }, // Solo GLOBAL_ADMIN y ACCELERATOR_ADMIN pueden ser administradores
      },
    });

    if (administrators.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No se encontraron usuarios válidos para asignar como administradores',
      });
    }

    // Asignar administradores
    await accelerator.setAdministrators(administrators);

    res.json({
      success: true,
      message: 'Administradores asignados correctamente',
      assignedCount: administrators.length,
    });
  } catch (error) {
    console.error('Error al asignar administradores:', error);
    res.status(500).json({
      success: false,
      message: 'Error al asignar administradores',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
