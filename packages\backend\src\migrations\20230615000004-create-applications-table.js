'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Applications', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      formId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Forms',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      applicantId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      acceleratorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Accelerators',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      funnelStageId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'FunnelStages',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      responses: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'pending',
      },
      score: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      internalNotes: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      feedback: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      submittedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      statusUpdatedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Añadir índices para mejorar el rendimiento
    await queryInterface.addIndex('Applications', ['formId']);
    await queryInterface.addIndex('Applications', ['applicantId']);
    await queryInterface.addIndex('Applications', ['acceleratorId']);
    await queryInterface.addIndex('Applications', ['funnelStageId']);
    await queryInterface.addIndex('Applications', ['status']);
    await queryInterface.addIndex('Applications', ['submittedAt']);
    await queryInterface.addIndex('Applications', ['isActive']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Applications');
  }
};
