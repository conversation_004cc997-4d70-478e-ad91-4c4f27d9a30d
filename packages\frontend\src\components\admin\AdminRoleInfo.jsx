import React from 'react';
import useAuth from '../../hooks/useAuth';

const AdminRoleInfo = () => {
  const { currentUser } = useAuth();

  // Obtener el rol desde localStorage como respaldo
  const userFromStorage = JSON.parse(localStorage.getItem('user') || '{}');
  const roleFromStorage = userFromStorage?.role;

  // Verificar si el usuario es administrador global
  const isGlobalAdmin = currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN';

  if (!isGlobalAdmin) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p className="font-bold">Error de permisos</p>
        <p>No tienes permisos de administrador global para acceder a esta sección.</p>
      </div>
    );
  }

  // Si el usuario es administrador global, no mostrar nada
  return null;
};

export default AdminRoleInfo;
