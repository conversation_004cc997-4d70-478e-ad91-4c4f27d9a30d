import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Alert from '../../components/common/Alert';
import Spinner from '../../components/common/Spinner';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import UserForm from '../../components/admin/UserForm';
import UserService from '../../services/user.service';
import { FaSearch, FaFilter, FaSort, FaEdit, FaTrash, FaUserPlus, FaUserCheck, FaUserTimes } from 'react-icons/fa';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    roleId: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'DESC',
  });
  const [roles, setRoles] = useState([]);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [actionType, setActionType] = useState('');

  // Cargar usuarios y roles
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Cargar usuarios
        const response = await UserService.getAllUsers(currentPage, 10, filters);
        setUsers(response.users);
        setTotalPages(response.totalPages);

        // Cargar roles
        const rolesResponse = await UserService.getAllRoles();
        setRoles(rolesResponse.roles);
      } catch (err) {
        setError('Error al cargar datos: ' + (err.message || 'Error desconocido'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currentPage, filters]);

  // Cambiar página
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Manejar cambios en los filtros
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Resetear a la primera página al cambiar filtros
  };

  // Manejar búsqueda
  const handleSearch = (e) => {
    e.preventDefault();
    // La búsqueda ya se maneja con el cambio en los filtros
  };

  // Manejar ordenamiento
  const handleSort = (field) => {
    setFilters((prev) => ({
      ...prev,
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'ASC' ? 'DESC' : 'ASC',
    }));
  };

  // Abrir modal para crear usuario
  const handleCreateUser = () => {
    setCurrentUser(null);
    setShowUserModal(true);
  };

  // Abrir modal para editar usuario
  const handleEditUser = (user) => {
    setCurrentUser(user);
    setShowUserModal(true);
  };

  // Confirmar desactivación de usuario
  const handleConfirmDeactivate = (user) => {
    setCurrentUser(user);
    setActionType('deactivate');
    setShowConfirmDialog(true);
  };

  // Confirmar activación de usuario
  const handleConfirmActivate = (user) => {
    setCurrentUser(user);
    setActionType('activate');
    setShowConfirmDialog(true);
  };

  // Ejecutar acción confirmada
  const handleConfirmAction = async () => {
    try {
      setLoading(true);

      if (actionType === 'deactivate') {
        await UserService.deactivateUser(currentUser.id);
        setSuccess(`Usuario ${currentUser.firstName} ${currentUser.lastName} desactivado correctamente`);
      } else if (actionType === 'activate') {
        await UserService.activateUser(currentUser.id);
        setSuccess(`Usuario ${currentUser.firstName} ${currentUser.lastName} activado correctamente`);
      }

      // Recargar usuarios
      const response = await UserService.getAllUsers(currentPage, 10, filters);
      setUsers(response.users);
      setTotalPages(response.totalPages);

      setShowConfirmDialog(false);
      setCurrentUser(null);
      setActionType('');
    } catch (err) {
      setError('Error al procesar la acción: ' + (err.message || 'Error desconocido'));
    } finally {
      setLoading(false);
    }
  };

  // Manejar envío del formulario de usuario
  const handleUserFormSubmit = async (userData) => {
    try {
      setLoading(true);

      if (currentUser) {
        // Actualizar usuario existente
        await UserService.updateUser(currentUser.id, userData);
        setSuccess(`Usuario ${userData.firstName} ${userData.lastName} actualizado correctamente`);
      } else {
        // Crear nuevo usuario
        await UserService.createUser(userData);
        setSuccess(`Usuario ${userData.firstName} ${userData.lastName} creado correctamente`);
      }

      // Recargar usuarios
      const response = await UserService.getAllUsers(currentPage, 10, filters);
      setUsers(response.users);
      setTotalPages(response.totalPages);

      setShowUserModal(false);
      setCurrentUser(null);
    } catch (err) {
      setError('Error al guardar usuario: ' + (err.message || 'Error desconocido'));
      return Promise.reject(err);
    } finally {
      setLoading(false);
    }
  };

  // Renderizar estado del usuario
  const renderUserStatus = (user) => {
    if (user.isActive) {
      return (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
          Activo
        </span>
      );
    } else {
      return (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
          Inactivo
        </span>
      );
    }
  };

  // Renderizar nombre del rol
  const getRoleName = (roleId) => {
    const role = roles.find((r) => r.id === roleId);
    return role ? (role.description || role.name) : 'Sin rol';
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold">Gestión de Usuarios</h1>
        <Button variant="primary" onClick={handleCreateUser}>
          <FaUserPlus className="mr-2" /> Nuevo Usuario
        </Button>
      </div>

      {error && (
        <Alert
          type="error"
          message={error}
          onClose={() => setError(null)}
        />
      )}

      {success && (
        <Alert
          type="success"
          message={success}
          onClose={() => setSuccess(null)}
        />
      )}

      <Card>
        {/* Filtros y búsqueda */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              <form onSubmit={handleSearch} className="flex">
                <input
                  type="text"
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                  placeholder="Buscar por nombre, apellido o email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  <FaSearch />
                </button>
              </form>
            </div>

            <div className="flex gap-2">
              <select
                name="roleId"
                value={filters.roleId}
                onChange={handleFilterChange}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Todos los roles</option>
                {roles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.description || role.name}
                  </option>
                ))}
              </select>

              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>
          </div>
        </div>

        {/* Tabla de usuarios */}
        {loading && users.length === 0 ? (
          <div className="py-8">
            <Spinner className="mx-auto" />
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('firstName')}
                    >
                      <div className="flex items-center">
                        Nombre
                        {filters.sortBy === 'firstName' && (
                          <FaSort className="ml-1" />
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('email')}
                    >
                      <div className="flex items-center">
                        Email
                        {filters.sortBy === 'email' && (
                          <FaSort className="ml-1" />
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rol
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Estado
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('createdAt')}
                    >
                      <div className="flex items-center">
                        Fecha Registro
                        {filters.sortBy === 'createdAt' && (
                          <FaSort className="ml-1" />
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Acciones
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.length === 0 ? (
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap" colSpan="6">
                        <p className="text-gray-500 text-center">No hay usuarios que coincidan con los filtros</p>
                      </td>
                    </tr>
                  ) : (
                    users.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                              {user.firstName.charAt(0)}
                              {user.lastName.charAt(0)}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.firstName} {user.lastName}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{user.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {getRoleName(user.roleId)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {renderUserStatus(user)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              className="text-blue-600 hover:text-blue-900"
                              onClick={() => handleEditUser(user)}
                              title="Editar"
                            >
                              <FaEdit />
                            </button>

                            {user.isActive ? (
                              <button
                                className="text-red-600 hover:text-red-900"
                                onClick={() => handleConfirmDeactivate(user)}
                                title="Desactivar"
                                disabled={user.email === '<EMAIL>'}
                              >
                                <FaUserTimes />
                              </button>
                            ) : (
                              <button
                                className="text-green-600 hover:text-green-900"
                                onClick={() => handleConfirmActivate(user)}
                                title="Activar"
                              >
                                <FaUserCheck />
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>

            {/* Paginación */}
            {totalPages > 1 && (
              <div className="px-6 py-4 flex justify-center">
                <nav className="flex items-center">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === 1
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Anterior
                  </button>

                  {[...Array(totalPages).keys()].map((page) => (
                    <button
                      key={page + 1}
                      onClick={() => handlePageChange(page + 1)}
                      className={`px-3 py-1 rounded-md ${
                        currentPage === page + 1
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {page + 1}
                    </button>
                  ))}

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === totalPages
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    Siguiente
                  </button>
                </nav>
              </div>
            )}
          </>
        )}
      </Card>

      {/* Modal para crear/editar usuario */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title={currentUser ? 'Editar Usuario' : 'Crear Nuevo Usuario'}
        size="lg"
      >
        <UserForm
          user={currentUser}
          onSubmit={handleUserFormSubmit}
          onCancel={() => setShowUserModal(false)}
        />
      </Modal>

      {/* Diálogo de confirmación */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleConfirmAction}
        title={actionType === 'deactivate' ? 'Desactivar Usuario' : 'Activar Usuario'}
        message={
          actionType === 'deactivate'
            ? `¿Estás seguro de que deseas desactivar al usuario ${currentUser?.firstName} ${currentUser?.lastName}?`
            : `¿Estás seguro de que deseas activar al usuario ${currentUser?.firstName} ${currentUser?.lastName}?`
        }
        confirmText={actionType === 'deactivate' ? 'Desactivar' : 'Activar'}
        confirmVariant={actionType === 'deactivate' ? 'danger' : 'success'}
        isLoading={loading}
      />
    </AdminLayout>
  );
};

export default UserManagement;
