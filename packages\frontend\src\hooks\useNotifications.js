import { useState, useEffect, useCallback } from 'react';
import useAuth from '../hooks/useAuth';
import sessionService from '../services/sessionService';
import taskService from '../services/taskService';

export const useNotifications = () => {
  const { currentUser } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  const loadNotifications = useCallback(async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const notificationsList = [];
      const now = new Date();

      // Obtener sesiones próximas (próximas 24 horas)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const sessionsResponse = await sessionService.getAllSessions({ limit: 50 });
      const sessions = sessionsResponse.sessions || [];

      // Sesiones próximas para el usuario
      const upcomingSessions = sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return session.status === 'CONFIRMED' &&
               sessionDate > now &&
               sessionDate <= tomorrow &&
               (session.mentorId === currentUser.id || session.entrepreneurId === currentUser.id);
      });

      upcomingSessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const hoursUntil = Math.ceil((sessionDate - now) / (1000 * 60 * 60));

        notificationsList.push({
          id: `session-${session.id}`,
          type: 'session',
          title: 'Sesión próxima',
          message: `Tienes una sesión "${session.title}" en ${hoursUntil} hora(s)`,
          time: session.startTime,
          priority: hoursUntil <= 2 ? 'high' : 'medium',
          data: session
        });
      });

      // Obtener tareas del usuario
      const tasksResponse = await taskService.getAllTasks({ limit: 50 });
      const tasks = tasksResponse.tasks || [];

      const userTasks = tasks.filter(task =>
        task.assignedToId === currentUser.id && task.status !== 'COMPLETED'
      );

      userTasks.forEach(task => {
        const dueDate = new Date(task.dueDate);
        const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));

        if (daysUntilDue < 0) {
          // Tarea vencida
          notificationsList.push({
            id: `task-overdue-${task.id}`,
            type: 'task-overdue',
            title: 'Tarea vencida',
            message: `La tarea "${task.title}" venció hace ${Math.abs(daysUntilDue)} día(s)`,
            time: task.dueDate,
            priority: 'high',
            data: task
          });
        } else if (daysUntilDue <= 2) {
          // Tarea próxima a vencer
          notificationsList.push({
            id: `task-due-${task.id}`,
            type: 'task-due',
            title: 'Tarea próxima a vencer',
            message: `La tarea "${task.title}" vence en ${daysUntilDue} día(s)`,
            time: task.dueDate,
            priority: daysUntilDue === 0 ? 'high' : 'medium',
            data: task
          });
        }
      });

      // Sesiones pendientes de confirmación (solo para mentores y admins)
      if (['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN', 'MENTOR'].includes(currentUser.role)) {
        const pendingSessions = sessions.filter(session =>
          session.status === 'PENDING' &&
          (session.mentorId === currentUser.id || ['GLOBAL_ADMIN', 'ACCELERATOR_ADMIN'].includes(currentUser.role))
        );

        pendingSessions.forEach(session => {
          notificationsList.push({
            id: `session-pending-${session.id}`,
            type: 'session-pending',
            title: 'Sesión pendiente de confirmación',
            message: `La sesión "${session.title}" requiere confirmación`,
            time: session.createdAt,
            priority: 'medium',
            data: session
          });
        });
      }

      // Ordenar por prioridad y fecha
      notificationsList.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return new Date(a.time) - new Date(b.time);
      });

      setNotifications(notificationsList);
      setUnreadCount(notificationsList.length);
    } catch (error) {
      console.error('Error al cargar notificaciones:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  const markAsRead = useCallback((notificationId) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
    setUnreadCount(prev => Math.max(0, prev - 1));
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications([]);
    setUnreadCount(0);
  }, []);

  const addNotification = useCallback((notification) => {
    const newNotification = {
      id: `custom-${Date.now()}`,
      time: new Date().toISOString(),
      priority: 'medium',
      ...notification
    };

    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);
  }, []);

  // Cargar notificaciones al montar el componente
  useEffect(() => {
    loadNotifications();
  }, [loadNotifications]);

  // Actualizar notificaciones cada 5 minutos
  useEffect(() => {
    const interval = setInterval(loadNotifications, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [loadNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    addNotification
  };
};

export default useNotifications;
