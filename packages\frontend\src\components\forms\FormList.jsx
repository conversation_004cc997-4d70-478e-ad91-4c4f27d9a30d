import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiEdit, <PERSON>Eye, FiTrash2, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';
import Button from '../common/Button';
import Card from '../common/Card';
import Pagination from '../common/Pagination';
import LoadingSpinner from '../common/LoadingSpinner';
import EmptyState from '../common/EmptyState';
import api from '../../services/api';
import useAuth from '../../hooks/useAuth';

/**
 * Componente para listar formularios
 */
const FormList = ({ showNotification }) => {
  const [forms, setForms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    acceleratorId: '',
    isPublished: '',
    isActive: '',
    formType: ''
  });
  const { currentUser } = useAuth();

  // Cargar formularios al montar el componente o cuando cambian los filtros o la página
  useEffect(() => {
    const fetchForms = async () => {
      try {
        setLoading(true);

        // Construir parámetros de consulta
        const params = new URLSearchParams();
        params.append('page', currentPage);
        params.append('limit', 10);

        // Añadir filtros si están definidos
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== '') {
            params.append(key, value);
          }
        });

        const response = await api.get(`/forms?${params.toString()}`);

        if (response.data.success) {
          setForms(response.data.forms);
          setTotalPages(response.data.totalPages);
        } else {
          showNotification('error', 'Error al cargar formularios');
        }
      } catch (error) {
        console.error('Error al cargar formularios:', error);
        showNotification('error', 'Error al cargar formularios. Por favor, intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchForms();
  }, [currentPage, filters, showNotification]);

  // Manejar cambio de filtros
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Resetear a la primera página al cambiar filtros
  };

  // Manejar cambio de página
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Manejar eliminación de formulario
  const handleDeleteForm = async (id) => {
    if (window.confirm('¿Está seguro de que desea eliminar este formulario? Esta acción no se puede deshacer.')) {
      try {
        const response = await api.delete(`/forms/${id}`);

        if (response.data.success) {
          showNotification('success', 'Formulario eliminado correctamente');
          // Actualizar la lista de formularios
          setForms(forms.filter(form => form.id !== id));
        } else {
          showNotification('error', 'Error al eliminar formulario');
        }
      } catch (error) {
        console.error('Error al eliminar formulario:', error);
        showNotification('error', 'Error al eliminar formulario. Por favor, intente nuevamente.');
      }
    }
  };

  // Manejar publicación/despublicación de formulario
  const handleTogglePublish = async (id, isCurrentlyPublished) => {
    try {
      const response = await api.put(`/forms/${id}`, {
        isPublished: !isCurrentlyPublished
      });

      if (response.data.success) {
        showNotification('success', `Formulario ${!isCurrentlyPublished ? 'publicado' : 'despublicado'} correctamente`);
        // Actualizar el estado del formulario en la lista
        setForms(forms.map(form =>
          form.id === id ? { ...form, isPublished: !isCurrentlyPublished } : form
        ));
      } else {
        showNotification('error', `Error al ${!isCurrentlyPublished ? 'publicar' : 'despublicar'} formulario`);
      }
    } catch (error) {
      console.error(`Error al ${!isCurrentlyPublished ? 'publicar' : 'despublicar'} formulario:`, error);
      showNotification('error', `Error al ${!isCurrentlyPublished ? 'publicar' : 'despublicar'} formulario. Por favor, intente nuevamente.`);
    }
  };

  // Renderizar estado de carga
  if (loading && forms.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div>
      {/* Filtros */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipo de Formulario
            </label>
            <select
              name="formType"
              value={filters.formType}
              onChange={handleFilterChange}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
            >
              <option value="">Todos</option>
              <option value="application">Postulación</option>
              <option value="evaluation">Evaluación</option>
              <option value="feedback">Feedback</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Estado de Publicación
            </label>
            <select
              name="isPublished"
              value={filters.isPublished}
              onChange={handleFilterChange}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
            >
              <option value="">Todos</option>
              <option value="true">Publicados</option>
              <option value="false">No Publicados</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Estado
            </label>
            <select
              name="isActive"
              value={filters.isActive}
              onChange={handleFilterChange}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
            >
              <option value="">Todos</option>
              <option value="true">Activos</option>
              <option value="false">Inactivos</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Aceleradora
            </label>
            <select
              name="acceleratorId"
              value={filters.acceleratorId}
              onChange={handleFilterChange}
              className="w-full border border-gray-300 rounded-md shadow-sm p-2"
            >
              <option value="">Todas</option>
              {/* Aquí se cargarían dinámicamente las aceleradoras */}
            </select>
          </div>
        </div>
      </div>

      {/* Lista de formularios */}
      {forms.length === 0 ? (
        <EmptyState
          title="No hay formularios"
          description="No se encontraron formularios con los filtros seleccionados."
          actionText="Crear Formulario"
          actionLink="/admin/forms/new"
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {forms.map(form => (
            <Card key={form.id} className="flex flex-col">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold">{form.title}</h3>
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleTogglePublish(form.id, form.isPublished)}
                    className={`p-1 rounded-full ${
                      form.isPublished
                        ? 'bg-green-100 text-green-600 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                    title={form.isPublished ? 'Despublicar' : 'Publicar'}
                  >
                    {form.isPublished ? <FiCheck /> : <FiX />}
                  </button>
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                {form.description || 'Sin descripción'}
              </p>

              <div className="flex flex-wrap gap-2 mb-3">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  form.formType === 'application' ? 'bg-blue-100 text-blue-800' :
                  form.formType === 'evaluation' ? 'bg-purple-100 text-purple-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {form.formType === 'application' ? 'Postulación' :
                   form.formType === 'evaluation' ? 'Evaluación' : 'Feedback'}
                </span>

                <span className={`text-xs px-2 py-1 rounded-full ${
                  form.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {form.isPublished ? 'Publicado' : 'Borrador'}
                </span>
              </div>

              <div className="text-xs text-gray-500 mb-4">
                <p>Creado por: {form.creator?.firstName} {form.creator?.lastName}</p>
                <p>Aceleradora: {form.accelerator?.name}</p>
              </div>

              <div className="mt-auto flex justify-between">
                <Link to={`/admin/forms/${form.id}`}>
                  <Button variant="outline" size="sm" className="flex items-center">
                    <FiEye className="mr-1" /> Ver
                  </Button>
                </Link>

                <div className="flex space-x-2">
                  <Link to={`/admin/forms/${form.id}/edit`}>
                    <Button variant="outline" size="sm" className="flex items-center">
                      <FiEdit className="mr-1" /> Editar
                    </Button>
                  </Link>

                  <Button
                    variant="danger"
                    size="sm"
                    className="flex items-center"
                    onClick={() => handleDeleteForm(form.id)}
                  >
                    <FiTrash2 className="mr-1" /> Eliminar
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default FormList;
