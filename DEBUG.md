# Sistema de Depuración de Bumeran

Este documento describe el sistema de depuración (debug) implementado en la plataforma Bumeran.

## Configuración del Modo Debug

### Frontend

El frontend tiene un sistema de depuración que se puede configurar desde la interfaz de usuario:

1. Accede a tu perfil de usuario haciendo clic en tu nombre en la barra de navegación y seleccionando "Mi Perfil"
2. En la sección "Configuración", encontrarás la opción "Activar modo de depuración"
3. Al activar esta opción, se habilitarán las herramientas de depuración y aparecerá un indicador en la parte superior de la página
4. También puedes activar la opción "Mostrar mensajes de depuración en la consola" para ver información detallada en la consola del navegador

### Backend

El backend tiene un sistema de depuración que se puede configurar mediante variables de entorno o scripts:

1. **Usando scripts**:
   - `npm run debug:toggle` - Alterna el modo debug (activar/desactivar)
   - `npm run debug:on` - Inicia el servidor con el modo debug activado
   - `npm run debug:off` - Inicia el servidor con el modo debug desactivado
   - `npm run dev:debug` - Inicia tanto el frontend como el backend con el modo debug activado

2. **Usando variables de entorno**:
   - Edita el archivo `.env` y establece `DEBUG_MODE=true` para activar el modo debug
   - Establece `DEBUG_MODE=false` para desactivar el modo debug

## Herramientas de Depuración

### Frontend

1. **Barra de indicación**: Cuando el modo debug está activado, aparece una barra amarilla en la parte superior de la página
2. **Página de herramientas**: Accesible desde la barra de indicación o desde el menú de usuario
3. **Componentes de depuración**:
   - `AuthDebug`: Muestra información sobre la autenticación actual
   - `TokenDebug`: Muestra información sobre el token JWT
   - `RoleDebug`: Permite verificar el rol de un usuario en la base de datos
   - `LocalStorageDebug`: Muestra el contenido del localStorage
   - `ForceLogout`: Permite forzar el cierre de sesión y limpiar el localStorage

### Backend

1. **Logs detallados**: Cuando el modo debug está activado, se muestran logs detallados en la consola
2. **Middleware de depuración**: Registra información sobre cada solicitud HTTP
3. **Consultas SQL**: Se muestran las consultas SQL ejecutadas por Sequelize
4. **Utilidades de depuración**:
   - `debugLog`: Función para imprimir mensajes de depuración
   - `debugError`: Función para imprimir errores de depuración
   - `debugMiddleware`: Middleware para registrar información sobre las solicitudes HTTP

## Uso de las Funciones de Depuración

### Frontend

```javascript
import { debugLog } from '../contexts/DebugContext';

// Imprimir un mensaje de depuración
debugLog('Mensaje de depuración', objeto, otraVariable);
```

### Backend

```javascript
const { debugLog, debugError } = require('./utils/debug');

// Imprimir un mensaje de depuración
debugLog('Mensaje de depuración', objeto, otraVariable);

// Imprimir un error de depuración
debugError('Error en la función X', error);
```

## Recomendaciones

1. **No dejes el modo debug activado en producción**: El modo debug puede exponer información sensible y afectar el rendimiento
2. **Usa las funciones de depuración en lugar de console.log**: Esto permite controlar cuándo se muestran los mensajes
3. **Documenta los mensajes de depuración**: Incluye información contextual en los mensajes para facilitar la depuración
4. **Limpia los mensajes de depuración innecesarios**: Elimina los mensajes que ya no sean útiles
