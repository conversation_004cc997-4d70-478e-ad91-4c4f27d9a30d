import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';
import useScrollToSection from '../../hooks/useScrollToSection';
import BumeranLogo from '../common/BumeranLogo';
import { useDebug, debugLog } from '../../contexts/DebugContext';

const Navbar = () => {
  const { currentUser, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { scrollToSection } = useScrollToSection();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAdminMenuOpen, setIsAdminMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const profileMenuRef = useRef(null);
  const profileButtonRef = useRef(null);
  const adminMenuRef = useRef(null);
  const adminButtonRef = useRef(null);
  const { debugMode, toggleDebugMode } = useDebug();

  // Debug - Mostrar información del usuario en la consola usando la función debugLog
  debugLog('Navbar - currentUser:', currentUser);
  debugLog('Navbar - isAuthenticated:', isAuthenticated);

  // Verificar el rol del usuario
  const isGlobalAdmin = currentUser?.role === 'GLOBAL_ADMIN';
  debugLog('Navbar - isGlobalAdmin:', isGlobalAdmin);
  debugLog('Navbar - role from localStorage:', JSON.parse(localStorage.getItem('user') || '{}')?.role);

  // Forzar la verificación del rol desde localStorage si no está presente en currentUser
  const userFromStorage = JSON.parse(localStorage.getItem('user') || '{}');
  const roleFromStorage = userFromStorage?.role;

  // Si el usuario está autenticado pero no tiene rol en currentUser, intentar obtenerlo de localStorage
  if (isAuthenticated && !currentUser?.role && roleFromStorage) {
    debugLog('Navbar - Usando rol desde localStorage:', roleFromStorage);
    currentUser.role = roleFromStorage;
  }

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleAdminMenu = () => {
    setIsAdminMenuOpen(!isAdminMenuOpen);
  };

  const toggleProfileMenu = () => {
    setIsProfileMenuOpen(!isProfileMenuOpen);
  };

  // Función para manejar la navegación a secciones
  const handleSectionNavigation = (sectionId, event) => {
    event.preventDefault();

    // Si estamos en la página de inicio, desplazarse a la sección
    if (location.pathname === '/') {
      const success = scrollToSection(sectionId);
      if (success) {
        // Cerrar el menú móvil si está abierto
        if (isMobileMenuOpen) {
          setIsMobileMenuOpen(false);
        }
        return;
      }
    }

    // Si no estamos en la página de inicio o la sección no existe, navegar a la página de inicio con el hash
    navigate(`/#${sectionId}`);

    // Cerrar el menú móvil si está abierto
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  };

  // Cerrar los menús cuando se hace clic fuera de ellos
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Cerrar menú de perfil
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target) &&
        profileButtonRef.current &&
        !profileButtonRef.current.contains(event.target)
      ) {
        setIsProfileMenuOpen(false);
      }

      // Cerrar menú de administración
      if (
        adminMenuRef.current &&
        !adminMenuRef.current.contains(event.target) &&
        adminButtonRef.current &&
        !adminButtonRef.current.contains(event.target)
      ) {
        setIsAdminMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Determinar si estamos en la página de inicio
  const isHomePage = location.pathname === '/';

  return (
    <nav className={`bg-blue-600 text-white ${isHomePage && !isAuthenticated ? '' : 'shadow-md fixed top-0 left-0 right-0'} z-30`}>
      <div className="w-full px-4">
        <div className="flex justify-between items-center py-2">
          {/* Logo o título según la página */}
          {isAuthenticated ? (
            <div className="text-xl font-bold ml-16 md:ml-16 lg:ml-64 transition-all duration-300">
              {location.pathname.includes('/admin') ? 'Admin Dashboard' : 'Dashboard'}
            </div>
          ) : (
            <Link to="/" className="text-xl font-bold flex items-center">
              <BumeranLogo className="mr-2" size="md" />
              <span>Bumeran</span>
            </Link>
          )}

          {/* Menú para móviles */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="text-white focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>

          {/* Menú para escritorio */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/" className="hover:text-blue-200">
              Inicio
            </Link>
            <a
              href="/#features"
              className="hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('features', e)}
            >
              Características
            </a>
            <a
              href="/#pricing"
              className="hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('pricing', e)}
            >
              Precios
            </a>
            <a
              href="/#testimonials"
              className="hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('testimonials', e)}
            >
              Testimonios
            </a>
            <a
              href="/#faq"
              className="hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('faq', e)}
            >
              FAQ
            </a>

            {/* Enlace de Debug eliminado para usar el sistema centralizado */}

            {isAuthenticated ? (
              <>
                {/* Enlaces para usuarios autenticados */}
                {/* Menú Admin Global - Solo se muestra uno */}
                <div className="relative">
                  <button
                    ref={profileButtonRef}
                    onClick={toggleProfileMenu}
                    className="hover:text-blue-200 flex items-center focus:outline-none"
                  >
                    <span className="text-sm">{currentUser?.firstName}</span>
                    <svg
                      className={`ml-1 h-4 w-4 transition-transform duration-200 ${isProfileMenuOpen ? 'transform rotate-180' : ''}`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  {isProfileMenuOpen && (
                    <div
                      ref={profileMenuRef}
                      className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10"
                    >
                      <Link
                        to={(currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN') ? '/admin/dashboard' : '/dashboard'}
                        className="block px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                      {(currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN') && (
                        <>
                          {/* Admin links removed as per request */}
                        </>
                      )}
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                        onClick={() => setIsProfileMenuOpen(false)}
                      >
                        Mi Perfil y Configuración
                      </Link>
                      {debugMode && (
                        <Link
                          to="/debug/auth"
                          className="block px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          Herramientas de Debug
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          setIsProfileMenuOpen(false);
                          handleLogout();
                        }}
                        className="block w-full text-left px-4 py-2 text-gray-800 hover:bg-blue-500 hover:text-white"
                      >
                        Cerrar Sesión
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Enlaces para usuarios no autenticados */}
                <Link to="/login" className="hover:text-blue-200">
                  Iniciar Sesión
                </Link>
                <Link
                  to="/register"
                  className="bg-white text-blue-600 px-4 py-2 rounded-md hover:bg-blue-100"
                >
                  Registrarse
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Menú móvil desplegable */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-3 border-t border-blue-500">
            <Link
              to="/"
              className="block py-2 hover:text-blue-200"
              onClick={toggleMobileMenu}
            >
              Inicio
            </Link>
            <a
              href="/#features"
              className="block py-2 hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('features', e)}
            >
              Características
            </a>
            <a
              href="/#pricing"
              className="block py-2 hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('pricing', e)}
            >
              Precios
            </a>
            <a
              href="/#testimonials"
              className="block py-2 hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('testimonials', e)}
            >
              Testimonios
            </a>
            <a
              href="/#faq"
              className="block py-2 hover:text-blue-200 cursor-pointer"
              onClick={(e) => handleSectionNavigation('faq', e)}
            >
              FAQ
            </a>

            {/* Enlace de Debug eliminado para usar el sistema centralizado */}

            {isAuthenticated ? (
              <>
                {/* Enlaces para usuarios autenticados */}
                <Link
                  to={(currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN') ? '/admin/dashboard' : '/dashboard'}
                  className="block py-2 hover:text-blue-200"
                  onClick={toggleMobileMenu}
                >
                  Dashboard
                </Link>

                {/* Enlaces de administración para administradores globales */}
                {(currentUser?.role === 'GLOBAL_ADMIN' || roleFromStorage === 'GLOBAL_ADMIN') && (
                  <>
                    <div className="py-1 border-t border-blue-500"></div>
                    <div className="py-1 font-semibold">Administración</div>
                    <Link
                      to="/admin/users"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Usuarios
                    </Link>
                    <Link
                      to="/admin/accelerators"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Aceleradoras
                    </Link>
                    <Link
                      to="/admin/registration-codes"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Códigos de Registro
                    </Link>
                    <Link
                      to="/admin/forms"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Formularios
                    </Link>
                    <Link
                      to="/admin/applications"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Aplicaciones
                    </Link>
                    <Link
                      to="/admin/funnel-stages"
                      className="block py-2 hover:text-blue-200 pl-4"
                      onClick={toggleMobileMenu}
                    >
                      Etapas del Embudo
                    </Link>
                    <div className="py-1 border-t border-blue-500"></div>
                  </>
                )}

                <Link
                  to="/profile"
                  className="block py-2 hover:text-blue-200"
                  onClick={toggleMobileMenu}
                >
                  Mi Perfil
                </Link>
                {debugMode && (
                  <Link
                    to="/debug/auth"
                    className="block py-2 hover:text-blue-200"
                    onClick={toggleMobileMenu}
                  >
                    Herramientas de Debug
                  </Link>
                )}
                <button
                  onClick={handleLogout}
                  className="block w-full text-left py-2 hover:text-blue-200"
                >
                  Cerrar Sesión
                </button>
              </>
            ) : (
              <>
                {/* Enlaces para usuarios no autenticados */}
                <Link
                  to="/login"
                  className="block py-2 hover:text-blue-200"
                  onClick={toggleMobileMenu}
                >
                  Iniciar Sesión
                </Link>
                <Link
                  to="/register"
                  className="block py-2 hover:text-blue-200"
                  onClick={toggleMobileMenu}
                >
                  Registrarse
                </Link>
              </>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;

