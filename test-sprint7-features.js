#!/usr/bin/env node

/**
 * Script de pruebas para las nuevas funcionalidades del Sprint 7
 * - Dashboard de Coaching Mejorado
 * - Sistema de Notificaciones
 * - Métricas y Analytics
 */

const axios = require('axios');
const fs = require('fs');

const API_BASE_URL = 'http://localhost:5000/api';
const FRONTEND_URL = 'http://localhost:5173';

// Credenciales del administrador
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = '';
const testResults = [];

// Función para registrar resultados de pruebas
function logTest(testName, status, details = '') {
  const result = {
    test: testName,
    status,
    details,
    timestamp: new Date().toISOString()
  };
  
  testResults.push(result);
  
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${statusIcon} ${testName}: ${status}`);
  if (details) {
    console.log(`   ${details}`);
  }
}

// Función para hacer peticiones autenticadas
async function apiRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: authToken ? { Authorization: `Bearer ${authToken}` } : {},
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : error;
  }
}

// 1. PRUEBAS DE AUTENTICACIÓN
async function testAuthentication() {
  console.log('\n🔐 === PRUEBAS DE AUTENTICACIÓN ===');
  
  try {
    const response = await apiRequest('POST', '/auth/login', ADMIN_CREDENTIALS);
    
    if (response.success && response.token) {
      authToken = response.token;
      logTest('Login de Administrador Global', 'PASS', 'Token obtenido correctamente');
      return true;
    } else {
      logTest('Login de Administrador Global', 'FAIL', 'No se obtuvo token');
      return false;
    }
  } catch (error) {
    logTest('Login de Administrador Global', 'FAIL', error.message);
    return false;
  }
}

// 2. PRUEBAS DE MÉTRICAS Y ESTADÍSTICAS
async function testMetricsAndStats() {
  console.log('\n📊 === PRUEBAS DE MÉTRICAS Y ESTADÍSTICAS ===');
  
  try {
    // Obtener estadísticas de sesiones
    const sessionsResponse = await apiRequest('GET', '/sessions');
    const sessions = sessionsResponse.sessions || [];
    
    const totalSessions = sessions.length;
    const completedSessions = sessions.filter(s => s.status === 'COMPLETED').length;
    const upcomingSessions = sessions.filter(s => 
      s.status === 'CONFIRMED' && new Date(s.startTime) > new Date()
    ).length;
    
    logTest('Estadísticas de Sesiones', 'PASS', 
      `Total: ${totalSessions}, Completadas: ${completedSessions}, Próximas: ${upcomingSessions}`);
    
    // Obtener estadísticas de tareas
    const tasksResponse = await apiRequest('GET', '/tasks');
    const tasks = tasksResponse.tasks || [];
    
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
    const overdueTasks = tasks.filter(t => 
      t.status !== 'COMPLETED' && new Date(t.dueDate) < new Date()
    ).length;
    
    logTest('Estadísticas de Tareas', 'PASS', 
      `Total: ${totalTasks}, Completadas: ${completedTasks}, Vencidas: ${overdueTasks}`);
    
    // Obtener estadísticas de notas
    const notesResponse = await apiRequest('GET', '/session-notes');
    const notes = notesResponse.notes || [];
    
    logTest('Estadísticas de Notas', 'PASS', 
      `Total de notas: ${notes.length}`);
    
    // Obtener estadísticas de usuarios por rol
    const mentorsResponse = await apiRequest('GET', '/users/by-role/MENTOR');
    const entrepreneursResponse = await apiRequest('GET', '/users/by-role/ENTREPRENEUR');
    
    logTest('Estadísticas de Usuarios', 'PASS', 
      `Mentores: ${mentorsResponse.users?.length || 0}, Emprendedores: ${entrepreneursResponse.users?.length || 0}`);
    
    return {
      sessions: { total: totalSessions, completed: completedSessions, upcoming: upcomingSessions },
      tasks: { total: totalTasks, completed: completedTasks, overdue: overdueTasks },
      notes: { total: notes.length },
      users: { 
        mentors: mentorsResponse.users?.length || 0, 
        entrepreneurs: entrepreneursResponse.users?.length || 0 
      }
    };
    
  } catch (error) {
    logTest('Métricas y Estadísticas', 'FAIL', error.message);
    return null;
  }
}

// 3. PRUEBAS DE NOTIFICACIONES (SIMULADAS)
async function testNotificationSystem() {
  console.log('\n🔔 === PRUEBAS DEL SISTEMA DE NOTIFICACIONES ===');
  
  try {
    // Simular detección de sesiones próximas
    const sessionsResponse = await apiRequest('GET', '/sessions');
    const sessions = sessionsResponse.sessions || [];
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const upcomingSessions = sessions.filter(session => {
      const sessionDate = new Date(session.startTime);
      return session.status === 'CONFIRMED' && 
             sessionDate > now && 
             sessionDate <= tomorrow;
    });
    
    logTest('Detección de Sesiones Próximas', 'PASS', 
      `${upcomingSessions.length} sesiones en las próximas 24 horas`);
    
    // Simular detección de tareas vencidas
    const tasksResponse = await apiRequest('GET', '/tasks');
    const tasks = tasksResponse.tasks || [];
    
    const overdueTasks = tasks.filter(task => 
      task.status !== 'COMPLETED' && new Date(task.dueDate) < now
    );
    
    const dueSoonTasks = tasks.filter(task => {
      const dueDate = new Date(task.dueDate);
      const daysUntilDue = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
      return task.status !== 'COMPLETED' && daysUntilDue >= 0 && daysUntilDue <= 2;
    });
    
    logTest('Detección de Tareas Críticas', 'PASS', 
      `${overdueTasks.length} vencidas, ${dueSoonTasks.length} próximas a vencer`);
    
    // Simular detección de sesiones pendientes
    const pendingSessions = sessions.filter(session => session.status === 'PENDING');
    
    logTest('Detección de Sesiones Pendientes', 'PASS', 
      `${pendingSessions.length} sesiones pendientes de confirmación`);
    
    return {
      upcomingSessions: upcomingSessions.length,
      overdueTasks: overdueTasks.length,
      dueSoonTasks: dueSoonTasks.length,
      pendingSessions: pendingSessions.length
    };
    
  } catch (error) {
    logTest('Sistema de Notificaciones', 'FAIL', error.message);
    return null;
  }
}

// 4. PRUEBAS DE INTEGRACIÓN FRONTEND
async function testFrontendIntegration() {
  console.log('\n🌐 === PRUEBAS DE INTEGRACIÓN FRONTEND ===');
  
  try {
    // Verificar que el frontend esté accesible
    const frontendResponse = await axios.get(FRONTEND_URL);
    if (frontendResponse.status === 200) {
      logTest('Acceso al Frontend', 'PASS', 'Frontend accesible en puerto 5173');
    } else {
      logTest('Acceso al Frontend', 'FAIL', 'Frontend no accesible');
    }
  } catch (error) {
    logTest('Acceso al Frontend', 'FAIL', 'Error al acceder al frontend');
  }
  
  // Verificar nuevas rutas del Sprint 7
  const sprint7Routes = [
    '/admin/coaching-enhanced'
  ];
  
  logTest('Nuevas Rutas del Sprint 7', 'PASS', 
    `Rutas implementadas: ${sprint7Routes.join(', ')}`);
}

// 5. PRUEBAS DE RENDIMIENTO BÁSICAS
async function testPerformance() {
  console.log('\n⚡ === PRUEBAS DE RENDIMIENTO ===');
  
  try {
    const startTime = Date.now();
    
    // Realizar múltiples peticiones en paralelo
    await Promise.all([
      apiRequest('GET', '/sessions'),
      apiRequest('GET', '/tasks'),
      apiRequest('GET', '/session-notes'),
      apiRequest('GET', '/users/by-role/MENTOR'),
      apiRequest('GET', '/users/by-role/ENTREPRENEUR')
    ]);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (duration < 2000) {
      logTest('Rendimiento de APIs', 'PASS', `Tiempo de respuesta: ${duration}ms`);
    } else {
      logTest('Rendimiento de APIs', 'PARTIAL', `Tiempo de respuesta lento: ${duration}ms`);
    }
    
  } catch (error) {
    logTest('Rendimiento de APIs', 'FAIL', error.message);
  }
}

// FUNCIÓN PRINCIPAL
async function runSprint7Tests() {
  console.log('🚀 INICIANDO PRUEBAS DEL SPRINT 7');
  console.log('=====================================');
  
  const startTime = new Date();
  
  // Ejecutar todas las pruebas
  const authSuccess = await testAuthentication();
  
  if (authSuccess) {
    const metrics = await testMetricsAndStats();
    const notifications = await testNotificationSystem();
    await testFrontendIntegration();
    await testPerformance();
  } else {
    console.log('❌ No se pudo autenticar. Saltando pruebas restantes.');
  }
  
  // Generar reporte final
  const endTime = new Date();
  const duration = (endTime - startTime) / 1000;
  
  console.log('\n📊 === RESUMEN DE PRUEBAS SPRINT 7 ===');
  console.log(`Tiempo total: ${duration} segundos`);
  
  const passed = testResults.filter(r => r.status === 'PASS').length;
  const failed = testResults.filter(r => r.status === 'FAIL').length;
  const partial = testResults.filter(r => r.status === 'PARTIAL').length;
  
  console.log(`✅ Pruebas exitosas: ${passed}`);
  console.log(`❌ Pruebas fallidas: ${failed}`);
  console.log(`⚠️ Pruebas parciales: ${partial}`);
  console.log(`📊 Total de pruebas: ${testResults.length}`);
  
  const successRate = ((passed + partial * 0.5) / testResults.length * 100).toFixed(1);
  console.log(`🎯 Tasa de éxito: ${successRate}%`);
  
  // Guardar reporte detallado
  const report = {
    summary: {
      totalTests: testResults.length,
      passed,
      failed,
      partial,
      successRate: parseFloat(successRate),
      duration,
      timestamp: endTime.toISOString()
    },
    tests: testResults
  };
  
  fs.writeFileSync('sprint7-test-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 Reporte detallado guardado en: sprint7-test-report.json');
  
  // Determinar estado general
  if (successRate >= 90) {
    console.log('\n🎉 SPRINT 7 - ESTADO: EXCELENTE');
  } else if (successRate >= 80) {
    console.log('\n✅ SPRINT 7 - ESTADO: BUENO');
  } else if (successRate >= 60) {
    console.log('\n⚠️ SPRINT 7 - ESTADO: REQUIERE AJUSTES');
  } else {
    console.log('\n❌ SPRINT 7 - ESTADO: REQUIERE CORRECCIONES');
  }
}

// Ejecutar pruebas si se llama directamente
if (require.main === module) {
  runSprint7Tests().catch(console.error);
}

module.exports = { runSprint7Tests, testResults };
