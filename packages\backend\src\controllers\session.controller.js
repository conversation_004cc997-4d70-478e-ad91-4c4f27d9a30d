const { Session, User, Accelerator, SessionNote, Task } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Obtener todas las sesiones con filtros y paginación
exports.getAllSessions = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status;
    const mentorId = req.query.mentorId;
    const entrepreneurId = req.query.entrepreneurId;
    const acceleratorId = req.query.acceleratorId;
    const sortBy = req.query.sortBy || 'startTime';
    const sortOrder = req.query.sortOrder || 'DESC';

    // Construir condiciones de búsqueda
    const whereConditions = {};

    // Búsqueda por título o descripción
    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ];
    }

    // Filtros específicos
    if (status && status !== '') {
      whereConditions.status = status;
    }

    if (mentorId) {
      whereConditions.mentorId = mentorId;
    }

    if (entrepreneurId) {
      whereConditions.entrepreneurId = entrepreneurId;
    }

    if (acceleratorId) {
      whereConditions.acceleratorId = acceleratorId;
    }

    // Validar campo de ordenamiento
    const validSortFields = ['startTime', 'endTime', 'title', 'status', 'createdAt'];
    const orderField = validSortFields.includes(sortBy) ? sortBy : 'startTime';

    // Validar dirección de ordenamiento
    const orderDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await Session.findAndCountAll({
      where: whereConditions,
      limit,
      offset,
      order: [[orderField, orderDirection]],
      include: [
        {
          model: User,
          as: 'Mentor',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'Entrepreneur',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
        {
          model: SessionNote,
          as: 'Notes',
          attributes: ['id', 'content', 'createdAt'],
          include: [
            {
              model: User,
              as: 'Author',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          model: Task,
          as: 'Tasks',
          attributes: ['id', 'title', 'status', 'dueDate'],
        },
      ],
    });

    res.json({
      success: true,
      sessions: rows,
      totalSessions: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener sesiones:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener sesiones',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener una sesión por ID
exports.getSessionById = async (req, res) => {
  try {
    const sessionId = req.params.id;

    const session = await Session.findByPk(sessionId, {
      include: [
        {
          model: User,
          as: 'Mentor',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
        {
          model: User,
          as: 'Entrepreneur',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description'],
        },
        {
          model: SessionNote,
          as: 'Notes',
          include: [
            {
              model: User,
              as: 'Author',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
          order: [['createdAt', 'ASC']],
        },
        {
          model: Task,
          as: 'Tasks',
          include: [
            {
              model: User,
              as: 'AssignedTo',
              attributes: ['id', 'firstName', 'lastName'],
            },
            {
              model: User,
              as: 'AssignedBy',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
          order: [['createdAt', 'DESC']],
        },
      ],
    });

    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    res.json({
      success: true,
      session,
    });
  } catch (error) {
    console.error('Error al obtener sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Crear una nueva sesión
exports.createSession = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const {
      mentorId,
      entrepreneurId,
      acceleratorId,
      title,
      description,
      startTime,
      endTime,
      location,
      videoMeetingLink,
      status = 'pending_confirmation',
    } = req.body;

    // Validar que mentor y entrepreneur existan
    const mentor = await User.findByPk(mentorId);
    const entrepreneur = await User.findByPk(entrepreneurId);

    if (!mentor || !entrepreneur) {
      return res.status(400).json({
        success: false,
        message: 'Mentor o emprendedor no encontrado',
      });
    }

    // Validar que las fechas sean válidas
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (start >= end) {
      return res.status(400).json({
        success: false,
        message: 'La fecha de inicio debe ser anterior a la fecha de fin',
      });
    }

    // Verificar conflictos de horario para el mentor
    const conflictingSession = await Session.findOne({
      where: {
        mentorId,
        status: { [Op.notIn]: ['cancelled_mentor', 'cancelled_entrepreneur'] },
        [Op.or]: [
          {
            startTime: { [Op.between]: [start, end] },
          },
          {
            endTime: { [Op.between]: [start, end] },
          },
          {
            [Op.and]: [
              { startTime: { [Op.lte]: start } },
              { endTime: { [Op.gte]: end } },
            ],
          },
        ],
      },
    });

    if (conflictingSession) {
      return res.status(400).json({
        success: false,
        message: 'El mentor ya tiene una sesión programada en ese horario',
      });
    }

    // Crear nueva sesión
    const newSession = await Session.create({
      mentorId,
      entrepreneurId,
      acceleratorId,
      title,
      description,
      startTime: start,
      endTime: end,
      location,
      videoMeetingLink,
      status,
    });

    // Obtener sesión creada con sus relaciones
    const createdSession = await Session.findByPk(newSession.id, {
      include: [
        {
          model: User,
          as: 'Mentor',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'Entrepreneur',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: 'Sesión creada correctamente',
      session: createdSession,
    });
  } catch (error) {
    console.error('Error al crear sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Actualizar una sesión
exports.updateSession = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const sessionId = req.params.id;
    const {
      title,
      description,
      startTime,
      endTime,
      location,
      videoMeetingLink,
      status,
    } = req.body;

    // Verificar si la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    // Validar fechas si se proporcionan
    if (startTime && endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);

      if (start >= end) {
        return res.status(400).json({
          success: false,
          message: 'La fecha de inicio debe ser anterior a la fecha de fin',
        });
      }

      // Verificar conflictos de horario si se cambian las fechas
      const conflictingSession = await Session.findOne({
        where: {
          id: { [Op.ne]: sessionId },
          mentorId: session.mentorId,
          status: { [Op.notIn]: ['cancelled_mentor', 'cancelled_entrepreneur'] },
          [Op.or]: [
            {
              startTime: { [Op.between]: [start, end] },
            },
            {
              endTime: { [Op.between]: [start, end] },
            },
            {
              [Op.and]: [
                { startTime: { [Op.lte]: start } },
                { endTime: { [Op.gte]: end } },
              ],
            },
          ],
        },
      });

      if (conflictingSession) {
        return res.status(400).json({
          success: false,
          message: 'El mentor ya tiene una sesión programada en ese horario',
        });
      }
    }

    // Actualizar sesión
    await session.update({
      title: title || session.title,
      description: description !== undefined ? description : session.description,
      startTime: startTime ? new Date(startTime) : session.startTime,
      endTime: endTime ? new Date(endTime) : session.endTime,
      location: location !== undefined ? location : session.location,
      videoMeetingLink: videoMeetingLink !== undefined ? videoMeetingLink : session.videoMeetingLink,
      status: status || session.status,
    });

    // Obtener información actualizada con las relaciones
    const updatedSession = await Session.findByPk(sessionId, {
      include: [
        {
          model: User,
          as: 'Mentor',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'Entrepreneur',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
    });

    res.json({
      success: true,
      message: 'Sesión actualizada correctamente',
      session: updatedSession,
    });
  } catch (error) {
    console.error('Error al actualizar sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Cancelar una sesión
exports.cancelSession = async (req, res) => {
  try {
    const sessionId = req.params.id;
    const { cancelledBy, reason } = req.body;

    // Verificar si la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    // Determinar el estado de cancelación
    let newStatus;
    if (cancelledBy === 'mentor') {
      newStatus = 'cancelled_mentor';
    } else if (cancelledBy === 'entrepreneur') {
      newStatus = 'cancelled_entrepreneur';
    } else {
      newStatus = 'cancelled_mentor'; // Por defecto
    }

    // Actualizar estado de la sesión
    await session.update({
      status: newStatus,
    });

    // Agregar nota de cancelación si se proporciona razón
    if (reason) {
      await SessionNote.create({
        sessionId: session.id,
        authorId: req.user.id,
        content: `Sesión cancelada. Razón: ${reason}`,
      });
    }

    res.json({
      success: true,
      message: 'Sesión cancelada correctamente',
    });
  } catch (error) {
    console.error('Error al cancelar sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al cancelar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Confirmar una sesión
exports.confirmSession = async (req, res) => {
  try {
    const sessionId = req.params.id;

    // Verificar si la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    // Actualizar estado a confirmado
    await session.update({
      status: 'scheduled',
    });

    res.json({
      success: true,
      message: 'Sesión confirmada correctamente',
    });
  } catch (error) {
    console.error('Error al confirmar sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al confirmar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Marcar sesión como completada
exports.completeSession = async (req, res) => {
  try {
    const sessionId = req.params.id;

    // Verificar si la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    // Actualizar estado a completado
    await session.update({
      status: 'completed',
    });

    res.json({
      success: true,
      message: 'Sesión marcada como completada',
    });
  } catch (error) {
    console.error('Error al completar sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al completar sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
