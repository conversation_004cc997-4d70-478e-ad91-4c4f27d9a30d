import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, FiFilter, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import Card from '../common/Card';
import Button from '../common/Button';
import Pagination from '../common/Pagination';
import EmptyState from '../common/EmptyState';
import LoadingSpinner from '../common/LoadingSpinner';
import api from '../../services/api';
import useAuth from '../../hooks/useAuth';

/**
 * Componente para listar aplicaciones
 */
const ApplicationList = ({ showNotification }) => {
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    acceleratorId: '',
    formId: '',
    status: '',
    funnelStageId: ''
  });
  const [accelerators, setAccelerators] = useState([]);
  const [forms, setForms] = useState([]);
  const [stages, setStages] = useState([]);
  const { currentUser } = useAuth();

  // Cargar aceleradoras disponibles
  useEffect(() => {
    const fetchAccelerators = async () => {
      try {
        const response = await api.get('/accelerators');
        if (response.data.success) {
          setAccelerators(response.data.accelerators);

          // Si el usuario es administrador de aceleradora, filtrar por su aceleradora por defecto
          if (currentUser.role === 'ACCELERATOR_ADMIN' && response.data.accelerators.length > 0) {
            const userAccelerator = response.data.accelerators.find(
              acc => acc.administrators.some(admin => admin.id === currentUser.id)
            );

            if (userAccelerator) {
              setFilters(prev => ({ ...prev, acceleratorId: userAccelerator.id }));
              // Cargar etapas para esta aceleradora
              fetchStages(userAccelerator.id);
            }
          }
        }
      } catch (error) {
        console.error('Error al cargar aceleradoras:', error);
        showNotification('error', 'Error al cargar aceleradoras. Por favor, intente nuevamente.');
      }
    };

    fetchAccelerators();
  }, [currentUser, showNotification]);

  // Cargar formularios cuando cambia la aceleradora seleccionada
  useEffect(() => {
    const fetchForms = async () => {
      if (!filters.acceleratorId) {
        setForms([]);
        return;
      }

      try {
        const response = await api.get(`/forms?acceleratorId=${filters.acceleratorId}&isPublished=true&isActive=true`);
        if (response.data.success) {
          setForms(response.data.forms);
        }
      } catch (error) {
        console.error('Error al cargar formularios:', error);
        showNotification('error', 'Error al cargar formularios. Por favor, intente nuevamente.');
      }
    };

    fetchForms();
  }, [filters.acceleratorId, showNotification]);

  // Cargar etapas del embudo
  const fetchStages = async (acceleratorId) => {
    if (!acceleratorId) {
      setStages([]);
      return;
    }

    try {
      const response = await api.get(`/funnel-stages/accelerator/${acceleratorId}`);
      if (response.data.success) {
        setStages(response.data.stages);
      }
    } catch (error) {
      console.error('Error al cargar etapas del embudo:', error);
      showNotification('error', 'Error al cargar etapas del embudo. Por favor, intente nuevamente.');
    }
  };

  // Cargar aplicaciones
  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setLoading(true);

        // Construir parámetros de consulta
        const params = new URLSearchParams();
        params.append('page', currentPage);
        params.append('limit', 10);

        // Añadir filtros si están definidos
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== '') {
            params.append(key, value);
          }
        });

        const response = await api.get(`/applications?${params.toString()}`);

        if (response.data.success) {
          setApplications(response.data.applications);
          setTotalPages(response.data.totalPages);
        } else {
          showNotification('error', 'Error al cargar aplicaciones');
        }
      } catch (error) {
        console.error('Error al cargar aplicaciones:', error);
        showNotification('error', 'Error al cargar aplicaciones. Por favor, intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, [currentPage, filters, showNotification]);

  // Manejar cambio de filtros
  const handleFilterChange = (e) => {
    const { name, value } = e.target;

    // Si cambia la aceleradora, cargar las etapas correspondientes
    if (name === 'acceleratorId' && value) {
      fetchStages(value);
    }

    setFilters(prev => ({ ...prev, [name]: value }));
    setCurrentPage(1); // Resetear a la primera página al cambiar filtros
  };

  // Manejar cambio de página
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Formatear fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Obtener color de estado
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewing':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Obtener texto de estado
  const getStatusText = (status) => {
    switch (status) {
      case 'pending':
        return 'Pendiente';
      case 'reviewing':
        return 'En revisión';
      case 'approved':
        return 'Aprobado';
      case 'rejected':
        return 'Rechazado';
      default:
        return 'Desconocido';
    }
  };

  return (
    <div>
      {/* Filtros */}
      <div className="bg-white p-4 rounded-lg shadow mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Filtros</h2>
          <Button
            variant="text"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center"
          >
            <FiFilter className="mr-2" />
            {showFilters ? 'Ocultar filtros' : 'Mostrar filtros'}
            {showFilters ? <FiChevronUp className="ml-1" /> : <FiChevronDown className="ml-1" />}
          </Button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Aceleradora
              </label>
              <select
                name="acceleratorId"
                value={filters.acceleratorId}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              >
                <option value="">Todas</option>
                {accelerators.map(accelerator => (
                  <option key={accelerator.id} value={accelerator.id}>
                    {accelerator.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Formulario
              </label>
              <select
                name="formId"
                value={filters.formId}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                disabled={!filters.acceleratorId}
              >
                <option value="">Todos</option>
                {forms.map(form => (
                  <option key={form.id} value={form.id}>
                    {form.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Estado
              </label>
              <select
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
              >
                <option value="">Todos</option>
                <option value="pending">Pendiente</option>
                <option value="reviewing">En revisión</option>
                <option value="approved">Aprobado</option>
                <option value="rejected">Rechazado</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Etapa
              </label>
              <select
                name="funnelStageId"
                value={filters.funnelStageId}
                onChange={handleFilterChange}
                className="w-full border border-gray-300 rounded-md shadow-sm p-2"
                disabled={!filters.acceleratorId}
              >
                <option value="">Todas</option>
                {stages.map(stage => (
                  <option key={stage.id} value={stage.id}>
                    {stage.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Lista de aplicaciones */}
      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : applications.length === 0 ? (
        <EmptyState
          title="No hay aplicaciones"
          description="No se encontraron aplicaciones con los filtros seleccionados."
          actionText="Limpiar filtros"
          onAction={() => {
            setFilters({
              acceleratorId: '',
              formId: '',
              status: '',
              funnelStageId: ''
            });
            setCurrentPage(1);
          }}
        />
      ) : (
        <>
          <div className="grid grid-cols-1 gap-4">
            {applications.map(application => (
              <Card key={application.id} className="p-0 overflow-hidden">
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold">
                        {application.applicant.firstName} {application.applicant.lastName}
                      </h3>
                      <p className="text-gray-600">{application.applicant.email}</p>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                        {getStatusText(application.status)}
                      </span>
                      {application.stage && (
                        <span
                          className="mt-1 px-2 py-1 rounded text-xs font-medium"
                          style={{ backgroundColor: `${application.stage.color}20`, color: application.stage.color }}
                        >
                          {application.stage.name}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Formulario:</span>
                      <p className="text-gray-800">{application.Form?.title || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Aceleradora:</span>
                      <p className="text-gray-800">{application.accelerator?.name || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-500">Fecha de envío:</span>
                      <p className="text-gray-800">{formatDate(application.submittedAt)}</p>
                    </div>
                    {application.score !== null && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Puntuación:</span>
                        <p className="text-gray-800">{application.score}/10</p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="border-t border-gray-200 bg-gray-50 p-3 flex justify-end">
                  <Link to={`/admin/applications/${application.id}`}>
                    <Button variant="primary" className="flex items-center">
                      <FiEye className="mr-2" /> Ver detalles
                    </Button>
                  </Link>
                </div>
              </Card>
            ))}
          </div>

          {/* Paginación */}
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ApplicationList;
