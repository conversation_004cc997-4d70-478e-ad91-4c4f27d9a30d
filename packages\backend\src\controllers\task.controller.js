const { Task, User, Session, Accelerator } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Obtener todas las tareas con filtros y paginación
exports.getAllTasks = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status;
    const assignedToId = req.query.assignedToId;
    const assignedById = req.query.assignedById;
    const acceleratorId = req.query.acceleratorId;
    const sessionId = req.query.sessionId;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'DESC';

    // Construir condiciones de búsqueda
    const whereConditions = {};

    // Búsqueda por título o descripción
    if (search) {
      whereConditions[Op.or] = [
        { title: { [Op.like]: `%${search}%` } },
        { description: { [Op.like]: `%${search}%` } },
      ];
    }

    // Filtros específicos
    if (status && status !== '') {
      whereConditions.status = status;
    }

    if (assignedToId) {
      whereConditions.assignedToId = assignedToId;
    }

    if (assignedById) {
      whereConditions.assignedById = assignedById;
    }

    if (acceleratorId) {
      whereConditions.acceleratorId = acceleratorId;
    }

    if (sessionId) {
      whereConditions.sessionId = sessionId;
    }

    // Validar campo de ordenamiento
    const validSortFields = ['title', 'status', 'dueDate', 'createdAt'];
    const orderField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

    // Validar dirección de ordenamiento
    const orderDirection = sortOrder === 'ASC' ? 'ASC' : 'DESC';

    const { count, rows } = await Task.findAndCountAll({
      where: whereConditions,
      limit,
      offset,
      order: [[orderField, orderDirection]],
      include: [
        {
          model: User,
          as: 'AssignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'AssignedBy',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
    });

    res.json({
      success: true,
      tasks: rows,
      totalTasks: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener tareas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener tareas',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener una tarea por ID
exports.getTaskById = async (req, res) => {
  try {
    const taskId = req.params.id;

    const task = await Task.findByPk(taskId, {
      include: [
        {
          model: User,
          as: 'AssignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
        {
          model: User,
          as: 'AssignedBy',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
          include: [
            {
              model: User,
              as: 'Mentor',
              attributes: ['id', 'firstName', 'lastName'],
            },
            {
              model: User,
              as: 'Entrepreneur',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name', 'description'],
        },
      ],
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Tarea no encontrada',
      });
    }

    res.json({
      success: true,
      task,
    });
  } catch (error) {
    console.error('Error al obtener tarea:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener tarea',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Crear una nueva tarea
exports.createTask = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const {
      title,
      description,
      assignedToId,
      acceleratorId,
      sessionId,
      dueDate,
      status = 'pending',
    } = req.body;

    const assignedById = req.user.id;

    // Validar que el usuario asignado existe
    const assignedUser = await User.findByPk(assignedToId);
    if (!assignedUser) {
      return res.status(400).json({
        success: false,
        message: 'Usuario asignado no encontrado',
      });
    }

    // Validar sesión si se proporciona
    if (sessionId) {
      const session = await Session.findByPk(sessionId);
      if (!session) {
        return res.status(400).json({
          success: false,
          message: 'Sesión no encontrada',
        });
      }
    }

    // Validar aceleradora si se proporciona
    if (acceleratorId) {
      const accelerator = await Accelerator.findByPk(acceleratorId);
      if (!accelerator) {
        return res.status(400).json({
          success: false,
          message: 'Aceleradora no encontrada',
        });
      }
    }

    // Crear nueva tarea
    const newTask = await Task.create({
      title,
      description,
      assignedToId,
      assignedById,
      acceleratorId,
      sessionId,
      dueDate: dueDate ? new Date(dueDate) : null,
      status,
    });

    // Obtener tarea creada con sus relaciones
    const createdTask = await Task.findByPk(newTask.id, {
      include: [
        {
          model: User,
          as: 'AssignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'AssignedBy',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Session,
          attributes: ['id', 'title'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: 'Tarea creada correctamente',
      task: createdTask,
    });
  } catch (error) {
    console.error('Error al crear tarea:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear tarea',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Actualizar una tarea
exports.updateTask = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const taskId = req.params.id;
    const {
      title,
      description,
      assignedToId,
      dueDate,
      status,
    } = req.body;

    // Verificar si la tarea existe
    const task = await Task.findByPk(taskId);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Tarea no encontrada',
      });
    }

    // Verificar permisos (solo el asignador, el asignado, o un administrador pueden actualizar)
    const userCanUpdate =
      task.assignedById === req.user.id ||
      task.assignedToId === req.user.id ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanUpdate) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para actualizar esta tarea',
      });
    }

    // Validar usuario asignado si se cambia
    if (assignedToId && assignedToId !== task.assignedToId) {
      const assignedUser = await User.findByPk(assignedToId);
      if (!assignedUser) {
        return res.status(400).json({
          success: false,
          message: 'Usuario asignado no encontrado',
        });
      }
    }

    // Actualizar tarea
    await task.update({
      title: title || task.title,
      description: description !== undefined ? description : task.description,
      assignedToId: assignedToId || task.assignedToId,
      dueDate: dueDate ? new Date(dueDate) : task.dueDate,
      status: status || task.status,
    });

    // Obtener información actualizada con las relaciones
    const updatedTask = await Task.findByPk(taskId, {
      include: [
        {
          model: User,
          as: 'AssignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: User,
          as: 'AssignedBy',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Session,
          attributes: ['id', 'title'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
    });

    res.json({
      success: true,
      message: 'Tarea actualizada correctamente',
      task: updatedTask,
    });
  } catch (error) {
    console.error('Error al actualizar tarea:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar tarea',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Eliminar una tarea
exports.deleteTask = async (req, res) => {
  try {
    const taskId = req.params.id;

    // Verificar si la tarea existe
    const task = await Task.findByPk(taskId);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Tarea no encontrada',
      });
    }

    // Verificar permisos (solo el asignador o un administrador pueden eliminar)
    const userCanDelete =
      task.assignedById === req.user.id ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanDelete) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para eliminar esta tarea',
      });
    }

    // Eliminar tarea
    await task.destroy();

    res.json({
      success: true,
      message: 'Tarea eliminada correctamente',
    });
  } catch (error) {
    console.error('Error al eliminar tarea:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar tarea',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Marcar tarea como completada
exports.completeTask = async (req, res) => {
  try {
    const taskId = req.params.id;

    // Verificar si la tarea existe
    const task = await Task.findByPk(taskId);
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Tarea no encontrada',
      });
    }

    // Verificar permisos (el asignado, el asignador, o un administrador pueden marcar como completada)
    const userCanComplete =
      task.assignedToId === req.user.id ||
      task.assignedById === req.user.id ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanComplete) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para completar esta tarea',
      });
    }

    // Actualizar estado a completado
    await task.update({
      status: 'completed',
    });

    res.json({
      success: true,
      message: 'Tarea marcada como completada',
    });
  } catch (error) {
    console.error('Error al completar tarea:', error);
    res.status(500).json({
      success: false,
      message: 'Error al completar tarea',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener tareas asignadas a un usuario
exports.getUserTasks = async (req, res) => {
  try {
    const userId = req.params.userId || req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const status = req.query.status;

    const whereConditions = { assignedToId: userId };

    if (status && status !== '') {
      whereConditions.status = status;
    }

    const { count, rows } = await Task.findAndCountAll({
      where: whereConditions,
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'AssignedBy',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
        },
        {
          model: Accelerator,
          attributes: ['id', 'name'],
        },
      ],
      order: [['dueDate', 'ASC'], ['createdAt', 'DESC']],
    });

    res.json({
      success: true,
      tasks: rows,
      totalTasks: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener tareas del usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener tareas del usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener estadísticas de tareas
exports.getTaskStats = async (req, res) => {
  try {
    const userId = req.query.userId;
    const acceleratorId = req.query.acceleratorId;

    let whereConditions = {};

    if (userId) {
      whereConditions.assignedToId = userId;
    }

    if (acceleratorId) {
      whereConditions.acceleratorId = acceleratorId;
    }

    // Contar tareas por estado
    const stats = await Task.findAll({
      where: whereConditions,
      attributes: [
        'status',
        [Task.sequelize.fn('COUNT', Task.sequelize.col('id')), 'count'],
      ],
      group: ['status'],
    });

    // Contar tareas vencidas
    const overdueTasks = await Task.count({
      where: {
        ...whereConditions,
        dueDate: { [Op.lt]: new Date() },
        status: { [Op.notIn]: ['completed', 'archived'] },
      },
    });

    // Contar tareas próximas a vencer (próximos 7 días)
    const upcomingTasks = await Task.count({
      where: {
        ...whereConditions,
        dueDate: {
          [Op.between]: [new Date(), new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)],
        },
        status: { [Op.notIn]: ['completed', 'archived'] },
      },
    });

    const totalTasks = await Task.count({ where: whereConditions });

    res.json({
      success: true,
      stats: {
        byStatus: stats.reduce((acc, stat) => {
          acc[stat.status] = parseInt(stat.dataValues.count);
          return acc;
        }, {}),
        overdue: overdueTasks,
        upcoming: upcomingTasks,
        total: totalTasks,
      },
    });
  } catch (error) {
    console.error('Error al obtener estadísticas de tareas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener estadísticas de tareas',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
