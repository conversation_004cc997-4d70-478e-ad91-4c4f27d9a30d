const { SessionNote, Session, User } = require('../models');
const { validationResult } = require('express-validator');
const { Op } = require('sequelize');

// Obtener todas las notas de una sesión
exports.getSessionNotes = async (req, res) => {
  try {
    const sessionId = req.params.sessionId;

    // Verificar que la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    const notes = await SessionNote.findAll({
      where: { sessionId },
      include: [
        {
          model: User,
          as: 'Author',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
      ],
      order: [['createdAt', 'ASC']],
    });

    res.json({
      success: true,
      notes,
    });
  } catch (error) {
    console.error('Error al obtener notas de sesión:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener notas de sesión',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener una nota específica
exports.getNoteById = async (req, res) => {
  try {
    const noteId = req.params.id;

    const note = await SessionNote.findByPk(noteId, {
      include: [
        {
          model: User,
          as: 'Author',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePicture'],
        },
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
        },
      ],
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        message: 'Nota no encontrada',
      });
    }

    res.json({
      success: true,
      note,
    });
  } catch (error) {
    console.error('Error al obtener nota:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener nota',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Crear una nueva nota
exports.createNote = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const { sessionId, content } = req.body;
    const authorId = req.user.id;

    // Verificar que la sesión existe
    const session = await Session.findByPk(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: 'Sesión no encontrada',
      });
    }

    // Verificar que el usuario tiene permisos para agregar notas a esta sesión
    // (debe ser el mentor, el emprendedor, o un administrador)
    const userCanAddNote =
      session.mentorId === authorId ||
      session.entrepreneurId === authorId ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanAddNote) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para agregar notas a esta sesión',
      });
    }

    // Crear nueva nota
    const newNote = await SessionNote.create({
      sessionId,
      authorId,
      content,
    });

    // Obtener nota creada con sus relaciones
    const createdNote = await SessionNote.findByPk(newNote.id, {
      include: [
        {
          model: User,
          as: 'Author',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: 'Nota creada correctamente',
      note: createdNote,
    });
  } catch (error) {
    console.error('Error al crear nota:', error);
    res.status(500).json({
      success: false,
      message: 'Error al crear nota',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Actualizar una nota
exports.updateNote = async (req, res) => {
  // Validar entrada
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ success: false, errors: errors.array() });
  }

  try {
    const noteId = req.params.id;
    const { content } = req.body;

    // Verificar si la nota existe
    const note = await SessionNote.findByPk(noteId);
    if (!note) {
      return res.status(404).json({
        success: false,
        message: 'Nota no encontrada',
      });
    }

    // Verificar que el usuario puede editar esta nota
    // (solo el autor o un administrador)
    const userCanEdit =
      note.authorId === req.user.id ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanEdit) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para editar esta nota',
      });
    }

    // Actualizar nota
    await note.update({
      content: content || note.content,
    });

    // Obtener información actualizada con las relaciones
    const updatedNote = await SessionNote.findByPk(noteId, {
      include: [
        {
          model: User,
          as: 'Author',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
      ],
    });

    res.json({
      success: true,
      message: 'Nota actualizada correctamente',
      note: updatedNote,
    });
  } catch (error) {
    console.error('Error al actualizar nota:', error);
    res.status(500).json({
      success: false,
      message: 'Error al actualizar nota',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Eliminar una nota
exports.deleteNote = async (req, res) => {
  try {
    const noteId = req.params.id;

    // Verificar si la nota existe
    const note = await SessionNote.findByPk(noteId);
    if (!note) {
      return res.status(404).json({
        success: false,
        message: 'Nota no encontrada',
      });
    }

    // Verificar que el usuario puede eliminar esta nota
    // (solo el autor o un administrador)
    const userCanDelete =
      note.authorId === req.user.id ||
      req.user.role === 'GLOBAL_ADMIN' ||
      req.user.role === 'ACCELERATOR_ADMIN';

    if (!userCanDelete) {
      return res.status(403).json({
        success: false,
        message: 'No tienes permisos para eliminar esta nota',
      });
    }

    // Eliminar nota
    await note.destroy();

    res.json({
      success: true,
      message: 'Nota eliminada correctamente',
    });
  } catch (error) {
    console.error('Error al eliminar nota:', error);
    res.status(500).json({
      success: false,
      message: 'Error al eliminar nota',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener todas las notas con filtros
exports.getAllNotes = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const sessionId = req.query.sessionId || '';
    const authorId = req.query.authorId || '';
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'DESC';

    // Construir condiciones de filtro
    const whereConditions = {};

    if (search) {
      whereConditions.content = {
        [Op.iLike]: `%${search}%`
      };
    }

    if (sessionId) {
      whereConditions.sessionId = sessionId;
    }

    if (authorId) {
      whereConditions.authorId = authorId;
    }

    // Verificar permisos: solo admins pueden ver todas las notas
    if (req.user.role !== 'GLOBAL_ADMIN' && req.user.role !== 'ACCELERATOR_ADMIN') {
      // Los mentores y emprendedores solo pueden ver sus propias notas
      whereConditions.authorId = req.user.id;
    }

    const { count, rows } = await SessionNote.findAndCountAll({
      where: whereConditions,
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'Author',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
          include: [
            {
              model: User,
              as: 'Mentor',
              attributes: ['id', 'firstName', 'lastName'],
            },
            {
              model: User,
              as: 'Entrepreneur',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
      ],
      order: [[sortBy, sortOrder]],
    });

    res.json({
      success: true,
      notes: rows,
      totalNotes: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener todas las notas:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener notas',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

// Obtener todas las notas de un usuario
exports.getUserNotes = async (req, res) => {
  try {
    const userId = req.params.userId || req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    const { count, rows } = await SessionNote.findAndCountAll({
      where: { authorId: userId },
      limit,
      offset,
      include: [
        {
          model: Session,
          attributes: ['id', 'title', 'startTime', 'endTime'],
          include: [
            {
              model: User,
              as: 'Mentor',
              attributes: ['id', 'firstName', 'lastName'],
            },
            {
              model: User,
              as: 'Entrepreneur',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    res.json({
      success: true,
      notes: rows,
      totalNotes: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
    });
  } catch (error) {
    console.error('Error al obtener notas del usuario:', error);
    res.status(500).json({
      success: false,
      message: 'Error al obtener notas del usuario',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
