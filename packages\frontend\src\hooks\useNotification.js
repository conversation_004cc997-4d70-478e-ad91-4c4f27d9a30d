import { useState } from 'react';

/**
 * Hook personalizado para manejar notificaciones
 * 
 * @returns {Object} Objeto con funciones y estado para manejar notificaciones
 */
const useNotification = () => {
  const [notification, setNotification] = useState(null);

  /**
   * Muestra una notificación y la elimina después de un tiempo
   * 
   * @param {string} type - Tipo de notificación ('success', 'error', 'warning', 'info')
   * @param {string} message - Mensaje a mostrar
   * @param {number} duration - Duración en milisegundos (por defecto 5000ms)
   */
  const showNotification = (type, message, duration = 5000) => {
    setNotification({ type, message });
    
    // Limpiar la notificación después del tiempo especificado
    const timer = setTimeout(() => {
      setNotification(null);
    }, duration);
    
    // Limpiar el temporizador si el componente se desmonta
    return () => clearTimeout(timer);
  };

  /**
   * Elimina la notificación actual
   */
  const clearNotification = () => {
    setNotification(null);
  };

  return {
    notification,
    showNotification,
    clearNotification
  };
};

export default useNotification;
