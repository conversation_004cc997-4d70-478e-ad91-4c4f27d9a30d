#!/usr/bin/env node

/**
 * Script de verificación rápida del estado del sistema Bumeran
 * Ejecuta verificaciones básicas para confirmar que todo funciona
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Colores para la consola
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function checkEndpoint(method, endpoint, expectedStatus = 200, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {},
      timeout: 5000
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return response.status === expectedStatus;
  } catch (error) {
    return false;
  }
}

async function getAuthToken() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    return response.data.token;
  } catch (error) {
    return null;
  }
}

async function runHealthCheck() {
  log('🏥 Verificación Rápida del Estado del Sistema Bumeran', 'blue');
  log('=' .repeat(55), 'blue');

  const checks = [];

  // 1. Verificar que el servidor esté corriendo
  log('\n🔍 Verificando conectividad del servidor...', 'yellow');
  const serverUp = await checkEndpoint('GET', '/health', 200);
  if (!serverUp) {
    // Si no hay endpoint de health, probar con auth
    const authCheck = await checkEndpoint('POST', '/auth/login', 401);
    checks.push({ name: 'Servidor corriendo', status: authCheck });
  } else {
    checks.push({ name: 'Servidor corriendo', status: serverUp });
  }

  // 2. Verificar autenticación
  log('🔐 Verificando autenticación...', 'yellow');
  const token = await getAuthToken();
  checks.push({ name: 'Autenticación', status: !!token });

  if (token) {
    // 3. Verificar endpoints principales
    log('📡 Verificando endpoints principales...', 'yellow');
    
    const endpointChecks = [
      { name: 'Listar aceleradoras', endpoint: '/accelerators' },
      { name: 'Obtener roles', endpoint: '/users/roles' },
      { name: 'Listar formularios', endpoint: '/forms' },
      { name: 'Listar aplicaciones', endpoint: '/applications' }
    ];

    for (const check of endpointChecks) {
      const status = await checkEndpoint('GET', check.endpoint, 200, null, token);
      checks.push({ name: check.name, status });
    }

    // 4. Verificar creación de recursos (test básico)
    log('✏️  Verificando capacidad de escritura...', 'yellow');
    const canCreate = await checkEndpoint('POST', '/accelerators', 201, {
      name: 'Health Check Test',
      description: 'Test de verificación automática',
      website: 'https://test.com',
      location: 'Test City',
      industry: 'Test'
    }, token);
    checks.push({ name: 'Crear recursos', status: canCreate });
  }

  // Mostrar resultados
  log('\n📊 Resultados de la Verificación:', 'blue');
  log('-' .repeat(40), 'blue');

  let passedChecks = 0;
  for (const check of checks) {
    const status = check.status ? '✅ OK' : '❌ FAIL';
    const color = check.status ? 'green' : 'red';
    log(`${status} ${check.name}`, color);
    if (check.status) passedChecks++;
  }

  log('\n' + '=' .repeat(55), 'blue');
  
  if (passedChecks === checks.length) {
    log(`🎉 Sistema completamente operativo (${passedChecks}/${checks.length})`, 'green');
    log('✅ Todos los componentes funcionan correctamente', 'green');
  } else if (passedChecks >= checks.length * 0.8) {
    log(`⚠️  Sistema mayormente operativo (${passedChecks}/${checks.length})`, 'yellow');
    log('🔧 Algunos componentes necesitan atención', 'yellow');
  } else {
    log(`🚨 Sistema con problemas (${passedChecks}/${checks.length})`, 'red');
    log('🛠️  Se requiere intervención técnica', 'red');
  }

  // Recomendaciones
  log('\n💡 Recomendaciones:', 'blue');
  if (passedChecks === 0) {
    log('• Verificar que el servidor esté corriendo: npm run dev', 'yellow');
    log('• Verificar la configuración de la base de datos', 'yellow');
  } else if (passedChecks < checks.length) {
    log('• Revisar los logs del servidor para errores específicos', 'yellow');
    log('• Ejecutar: npm run db:fix si hay problemas de BD', 'yellow');
  } else {
    log('• Sistema funcionando correctamente', 'green');
    log('• Listo para desarrollo y pruebas', 'green');
  }

  log('\n🔗 Enlaces útiles:', 'blue');
  log('• Frontend: http://localhost:5173', 'reset');
  log('• Backend API: http://localhost:5000/api', 'reset');
  log('• Pruebas completas: node test-api.js', 'reset');

  return passedChecks === checks.length;
}

// Función para verificación silenciosa (para scripts)
async function silentHealthCheck() {
  const token = await getAuthToken();
  if (!token) return false;

  const checks = [
    checkEndpoint('GET', '/accelerators', 200, null, token),
    checkEndpoint('GET', '/users/roles', 200, null, token),
    checkEndpoint('GET', '/forms', 200, null, token),
    checkEndpoint('GET', '/applications', 200, null, token)
  ];

  const results = await Promise.all(checks);
  return results.every(result => result);
}

// Ejecutar verificación si el script se ejecuta directamente
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--silent')) {
    silentHealthCheck().then(healthy => {
      process.exit(healthy ? 0 : 1);
    });
  } else {
    runHealthCheck().catch(error => {
      log(`💥 Error durante la verificación: ${error.message}`, 'red');
      process.exit(1);
    });
  }
}

module.exports = { runHealthCheck, silentHealthCheck };
